{"name": "sayyes-ai-monorepo", "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0"}, "private": true, "scripts": {"dev": "bun --filter web dev", "build": "bun run build:agent-system && bun --filter web build", "build:agent-system": "bun --filter agent-system build", "start": "bun --filter web start", "lint": "bun --filter web lint && bun --filter agent-system lint", "test": "bun --filter web test && bun --filter agent-system test", "typecheck": "bun --filter web typecheck && bun --filter agent-system typecheck"}, "trustedDependencies": ["unrs-resolver"], "workspaces": ["apps/*"], "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint-config-next": "^15.3.3", "graceful-fs": "^4.2.11", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0"}}