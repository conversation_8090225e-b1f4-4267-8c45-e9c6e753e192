# SayYes AI Wedding Planner - DevContainer

This devcontainer provides a complete development environment for the SayYes AI Wedding Planner project.

## Features

### Base Environment

- **Node.js 20 LTS** - Latest stable Node.js runtime
- **Bun** - Fast JavaScript runtime and package manager (preferred over npm)
- **TypeScript** - Full TypeScript support with latest compiler
- **Git & GitHub CLI** - Version control and GitHub integration
- **Debian Bullseye** - Stable Linux base image

### VS Code Extensions

- **TypeScript & React Development**

  - TypeScript Next - Enhanced TypeScript support
  - React Native - React development tools
  - Auto Rename Tag - Automatically rename paired HTML/JSX tags
  - Path Intellisense - Autocomplete for file paths
  - Bun for VS Code - Bun runtime support

- **Styling & CSS**

  - Tailwind CSS IntelliSense - Tailwind CSS autocomplete and utilities
  - CSS Peek - Navigate to CSS definitions
  - HTML CSS Class Completion - CSS class autocomplete

- **Code Quality**
  - Prettier - Code formatting
  - ESLint - JavaScript/TypeScript linting
  - Format on Save enabled by default

### Port Forwarding

- **Port 3000** - Vite development server (auto-notify)
- **Port 5173** - Vite preview server (silent)

### Environment Setup

- Automatic Bun installation and `bun install` on container creation
- Environment variables mounted from `.env.local`
- Development mode configured by default

## Getting Started

### Option 1: VS Code DevContainer (Recommended)

1. **Open in DevContainer**

   - Install the "Dev Containers" extension in VS Code
   - Open the project folder
   - Click "Reopen in Container" when prompted
   - Or use Command Palette: "Dev Containers: Reopen in Container"

2. **Start Development**

   ```bash
   bun dev
   ```

   The application will be available at `http://localhost:3000`

### Option 2: Standalone Docker

If you prefer to use Docker without VS Code:

1. **Using Docker Compose (Recommended)**

   ```bash
   # From the project root
   docker-compose -f .devcontainer/docker-compose.yml up --build
   ```

2. **Using Docker directly**

   ```bash
   # Build the image
   docker build -f .devcontainer/Dockerfile -t sayyes-wedding-planner .

   # Run the container
   docker run -p 3000:3000 -p 5173:5173 \
     -v $(pwd):/workspace \
     -v /workspace/node_modules \
     --env-file .env.local \
     sayyes-wedding-planner
   ```

3. **Environment Variables**
   - Ensure your `.env.local` file is configured with required API keys
   - The file is automatically mounted into the container

## Project Structure Support

The devcontainer is optimized for this project's structure:

- React + TypeScript + Vite development
- Tailwind CSS styling
- Component-based architecture
- AI service integrations (Gemini API)
- **Bun package management** (preferred over npm)

## Package Management

This project uses **Bun** instead of npm for all package management:

```bash
# Install dependencies
bun install

# Run development server
bun dev

# Build for production
bun run build

# Preview production build
bun run preview

# Add new packages
bun add <package-name>

# Add dev dependencies
bun add -d <package-name>
```

## Docker Files

The devcontainer includes several Docker-related files:

- **`Dockerfile`** - Custom Docker image with Node.js 20, Bun, and development tools
- **`docker-compose.yml`** - Complete development environment with volume mounts
- **`../.dockerignore`** - Optimized build context exclusions

## Troubleshooting

### Port Issues

If port 3000 is already in use:

```bash
bun dev --port 3001
```

### Environment Variables

If API calls fail, verify your `.env.local` file contains:

- `GEMINI_API_KEY` - Required for AI features
- Other service keys as needed

### Container Rebuild

If you encounter issues, rebuild the container:

- **VS Code**: Command Palette: "Dev Containers: Rebuild Container"
- **Docker Compose**: `docker-compose -f .devcontainer/docker-compose.yml up --build --force-recreate`
- **Docker**: `docker build --no-cache -f .devcontainer/Dockerfile -t sayyes-wedding-planner .`

### Bun Issues

If Bun commands fail, ensure Bun is in PATH:

```bash
export PATH="$HOME/.bun/bin:$PATH"
```

## Additional Tools

The container includes:

- `bun` - Fast JavaScript runtime and package manager
- `git` - Version control
- `gh` - GitHub CLI for repository management
- `node` - Node.js runtime

## VS Code Settings

Pre-configured settings include:

- Single quotes for TypeScript
- Format on save with Prettier
- ESLint auto-fix on save
- Emmet support for TypeScript/React
- Tailwind CSS IntelliSense for TypeScript files
- Bun VS Code extension for enhanced Bun support
