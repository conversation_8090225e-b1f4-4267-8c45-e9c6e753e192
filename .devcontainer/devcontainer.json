{"name": "SayYes AI Wedding Planner", "build": {"dockerfile": "Dockerfile", "context": ".."}, "features": {"ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "ms-vscode.vscode-json", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-css-peek", "zignd.html-css-class-completion", "ms-vscode.vscode-react-native", "oven.bun-vscode"], "settings": {"typescript.preferences.quoteStyle": "single", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}}}}, "forwardPorts": [3000, 5173], "portsAttributes": {"3000": {"label": "Vite Dev Server", "onAutoForward": "notify"}, "5173": {"label": "Vite Preview", "onAutoForward": "silent"}}, "postCreateCommand": "bun install", "postStartCommand": "echo 'SayYes AI Wedding Planner DevContainer Ready! Run: bun dev'", "mounts": ["source=${localWorkspaceFolder}/.env.local,target=${containerWorkspaceFolder}/.env.local,type=bind,consistency=cached"], "remoteUser": "node", "workspaceFolder": "/workspace"}