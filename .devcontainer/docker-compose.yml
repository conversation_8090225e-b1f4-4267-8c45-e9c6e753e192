version: "3.8"

services:
  sayyes-wedding-planner:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    container_name: sayyes-wedding-planner-dev
    ports:
      - "3000:3000"
      - "5173:5173"
    volumes:
      - ..:/workspace:cached
      - node_modules:/workspace/node_modules
      - bun_cache:/home/<USER>/.bun
    environment:
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
    env_file:
      - ../.env.local
    working_dir: /workspace
    command: bun dev --host 0.0.0.0
    stdin_open: true
    tty: true
    networks:
      - sayyes-network

  # Optional: Add a database service if needed in the future
  # postgres:
  #   image: postgres:15-alpine
  #   container_name: sayyes-postgres
  #   environment:
  #     POSTGRES_DB: sayyes_wedding_planner
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: postgres
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - sayyes-network

volumes:
  node_modules:
  bun_cache:
  # postgres_data:

networks:
  sayyes-network:
    driver: bridge
