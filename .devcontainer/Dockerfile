FROM node:20-bullseye-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Bun
RUN curl -fsSL https://bun.sh/install | bash

# Set up environment
ENV PATH="/root/.bun/bin:$PATH"
ENV NODE_ENV=development

# Create app directory
WORKDIR /workspace

# Create non-root user
RUN groupadd --gid 1000 node \
    && useradd --uid 1000 --gid node --shell /bin/bash --create-home node

# Install Bun for the node user
USER node
RUN curl -fsSL https://bun.sh/install | bash
ENV PATH="/home/<USER>/.bun/bin:$PATH"

# Set working directory
WORKDIR /workspace

# Copy package files
COPY --chown=node:node package*.json bun.lockb* ./

# Install dependencies
RUN bun install

# Copy source code
COPY --chown=node:node . .

# Expose port
EXPOSE 3000 5173

# Default command
CMD ["bun", "dev", "--host", "0.0.0.0"] 