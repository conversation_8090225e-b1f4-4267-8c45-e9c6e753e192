# Environment Setup Guide

## Overview

This guide explains how to set up separate development and production environments for the SayYes Wedding Planner app to resolve discrepancies between local and deployed versions.

## 🏗️ Environment Architecture

```
┌─────────────────┬─────────────────┬─────────────────┐
│   Environment   │   Development   │   Production    │
├─────────────────┼─────────────────┼─────────────────┤
│ Branch          │ dev             │ main            │
│ Hostname        │ localhost:*     │ *.vercel.app    │
│ App Mode        │ dev (mocks)     │ prod (real AI)  │
│ Client Data     │ vl_wedding_planner │ vl_wedding_planner │
│ Vendor Data     │ sayyes_data     │ sayyes_data     │
│ Data Source     │ localStorage    │ Supabase DB     │
│ Debug Logs      │ Enabled         │ Disabled        │
│ Dev Tools       │ Enabled         │ Disabled        │
└─────────────────┴─────────────────┴─────────────────┘
```

## 🗄️ Supabase Project Structure

Your app uses **two separate Supabase projects**:

1. **`vl_wedding_planner`** (ID: `gxlucamlneoombifnirp`)

   - **Purpose**: Primary client/wedding data
   - **Contains**: Wedding details, chat history, budget items, tasks, guests, etc.
   - **Used by**: Main app functionality

2. **`sayyes_data`** (ID: `tuhzrxmrdghprrspftwe`)
   - **Purpose**: Vendor directory data
   - **Contains**: Vendor listings, categories, contact info
   - **Used by**: Vendor Directory page

## 🔧 Environment Variables

### Development (.env.local)

```bash
# Development environment
ENVIRONMENT=development

# Primary client data project (vl_wedding_planner)
NEXT_PUBLIC_SUPABASE_URL=https://gxlucamlneoombifnirp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_vl_wedding_planner_key_here

# Vendor data project (sayyes_data) - separate project
NEXT_PUBLIC_SUPABASE_DATA_URL=https://tuhzrxmrdghprrspftwe.supabase.co
NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY=your_sayyes_data_key_here

# Optional for testing prod features locally
GEMINI_API_KEY=your_api_key_here
```

### Production (Vercel Environment Variables)

```bash
# Production environment
ENVIRONMENT=production

# Primary client data project (vl_wedding_planner)
NEXT_PUBLIC_SUPABASE_URL=https://gxlucamlneoombifnirp.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_vl_wedding_planner_key_here

# Vendor data project (sayyes_data) - separate project
NEXT_PUBLIC_SUPABASE_DATA_URL=https://tuhzrxmrdghprrspftwe.supabase.co
NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY=your_sayyes_data_key_here

# Required for AI features
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GEMINI_API_KEY=your_production_api_key_here
```

## 🚀 Deployment Strategy

### Branch Strategy

1. **`main` branch**: Production code

   - Deploys to production environment
   - Uses real AI APIs
   - Live client data

2. **`dev` branch**: Development code
   - Deploys to preview environment
   - Uses mock data by default
   - Testing and development

### Vercel Setup (Single Project)

1. **Go to Vercel Dashboard** → Your Project → Settings → Git

2. **Configure Branch Deployments:**

   - **Production Branch**: `main`
   - **Preview Branches**: `dev` (and any feature branches)
   - **Development Command**: `bun dev`

3. **Environment Variables:**
   - Set production values in Vercel dashboard
   - Preview deployments from `dev` branch will use development mode automatically

## 📁 File Changes Made

### New Files

- `src/constants/environment.ts` - Environment detection and configuration
- `src/components/EnvironmentStatus.tsx` - Environment status component
- `ENVIRONMENT_SETUP.md` - This guide

### Modified Files

- `src/contexts/WeddingContext.tsx` - Environment-based app mode defaults
- `src/pages/OverviewPage.tsx` - Fixed chat message duplication
- `src/pages/SettingsPage.tsx` - Added environment status display

## 🛠️ How to Set Up

### 1. Create Development Branch

```bash
git checkout -b dev
git push origin dev
```

### 2. Configure Vercel Project

1. **Go to Project Settings** → Git
2. **Set Production Branch**: `main`
3. **Preview Branches**: Add `dev` branch
4. **Environment Variables**: Set production values in Vercel dashboard

### 3. Update Environment Variables

Set the production environment variables in Vercel dashboard:

**Primary Client Data (vl_wedding_planner):**

- `NEXT_PUBLIC_SUPABASE_URL=https://gxlucamlneoombifnirp.supabase.co`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY=your_vl_wedding_planner_key`

**Vendor Data (sayyes_data):**

- `NEXT_PUBLIC_SUPABASE_DATA_URL=https://tuhzrxmrdghprrspftwe.supabase.co`
- `NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY=your_sayyes_data_key`

**AI Features:**

- `VITE_GEMINI_API_KEY=your_production_api_key`

### 4. Test Both Environments

- **Development**: Work on `dev` branch, preview deployments use mock data
- **Production**: `main` branch deployment uses real AI and Supabase data

## 🔍 Environment Detection Logic

The app automatically detects environment based on:

1. **Hostname**: `*.vercel.app` = production
2. **Build Mode**: `import.meta.env.PROD` = production
3. **Default**: Development mode

## 🐛 Troubleshooting

### Chat Message Duplication

- **Fixed**: Removed `chatHistory.length` dependency from effect
- **Validation**: Welcome message cleanup runs on mount

### Missing Environment Variables

- **Check**: Settings page shows environment status
- **Validation**: Missing variables are highlighted

### Data Discrepancies

- **Development**: Uses localStorage and mock data
- **Production**: Uses Supabase database with real client data

### API Key Issues

- **Development**: Works without API key (mocks)
- **Production**: Requires valid `VITE_GEMINI_API_KEY`

## ✅ Verification Checklist

- [ ] Development branch uses mock data
- [ ] Production branch uses real AI and Supabase data
- [ ] No chat message duplication in either environment
- [ ] Environment status shows correctly in settings
- [ ] API keys are properly configured for production
- [ ] Local development works without production keys

## 🔄 Migration Steps

If you need to migrate existing data:

1. **Export from Development**:

   ```bash
   # Export localStorage data
   console.log(JSON.stringify(localStorage.getItem('sayyes_managed_weddings')))
   ```

2. **Import to Production**:
   - Use Supabase dashboard to import data
   - Or create migration scripts

## 📞 Support

If you encounter issues:

1. Check the Environment Status in Settings
2. Verify environment variables in Vercel dashboard
3. Compare local vs deployed behavior
4. Check browser console for error messages
