# LangGraph-Powered Hierarchical Agent System for VL Wedding Planner

The system will feature a fully functional Orchestrator → Manager → Associate architecture with real task execution, CopilotKit-powered UI updates, and AG-UI-powered conversational UX. I’ll focus on making sure agents can act on users' behalf, coordinate planning actions, and collaborate with human input where needed.

## Overview

We propose a **hierarchical multi-agent system** for the VL Wedding Planner app that uses **LangGraph** to orchestrate AI agents in a graph structure. The goal is to provide intelligent, proactive wedding planning assistance (via an AI assistant “<PERSON>”) by delegating user requests to specialized agent “roles.” The system integrates tightly with the existing Next.js (Turbopack) front-end and Supabase backend, and uses **CopilotKit’s AG-UI protocol** for real-time, interactive UI updates. This design ensures that AI-driven actions (e.g. adding a task, updating a budget, contacting a vendor) are transparent to the user and subject to user confirmation when needed. The architecture emphasizes modularity, state persistence, and human-in-the-loop control, aligning with the app’s current structure and capabilities.

**Key components include:**

- **LangGraph Orchestrator:** A graph-based controller (the “CEO” agent) that interprets user intents and routes them to the appropriate domain expert agents.
- **Domain Manager Agents:** Specialized “Manager” AI agents for each planning domain (Budget, Guests/RSVP, Vendors, Timeline, etc.) responsible for handling subtasks in their domain.
- **Associate Agents:** Ephemeral sub-agents spawned by Managers to handle granular subtasks or parallel tasks.
- **Shared Memory & State:** A Supabase (PostgreSQL) persistence layer (with pgvector) for long-term memory and task state sharing among agents.
- **Tool Integrations:** Domain-specific tools/functions (e.g. vendor lookup, budget update, guest RSVP) exposed to agents via LangChain-style function calls.
- **AG-UI Frontend Integration:** The AG-UI protocol via CopilotKit to stream agent messages and UI action events to the React front-end in real time. This enables partial answer streaming, UI updates (like modals or toasts), and user feedback loops during agent execution.

This system will be implemented in a new module (scaffolded under `apps/agent-system/` in the monorepo) and will leverage OpenAI GPT, Anthropic Claude, and Gemini models (and others configured in the environment) for agent reasoning. It will be compatible with the app’s **Bun** runtime for development and deployable as a Vercel serverless function or Supabase Edge Function for production. Next, we detail the architecture, agents, data flow, and integration points.

## Architecture and Agent Roles

**Hierarchical Agent Graph:** We model the AI system as a directed graph of agents using LangGraph. Each node in the graph is an agent role with a specific responsibility, and edges represent delegation paths. LangGraph, being a low-level orchestration framework for building stateful agent workflows, allows us to define this hierarchy and control the flow of tasks between agents. The framework supports long-running processes with persistent state and human interventions, which is ideal for our wedding planner scenario with ongoing planning sessions and user approvals.

**Agent Hierarchy:** At the top is the **Orchestrator (CEO Agent)**, which receives user inputs and decides how to handle them. It can delegate to one or more **Manager agents** that cover specific domains. Managers, in turn, can break down tasks and spawn **Associate agents** for fine-grained or parallel work. The hierarchy can be visualized as follows:

- **CEO Orchestrator Agent (root)** – _Role:_ “Executive” agent that oversees the entire planning assistant. It parses the user’s request and determines the high-level intent. The CEO agent does not solve queries directly; instead, it routes the request to the appropriate domain manager(s) and coordinates results. For example, if a user asks, “We are over budget on catering; what should we do?”, the CEO delegates to the Budget Manager. If a query spans domains (e.g. “Find a cheaper florist and update my budget”), the CEO might involve both Vendor and Budget Managers, orchestrating their interaction.

  - **Domain Manager Agents (children of Orchestrator)** – Each manager is an expert in a particular planning domain and is implemented as a LangGraph node (or subgraph) dedicated to that area. We anticipate the following managers (aligned with the app’s core features):

    - **Budget Manager:** Handles anything related to budgeting and expenses. This agent can answer budget status queries, suggest cost optimizations, and update the budget records. _Tools:_ It has functions to fetch the current budget breakdown, adjust category allocations, and suggest savings (possibly via AI analysis of expenses). It interacts with the `wedding_details` or `budget` tables in Supabase to read/write financial data.
    - **Guest/RSVP Manager:** Manages the guest list and invitations. It can add or update guest entries, mark RSVPs, and answer questions about guests. _Tools:_ Functions to query the guest list (from `guests` table), add a new guest, send or resend an RSVP invite, and update attendance status.
    - **Vendor Manager:** Handles vendor research and bookings. It can search the vendor directory (the separate Supabase `sayyes_data` project or an API) for vendors matching criteria, communicate with vendors (e.g., draft inquiry emails), and facilitate bookings. _Tools:_ Functions to search vendors by category/location/budget, check availability, and create a booking or contract. (Integration with the vendor ecosystem is through Supabase or external APIs – the agent calls these as needed.)
    - **Timeline/Task Manager:** Focuses on scheduling and task management. It can generate or adjust the planning timeline (using AI to suggest tasks and milestones) and integrate with the app’s task system. _Tools:_ Functions to create timeline entries (into `wedding_plan_tasks` and subtasks tables), set reminders, or adjust deadlines. This agent ensures that any AI-generated plan (e.g., a day-of timeline or a checklist) is saved to the user’s tasks.
    - _(Additional managers can be added as needed, e.g., a “Vision Board Manager” for AI image generation or a “Styling Manager,” following the same pattern. The system is modular to accommodate new agent roles.)_

  - **Associate Agents (spawned by Managers)** – These are lightweight, task-specific agents that a Manager can spin up to accomplish subtasks. An Associate might be used to perform an isolated piece of work or to do multiple operations in parallel. For example, the Vendor Manager might spawn an associate agent to handle contacting a specific vendor or to concurrently fetch details for several vendor options while it focuses on another part of the task. Associates inherit the context and tools of their manager but have a narrower goal and short lifespan (typically one concrete task, then they terminate). They report results back to their Manager agent. Using LangGraph, a Manager can programmatically create a sub-node (subgraph) for an Associate on the fly. All Associates still share the global memory/state store, so they can log findings or read relevant context from Supabase as needed.

**Agent Communication and Control:** The Orchestrator and Managers communicate via LangGraph’s orchestrated calls. In practice, the **CEO Orchestrator** will analyze the user query (either via rules or an AI classification) and then invoke the appropriate Manager agent’s logic. This can be implemented in code by treating each Manager as a callable sub-agent (e.g., the orchestrator node calls a child node). LangGraph’s design allows the orchestrator to invoke subgraphs and wait for their completion, making the delegation control flow explicit. If multiple Manager agents need to be consulted for one user query, the orchestrator can run them in sequence or in parallel (LangGraph supports branching and even joining results if needed). In most cases, one manager will handle the query; multi-agent coordination is used for complex asks.

Managers themselves operate as semi-autonomous agents with their own reasoning chain: they may plan a sequence of steps (using AI reasoning) and execute them one by one. When a Manager decides to spawn an Associate, it essentially hands off a subtask. In LangGraph terms, the Manager might call a helper agent node or a utility function (e.g., “ResearchAgent” or “EmailAgent”) which we instantiate on demand. After an Associate finishes (or if it fails), control returns to the Manager. Throughout this process, the orchestrator is monitoring the high-level progress (and could time-out or intervene if something goes wrong).

**Example:** _User asks:_ “Find a florist under \$1000 and book them for our date.”

- **CEO Orchestrator:** detects this involves vendor sourcing and a booking action, so it delegates to the Vendor Manager agent. (It might also notify the Budget Manager if budget data is needed, or the Budget Manager could be consulted by the Vendor agent; for simplicity, assume Vendor Manager handles both checking price and booking.)
- **Vendor Manager:** receives the task from CEO (“find affordable florist and book”). It breaks this down: (1) search the vendor database for florists under \$1000 who are available on the wedding date; (2) choose the best option; (3) get user confirmation to book; (4) perform the booking. The Vendor Manager executes step (1) by calling a **vendor lookup tool** (a function that queries the vendor Supabase or API). Suppose it finds three matches. For step (2), the agent (powered by an LLM) analyzes the options and picks one florist (perhaps based on ratings or proximity). It then pauses and uses a **CopilotKit action** to request user approval for the choice (triggering a confirmation modal in the UI). Once the user clicks “Confirm” in the UI, the agent resumes: it spawns an **Associate agent** to actually perform the booking (step (4)), which might involve calling an external API or sending an email template. The Associate reports success/failure back to the Vendor Manager. Finally, the Vendor Manager returns the outcome to the orchestrator (e.g., “Florist ABC has been booked for \$950 on your date, confirmation email sent”). The orchestrator then formats the final answer to the user if needed (or it may let the Vendor Manager’s response pass through as the chat message). This entire flow is managed by LangGraph behind the scenes, ensuring each subtask is a node in the graph and that state (like the florist options or user’s confirmation) is preserved through the workflow.

The **hierarchical design** keeps each agent’s scope focused and allows parallel development and testing. We will define clear interface boundaries: e.g., the Orchestrator will pass a structured **task request** to a Manager (including relevant context like the wedding ID or user preferences), and each Manager returns a **result object or message** back to the Orchestrator. The agents use shared databases for data exchange when necessary (instead of trying to stuff all context in prompts). This promotes reliability and debuggability: we can inspect the state in the DB at each step, and even intervene if needed (LangGraph supports pausing agents and injecting human feedback).

## LangGraph Orchestration Flow

Using LangGraph, we will formally define the agent network and how control flows between nodes. LangGraph allows us to create a **graph of tasks/nodes** where each node can be an LLM-backed agent or a function, and to control transitions explicitly (including branching logic and waiting for events). Below is an outline of how we will set up the orchestration:

- **Graph Definition:** In the `apps/agent-system` module, we will create a LangGraph configuration (could be done in TypeScript if using the JS SDK of LangGraph, or in Python if we call a Python service – but likely JS given the stack). Pseudo-code example (TypeScript):

  ```ts
  // Pseudo-code for defining agent nodes
  import { createAgentNode, createTool } from "@langchain/langgraph" // hypothetical JS API

  // Define tools (functions) first:
  const budgetTools = [
    createTool("getBudgetStatus", getBudgetStatusFn),
    createTool("updateBudgetItem", updateBudgetItemFn),
    createTool("suggestSavings", suggestSavingsFn),
  ]
  const vendorTools = [
    createTool("searchVendors", searchVendorsFn),
    createTool("bookVendor", bookVendorFn),
    // ... etc
  ]
  // (Similarly for guestTools, timelineTools)

  // Define Manager agent nodes with their role prompt and tools:
  const BudgetManagerNode = createAgentNode({
    name: "BudgetManager",
    prompt: budgetManagerPrompt, // system prompt defining role and behavior
    tools: budgetTools,
    memory: sharedMemoryInterface, // connection to Supabase memory for vector search
  })
  const VendorManagerNode = createAgentNode({
    name: "VendorManager",
    prompt: vendorManagerPrompt,
    tools: vendorTools,
    memory: sharedMemoryInterface,
  })
  // ... GuestManagerNode, TimelineManagerNode similarly ...

  // Define Orchestrator node:
  const OrchestratorNode = createAgentNode({
    name: "CEO_Orchestrator",
    prompt: orchestratorPrompt, // instructs to delegate tasks rather than solve directly
    tools: [
      // Instead of direct domain tools, the orchestrator might have "tools" that are actually calls to sub-agents:
      createTool("invokeBudgetManager", async (task) =>
        BudgetManagerNode.invoke(task)
      ),
      createTool("invokeVendorManager", async (task) =>
        VendorManagerNode.invoke(task)
      ),
      // ... or use a custom mechanism to route to the right agent ...
    ],
  })

  // Define graph relationships (if using an explicit graph structure):
  OrchestratorNode.addSubAgent("BudgetQuery", BudgetManagerNode)
  OrchestratorNode.addSubAgent("VendorQuery", VendorManagerNode)
  // etc., possibly with conditions or selection logic
  export const WeddingPlannerAgentGraph = OrchestratorNode // entry point of the LangGraph workflow
  ```

  In the above pseudocode, we treat each Manager as a callable sub-agent. The Orchestrator might use either a **tool-calling approach** or conditional logic to decide which sub-agent to invoke. One approach is to use OpenAI function calling: the Orchestrator agent is given a list of “functions” named after each manager (like `BudgetManager()`), and based on the user query it will choose one. For example, if the user input mentions budget or money, the Orchestrator LLM might call `invokeBudgetManager` function with the user query as argument. We’ll ensure the Orchestrator’s prompt encourages it to delegate and not solve things itself, essentially making it a router. In simpler terms, a rule-based or ML-based classifier could also direct queries to agents without an LLM at this level. The architecture is flexible – the orchestration logic can be AI-driven or static rules, as long as it produces the correct subtask routing.

- **Graph Execution:** When a user submits a request via the UI, the **agent entrypoint** (API route or function) will invoke `WeddingPlannerAgentGraph` with an initial input node (the user message). LangGraph will then manage the execution: it will start at the OrchestratorNode, which will analyze the message and delegate to the appropriate Manager node(s). Each Manager node may further invoke tools or spawn associates (which can be implemented as either recursive calls to the same Manager with modified goals or separate agent nodes defined for specific subtasks). LangGraph’s support for branching means we can have sequences like:

  1. Orchestrator -> VendorManager (for primary task)
  2. VendorManager -> \[Tool: searchVendors]
  3. VendorManager -> \[Action: request confirmation] -> **pause** workflow until user responds
  4. On confirmation -> VendorManager -> \[Spawn Associate: BookVendorAgent]
  5. Associate completes -> returns result -> VendorManager
  6. VendorManager -> returns final answer -> Orchestrator -> user.
     This entire chain is one directed path through the graph, with a pause for human input. If multiple parallel tasks were needed, we could define two branches from Orchestrator that run concurrently and then join results (though initial use-cases can likely be handled sequentially). LangGraph’s durability ensures that if an agent or tool errors out, we can catch it and either retry or gracefully abort that branch without crashing the whole flow.

- **State and Context Passing:** LangGraph doesn’t abstract away prompts or data; we explicitly manage what each agent sees. The Orchestrator will pass along necessary context to the Manager agents. This could include the original user query, the relevant IDs (like which wedding or user), and any parsed intent info. Managers, when spawning associates, will provide them with the slice of context they need (e.g., VendorManager gives a BookingAgent the vendor ID and user’s contact details). Additionally, all agents have access to a **shared memory interface** (discussed below) so they can pull in additional context from the database on demand – for example, BudgetManager can query the total budget or spending so far from Supabase as it works. Each agent’s prompt can be dynamically constructed with known facts (e.g., “You are the Budget Manager. The total budget is \$50,000, current spending is \$52,000, categories over budget: Catering.”).

- **Long-Running Workflow:** If the user conversation is ongoing, LangGraph can maintain the graph across messages or we can reconstruct needed state for each query. Given Vercel/Supabase functions are stateless per request, our design will likely treat each user message as a new invocation of the graph. However, because we persist important state in Supabase, the agents can recover context (like previous decisions or the current plan) at the start of each invocation. LangGraph’s checkpointing features (in Python, at least) could allow pausing and continuing agents across requests, but implementing that in a serverless environment is tricky. Instead, we’ll simulate continuity by storing the agent-generated plan or partial outputs in the DB. For example, if a user asks a follow-up question, the orchestrator can fetch the last conversation summary or any pending task context from memory to include in prompts, effectively continuing the conversation seamlessly.

In summary, LangGraph provides the scaffolding to build a robust, controllable agent workflow. We define each agent as a node with specific tools and responsibilities, and the orchestrator as the policy manager that directs the flow. Because LangGraph is used in production by others for complex agent systems, we gain confidence that features like error handling, stateful execution, and human intervention are well supported. We will leverage these to ensure our agents behave reliably in the unpredictable environment of wedding planning (where user requests can be diverse and critical data must persist).

## State Management and Shared Memory (Supabase Integration)

To make the agent system effective and contextually aware, we implement a **persistent memory store and task queue using Supabase**. This allows all agents (which may run as separate invocations or even in parallel) to **share state and knowledge** beyond what can fit in the prompt window. It also ensures that important information survives beyond a single agent run – vital for a long-term planning session spanning days or weeks.

**Supabase as Knowledge Store:**
We will create a dedicated table (or set of tables) in the Supabase PostgreSQL database to store agent memory. One table (e.g., `agent_memory`) will hold textual information with embeddings (pgvector) for semantic search. This memory might include: conversation transcripts, key facts extracted (like wedding date, venue booked, budget limits), and any notes or decisions made by agents. When an agent needs to recall something, it can query this vector store by embedding the query and finding similar entries. For instance, if the user later asks “Did we already book a photographer?”, the system can vector-search memory and find that a vendor booking entry for “Photographer” was made, and thus answer accurately even if that happened in a prior session. The **pgvector** integration means we can do this efficiently inside the database. We’ll use Supabase’s support for vector queries to implement functions like `searchMemory(queryEmbedding) -> topN results`.

In addition, structured data is already stored in various tables (guests, tasks, budget, etc.). Agents will prefer using those authoritative sources via tools. For example, instead of relying purely on an LLM’s memory of a budget total, the Budget Manager will call the database to get the latest numbers (ensuring consistency with any manual edits the user may have done via the UI). The **memory table is more for unstructured or conversational context** that isn’t captured in those structured tables. We will design a **MemoryManager** utility in the agent system to interface with Supabase: it can save a snippet to memory (with metadata like which agent wrote it, timestamp, tags) and perform searches. All agents can use this via a common tool (e.g., a `RecallMemory` and `StoreMemory` tool) if needed. This is analogous to how a LangChain memory would work, but backed by a persistent store.

**Task Queue / State Tracking:**
We will also use Supabase to maintain a **task and state log** for agent activities. This could involve tables such as:

- `agent_tasks`: to log each high-level request from the user and which agent handled it, along with status (pending, completed, failed). This is useful for auditing and for resuming if needed. For example, if an agent had to wait for user confirmation, an entry might remain “pending” until confirmed, then marked “completed” after action.
- `agent_subtasks`: if we want to log subtask details. However, since the app already has tables like `wedding_plan_tasks` and `wedding_plan_subtasks` for the _wedding planning tasks_, we might map agent-generated tasks into those rather than duplicating. For instance, if the Timeline Manager agent creates a new timeline entry “Order invitations by Jan 1”, it can directly insert that into `wedding_plan_tasks` table (which would make it visible in the UI’s task list). In this sense, the wedding plan tasks table becomes part of our state: the agent populates it and the UI and user can see/update it normally. We will ensure to mark such tasks as AI-suggested (perhaps via a flag column) so they can be treated differently if needed (or for the user to know it came from AI).

Using the database as a queue or log means agents that run in separate processes (or even potentially concurrently) can still coordinate. For example, a Manager agent could insert several subtasks in a table for associates to pick up. However, in our initial design we likely orchestrate associates via code (the Manager directly calls them). The task log is more for persistence and UI reflection. We’ll implement transactions or check-and-set logic for critical sections (ensuring two agents don’t double-book the same vendor, for instance). Supabase’s row-level security will be configured so that each user (or planner) only accesses their own project’s data, which aligns with the existing app’s multi-tenant design.

**Session and Identity:**
Since the app uses **Clerk** for auth and multiple wedding projects per user, we will ensure that each agent invocation is scoped by a **wedding ID or user ID**. The Orchestrator will be initialized with the current context (e.g., `currentWeddingId` from the app state), and that will propagate to all DB queries (either by including it in the query or by using the RLS policies). This prevents any cross-user data leakage and keeps the AI focused on the user’s specific wedding. We can fetch static context (like wedding date, couple’s names) at session start and feed it to the agents as part of their system prompts for richer personalization.

**Memory Example:** Suppose the Budget Manager last week suggested cutting cake costs. It would have stored a summary in memory like “Suggested cutting cake budget by 20% – user undecided.” Now, today the user asks, “Any ideas to reduce costs?” The Orchestrator can do a memory search and find that prior suggestion, and supply it to the Budget Manager agent (or even directly remind the user). The Budget Manager might then follow up on that, e.g., “Last week I proposed reducing the cake budget. We could revisit that or consider other areas.” This ensures continuity in the AI assistance, making it feel truly like a long-term planning aide.

**Vector DB for AI Knowledge:** We might also use the vector store to embed external knowledge (like vendor descriptions or common planning tips) so agents can retrieve factual info instead of hallucinating. For example, the Vendor Manager could vector-search a knowledge base of vendor FAQs or contract terms if it needs to answer a detailed question. This could be integrated as a retrieval-augmented generation (RAG) step via a tool call.

**Summarization for Long Conversations:** If the chat history with the AI grows large, the Orchestrator (or a dedicated “MemoryAgent”) can periodically summarize the conversation and store that, to keep prompts concise. The vector memory helps in retrieving relevant pieces of the conversation rather than sending the entire history to the model each time, which is important for token limit and performance.

**State Reset and Undo:** With a persistent log, we can implement “undo” features. For instance, if the agent added a task or made a change the user doesn’t want, the user (or system) can revert that action by deleting the DB entry or marking it undone. We will expose an “undo last action” function perhaps via the UI. The agent system can log a reversible set of changes for each high-level request, enabling one-click rollback. For example, if a vendor booking is done (which might send an email), an undo might send a cancellation email or mark that booking as cancelled in the database. While full automatic undo might not always be possible, we will design the system to **always seek user confirmation for irreversible actions** (thus preventing the need for undo in most cases).

In conclusion, Supabase serves as the **single source of truth** for the agent system’s memory and actions. All stateful information flows through it, whether it’s structured data (budget, tasks, guests) or unstructured memory (conversation logs, intermediate notes). By leveraging the database, we ensure that the AI’s operations are transparent and reproducible – developers or admins can inspect tables to see what the AI did, and users see changes reflected in the app’s UI (since the UI already reads from these tables). This tight integration also means if the user or planner makes manual changes (e.g., manually adds a task or edits the budget), the AI agents will see those updates on next run (because they query the same DB). The memory and state mechanism is core to making the AI a helpful “team member” in the planning process, rather than a detached chatbot.

## Tool Integrations for Agent Capabilities

Each agent is equipped with a set of **tools (functions)** corresponding to actions it can take or data it can query. We will implement these tools in a manner similar to LangChain’s tool mechanism, where the agent’s LLM can decide to invoke a function and get back its result. In the context of GPT models, we’ll use the function calling API to expose these capabilities to the agent; with LangGraph, we can register tools as shown in the pseudocode above. The design of tools will mirror domain operations in the wedding planner:

- **Budget Tools:** The Budget Manager uses tools to directly interact with budget data.

  - `getBudgetStatus(weddingId)`: Fetches the latest budget info from the DB – total budget, total spent, breakdown by category, etc. This might run a SQL query or call a Supabase RPC that returns a structured JSON of the budget. The agent can then reason over which categories are problematic.
  - `updateBudgetItem(itemId, newAmount)`: Updates a specific budget line item’s allocation or expenditure. The agent would call this if it has user approval to make a change (for instance, moving \$500 from decor to catering). It writes to the `wedding_details` or a `budget_items` table. After calling this, the agent might automatically call `getBudgetStatus` again to see the updated state or confirm success.
  - `suggestSavings()` or `listOverbudgetAreas()`: These could be analytical tools. For example, `listOverbudgetAreas` could query which categories are over budget or which items exceed their estimates, returning data the LLM can use to form suggestions.
  - _Tools may also integrate AI services:_ e.g., a `generateBudgetReport` tool might use an LLM to create a summary or a nicely formatted breakdown for the user (though the primary LLM could do that without a separate tool, depending on implementation).

- **Guest/RSVP Tools:** The Guests Manager’s tools revolve around the guest list:

  - `getGuestList(weddingId)`: Query the guest table to get all guests and their RSVP status. The agent might use this to answer questions like “How many people have accepted?” or to find if a particular person is on the list.
  - `addGuest(name, contactInfo, group)`: Inserts a new guest entry. The agent would call this if the user says “Add John Doe to the guest list.” Before calling, the agent could do a quick check (maybe via another tool `findGuestByName`) to avoid duplicates, or it may just attempt add and handle a duplicate error from the DB.
  - `updateRSVP(guestId, status)`: Updates a guest’s RSVP status (e.g., from “invited” to “attending”). This would be used if the user instructs, “Mark Alice as attending.”
  - Possibly `sendInvitation(guestId)`: Initiates sending an invitation email or link to the guest (integrating with an email service). This might be a longer process (maybe handled by an Associate agent or background job), so the tool could enqueue an email and return a success acknowledgement.
  - `getHeadcount(weddingId)`: Returns count of confirmed vs pending guests, etc., if the AI needs to provide stats.

- **Vendor Tools:** The Vendor Manager’s tools interact with the external vendor data and booking process:

  - `searchVendors(type, location, priceLimit)`: Queries the vendor directory (possibly the `sayyes_data` Supabase or an Algolia index) for vendors of a given category in the specified location and under a budget. For example, `searchVendors("florist","Austin, TX", 1000)` returns a list of florist profiles within \$1000 in Austin. The agent’s LLM would get a structured list (names, prices, ratings) and then decide how to proceed (perhaps it will present top 3 to user or pick one based on some heuristic).
  - `getVendorDetails(vendorId)`: Fetches detailed info on a particular vendor (services offered, availability, contact info). Might call a different table or an API.
  - `checkAvailability(vendorId, date)`: If supported, queries whether the vendor is available on the user’s wedding date. Could be a direct DB field or an API call to the vendor. If not available as a direct query, the agent might have to prompt the user to reach out to vendor – but we could simulate a tool for demo (or integrate a calendar API if available).
  - `bookVendor(vendorId, weddingId, details)`: This tool would handle finalizing a booking. Implementation-wise, it could create a record in a `bookings` table and send a notification to the vendor. If integrated with an external service, it might call that API. Because booking often requires user confirmation (and possibly payment outside our scope), in our system the agent will always confirm with the user first (via modal) before calling `bookVendor`. The tool would return success or failure message. For safety, this might actually create a “tentative booking” entry or send an inquiry rather than a full commit, to allow cancellation if needed.
  - `draftEmailToVendor(vendorId, topic)`: A utility tool that uses an LLM (or template) to draft an email. The agent might use this to show the user a draft of an inquiry or booking request for approval. This overlaps with CopilotKit UI actions (we could alternatively send the draft to the UI for the user to edit/approve).

- **Timeline/Task Tools:** The Timeline Manager deals with scheduling:

  - `generateTimeline(weddingId)`: Calls an AI (or a series of prompts) to create a high-level timeline of tasks/milestones from now until the wedding day, or a day-of timeline. It could insert multiple entries into the `wedding_plan_tasks` table (one for each major phase, each with subtasks in `wedding_plan_subtasks`). This tool encapsulates a complex operation: it might gather data from wedding_details (like the date and key events) and then use an LLM to produce a plan. We may implement it by a Python function calling our AI or using a prompt with few-shot examples.
  - `addTask(weddingId, task)`: Inserts a single new task (with possibly a reference to which category, due date, etc.). The agent would use this when it needs to add a follow-up action for the user. E.g., if the user says “Remind me to send invitations in January,” the Timeline Manager might call `addTask` to create a task “Send invitations” due in January.
  - `updateTaskStatus(taskId, status)`: Marks a task as done or deferred, etc. (Though typically the user or the system UI might do that, an agent could auto-complete tasks it helped finish, like marking “Book florist” as done after booking.)
  - `getTimelineOverview(weddingId)`: Retrieves all tasks or upcoming milestones to answer questions like “What’s next on my schedule?” or to let the agent provide a progress update.

- **Cross-Domain Tools and Utilities:** We’ll also have some general tools accessible to multiple agents:

  - `queryDatabase(queryName, params)`: A generic tool that can run a parameterized SQL or call a Supabase function. We can use this for quick reads or writes that we haven’t explicitly modeled above. However, we will guard its usage and prefer dedicated tools to prevent arbitrary queries.
  - `askUser(question, options)`: While not exactly a tool, this is a mechanism for agents to prompt the user for missing info or confirmation. LangGraph and our UI integration allow the agent to yield control back to the user. For example, if a detail is missing (“What is your catering budget?”), the agent can use a special action to ask this. In implementation, we might treat it as a tool that produces an AG-UI event of type question with suggested answers. (The AG-UI protocol can handle multi-turn interactions where the agent explicitly requests input, then waits.)

    - Similarly, a `requestConfirmation` action can be a standardized tool: the agent provides a message or summary of what will be done, and the UI will present “Confirm / Cancel” options. When the user responds, that input is fed back to the agent workflow.

  - `emitEvent(eventType, payload)`: A low-level utility to send custom events to the UI. We will use this for things like toasts or other UX niceties that aren’t just text. For instance, after adding a guest, the Guest Manager might call `emitEvent("SHOW_TOAST", { message: "Guest added successfully" })`. Under the hood, this will queue an AG-UI event of type `STATE_DELTA` or a custom event that the front-end knows how to display as a toast. (CopilotKit allows defining custom actions; more on this in UI integration.)

All tools will be implemented as functions in our code (likely under `apps/agent-system/tools/`). The agent nodes will have these tools registered so that during the LLM’s reasoning loop, it can decide to call them. For example, using OpenAI’s function calling: the agent’s prompt might say: “You have functions `getBudgetStatus` and `suggestSavings` available,” and GPT might output a function call `getBudgetStatus` if the user asked about budget, then use the result to craft its answer. The LangGraph library will help manage this sequence (especially if using its ReAct agent patterns, though we might implement a custom strategy since our scenario is complex).

We will carefully design the **function schemas and outputs** so that the LLM gets useful info. Each tool returns structured data (likely JSON objects). For instance, `searchVendors` might return `[{id, name, price, rating, ...}, ...]`. If the list is long, the agent might decide to summarize or ask the user to pick. We won’t overwhelm the model with data beyond what it can handle in context.

Security/limits: Tools that modify data (like updateBudget, bookVendor) will be used only when appropriate. The agent’s system prompts will include instructions to only call such tools after user confirmation or instruction. We will also include **safety checks** in the tool implementations – e.g., `bookVendor` might double-check if a “confirmed” flag is present in context, or if not, it could refuse and signal that confirmation is needed. This belt-and-suspenders approach prevents accidental side effects.

By structuring interactions as tools, we keep the LLM’s role to high-level reasoning and delegation, while actual data retrieval and updates happen via deterministic code. This both improves reliability and aligns with the **LangChain/LangGraph philosophy of tool use** for groundedness. It also makes testing easier – each tool can be unit-tested with simulated inputs and known outputs, and we can simulate various scenarios (like tool returning an error or empty result, and see how the agent reacts).

Finally, even UI actions (next section) are exposed as “tools” to the agent (in a conceptual sense). For example, showing a modal is an effect the agent triggers by calling a function (e.g., `confirmBookingModal(vendor, price)`) which we implement to emit the event. Thus, from the agent’s perspective, everything it does other than pure reasoning/talking is a tool or action call. This consistent approach fits naturally with the iterative “think -> act -> observe -> reply” loop of an AI agent.

## AG-UI Frontend Integration and CopilotKit Events

To deliver a smooth **interactive experience**, we integrate the agent system with the front-end using the **AG-UI protocol** via CopilotKit. AG-UI (Agent-User Interaction) provides a standardized way to stream events from our backend to the UI, so the user can see the AI’s thought process, outputs, and requested interactions in real time.

**Streaming Responses:**
When a user sends a message or command (via the chat interface or a button that triggers the AI, like “Generate timeline”), the front-end will make a single POST request to our agent API endpoint (for example, `/api/agent` or an Edge Function URL). This request contains the user prompt and relevant metadata (like a session or wedding ID). The backend orchestrator (LangGraph) will then begin processing and immediately start streaming back an event stream as the response. We’ll use **server-sent events (SSE)** or a similar HTTP streaming mechanism to send a sequence of JSON objects, each describing an event in the agent’s execution. The CopilotKit React SDK on the front-end will handle this stream and update the UI accordingly.

AG-UI defines several event types which we will utilize:

- **TEXT_MESSAGE_CONTENT:** As the agent formulates a textual reply, partial content is sent with this type. The user will see the assistant “typing” the answer token-by-token or sentence-by-sentence. For example, if the Budget Manager is explaining cost-saving suggestions, the user will see the paragraph appear gradually, just as in ChatGPT or similar UIs.

- **TOOL_CALL_START / TOOL_CALL_END:** When an agent invokes a tool, we can emit an event indicating that (start) and another when the tool returns (end). This can be used to show intermediate status in the UI, such as “🤖 Searching vendor database...” while the tool call is in progress, then possibly show the results or simply remove the status when done. This level of detail keeps the user informed that the AI is actively doing something (and if a tool takes a bit of time, the user knows it’s working and not stuck).

- **ACTION_REQUEST / ACTION_RESULT:** This is how we handle custom UI actions. When an agent needs the UI to do something (like open a modal or show a toast), it will emit an event with a custom action type. CopilotKit’s `useCopilotAction` hook on the React side is used to register these possible actions and their handlers. For instance, we will define in the front-end code an action named `"confirmVendorBooking"` with a handler that opens a confirmation modal dialog. When the backend sends an event like `{ type: "ACTION_REQUEST", action: "confirmVendorBooking", params: { vendorName: "ABC Florist", price: 950, date: "2025-06-01" } }`, the CopilotKit middleware will catch it and invoke our handler, causing the modal to appear. The agent run will **pause** at this point, waiting for a response. When the user clicks the confirm button, our handler might do two things: (1) close the modal and (2) send an **ACTION_RESULT** event back to the agent stream indicating the user’s choice (the CopilotKit hook likely abstracts this, but essentially the user’s response needs to flow to the backend agent). We will design the agent to anticipate this: e.g., the Vendor Manager after sending the confirm request will wait until it receives a message/event that the confirmation was given (LangGraph can suspend that branch until an event is received, implementing the human loop).

- **STATE_DELTA or UI_UPDATE:** These events represent changes in the agent’s state or external state that should reflect in UI. For example, if the agent adds a task to the task list, we can emit a STATE_DELTA event carrying the new task data. The front-end, which might be subscribed to such events, will then immediately show the new task in the UI (perhaps highlighting it as “AI-added”). Similarly, for something like marking an RSVP, instead of waiting for the usual Supabase subscription to pick up the DB change, we can optimistically update the UI via an event. This gives instant feedback. We will likely implement such events for:

  - Task list updates (add/remove tasks).
  - Guest list updates (new guest added or RSVP status changed).
  - Budget changes (e.g., updating a budget item could trigger an update event so the budget UI can reflect the new values without a full reload).
  - Possibly timeline/Gantt chart updates if the UI has a visual timeline.
    These events contain just the minimal data needed (diffs) so they are efficient.

- **ERROR or FINAL:** In case of errors or when the agent finishes all steps, final events will be sent. We will follow the AG-UI spec for signaling completion (maybe an event like `{type: "COMPLETE"}` at the end of stream so the front-end knows to stop listening and re-enable the input box). If an error occurs (say a tool fails with an exception we didn’t handle), we catch it and send an `ERROR` event with a message for the user (“Sorry, something went wrong while booking the vendor. Please try again.”). The UI can display this gracefully (e.g., as an assistant message or a notification).

**CopilotKit UI Components:**
On the front-end, CopilotKit provides pre-built React components that likely include a chat view and maybe some modals. We will integrate these into our Next.js app UI (which already uses Tailwind and shadcn UI). The chat interface will show messages from the user and from the AI (Ella). Because of AG-UI, the AI messages might be composite: some pure text, followed by perhaps a special component when an action completes (for example, after a vendor search, the agent might send an event that includes a list of vendors found; we could intercept that and render it as a small rich card or table in the chat). CopilotKit’s React library encourages such custom rendering: e.g., if the agent emits a structured data payload (maybe as a STATE_DELTA event with type “VENDOR_SEARCH_RESULTS”), we can have a React component listening for that and then display a nice result card in the chat instead of raw JSON. This makes the interaction more user-friendly.

We will use the `useCopilotAction` hook to define handlers for each **custom action** the agent might request. Concretely, we expect to implement the following actions (names tentative):

- `"confirmVendorBooking"` – Opens a modal with vendor details and “Confirm/Cancel” buttons. In our React code, this might set some state like `showBookingModal = true` and pass the vendor info to a Modal component. The `render` property (as seen in examples) could be set to something like `"Awaiting confirmation..."` which might be displayed as a system message while we wait. When user confirms or cancels, the handler will send the result back. (CopilotKit likely takes care of sending a follow-up user event; we may integrate it by calling an API route or via the event stream directly.)
- `"showToastMessage"` – Takes a message string and triggers a toast notification on the front-end. We’ll use our existing toast component (perhaps from shadcn UI or Radix) to display a brief non-blocking notification. For instance, after a successful update, agent calls this with message "Budget updated!" and the user sees a toast. This action is purely for UX sugar; it doesn’t require user response.
- `"addTaskToUI"` – This could serve to immediately append a new task in the UI’s task list. However, since our plan is to actually insert into the DB, the app’s state might update via Supabase’s real-time subscriptions anyway. If that’s real-time, a toast might suffice to indicate it was added. But we could also push the new task content via event to avoid any delay. Alternatively, the agent’s final message might include text like “_(Added to your task list.)_” which the user sees, and then the normal UI list will show it because of the DB insertion. We will coordinate with the front-end behavior to decide if explicit action events are needed for tasks or if DB sync covers it.
- `"openURL"` or `"navigatePage"` – Possibly an action to navigate the user to a different page in the app if needed (e.g., after booking a vendor, maybe suggest “View Vendors Page for details”). CopilotKit could handle such an action by, say, using Next router to push a new route. We can include such optional actions (with user confirmation ideally) to help users follow up on what the AI did.
- `"highlightUIElement"` – Another possible UI action is highlighting or focusing parts of the interface to guide the user (for instance, flash the budget overage in red if AI points it out). This might be an enhancement later; initially, a simple text explanation is fine.

**Front-End Display of Agent Activity:**
All agent actions will ultimately be reflected in the chat conversation or via UI elements. The user primarily interacts through the chat (AG-UI ensures a unified stream of messages and events). For instance, when the Budget Manager completes a suggestion, it might produce a multi-part response: first text streaming “I noticed your catering is \$2k over budget. You could reduce guest count or choose a cheaper menu...”, then it might emit a STATE_DELTA event that updates the Budget page chart to reflect a hypothetical change, or just waits for user input. We have to ensure that any non-text events are also communicated in the conversation context if needed. Perhaps a better example: The agent asks user a question via `<ask_followup_question>` tool (if it truly cannot find required info) – that would come through as an AG-UI event prompting the user with suggested answers. Our UI would show this as the assistant asking a question with clickable suggestions (like quick reply chips). The user’s click would then send a message which resumes the agent’s flow.

**CopilotKit Integration Details:**
We will integrate the CopilotKit provider in our Next.js app (likely wrapping our app in a context that handles the SSE connection). We’ll use their React hooks to subscribe to the event stream from our agent endpoint. The documentation indicates one can connect CopilotKit to “any agent backend (OpenAI, Ollama, LangGraph, or custom)”, which fits our case. The AG-UI protocol being open means we might have to implement the event formatting ourselves in the backend if not using a provided library for Node. We’ll likely create a simple serializer: e.g., when the Orchestrator starts, we `res.write()` an event for any system messages or just to confirm the stream started. Then, as the LLM yields tokens, we package them into JSON with type TEXT_MESSAGE_CONTENT and send them. For tool calls and custom actions, our agent framework will trigger callbacks where we insert appropriate events into the stream. We’ll make sure to flush buffers to keep the stream responsive.

One important aspect is **multiple concurrent sessions** and stopping runs. AG-UI events include identifiers for threads or runs. We will tag each event with an `agentRunId` (probably provided by CopilotKit when initiating the call) so the front-end can distinguish if multiple chats are happening or if a user refreshes. Also, if the user hits “Stop” or navigates away, we’ll handle SSE disconnect and instruct LangGraph to cancel the ongoing tasks (LangGraph supports cancellation/resume via its durable execution features). On cancel, we might abort any external calls and clean up partial state if needed (e.g., if halfway through adding tasks, either complete it or mark incomplete).

**Security & Controls:**
Since events flow over HTTP, we’ll ensure proper auth on the endpoint (likely requiring the user’s Supabase JWT or Clerk token to be passed in the request, so only authorized users can invoke their agent). The SSE connection will be subject to CORS and auth; AG-UI is designed with enterprise security in mind. We’ll use an HTTPS connection, and possibly log events server-side for audit. The user also has the ability to intervene at certain points: the system will explicitly ask for confirmation for potentially destructive actions (booking, spending money, sending emails). If the user says no or cancels, the agent will gracefully handle that (maybe apologizing or asking if they want something else).

**Example UI Walkthrough:**

- The user opens the “AI Assistant” panel (which uses CopilotKit’s chat component styled to match our app). They type: “We might have too many people; can you adjust the budget if we cut 10 guests?”.
- In the backend, orchestrator passes this to Budget Manager (guest count affects budget). The UI immediately shows the user’s message in the chat and an assistant placeholder message “Ella is thinking...”.
- As the Budget Manager agent works, it might call `getBudgetStatus` – we send a TOOL*CALL_START event (rendered as a small inline status, e.g., “🤖 Checking current budget...”). The DB returns data, agent calculates new budget scenario and calls `updateBudgetItem` for the catering cost. Because this is a significant change, suppose we decide to require confirmation (maybe not strictly needed for budget tweaks, but let’s assume). The agent emits an ACTION_REQUEST for “confirmBudgetChange” with details (e.g.,*“Reduce catering headcount by 10 to save \$X?”\_). The UI shows a prompt (maybe in the chat or a pop-up) with “Ella suggests reducing 10 guests to save \$X on catering. Do you want to apply this change?” with Yes/No buttons. The user clicks Yes.
- The front-end sends back the confirmation (through CopilotKit’s mechanism, which could be sending a hidden message or an SSE response). The agent resumes, performs the change (which updates the DB). It then emits a STATE_DELTA event with the new catering budget or simply a toast event. The UI shows a toast “Updated catering budget (-10 guests)”, and the budget page (if open) reflects the new numbers (maybe via direct DB subscription or the event).
- Meanwhile, the chat continues: the assistant’s message now continues to stream: “I’ve adjusted the budget to account for 10 fewer guests. Your new catering cost is \$8,000, which brings your total budget to \$48,000 (now within your \$50k limit) 🎉.” This appears as a nicely formatted assistant message, possibly with an emoji or styled text. The placeholder “Ella is thinking” is replaced by this content gradually.
- The conversation is logged (the suggestion and user’s confirmation are stored in memory). The user can then ask another question or close the chat.

This interplay shows how AG-UI lets the user in on what’s happening (the agent was not a black box – the user saw when it was “Checking budget” and got to approve the change). It also demonstrates real-time updates to the app’s state. By using CopilotKit, we avoid building custom WebSocket logic and leverage their out-of-the-box components and hooks for actions. The AG-UI protocol essentially acts as the **bridge between LangGraph (backend brain) and the React UI (frontend experience)**, ensuring they stay in sync with minimal effort.

We will thoroughly test these interactions to fine-tune timing (e.g., ensure modals pop at appropriate times, avoid race conditions in SSE ordering, etc.). The final UX should feel like the user is chatting with an AI planner that can also **push changes to the app in real time** – for instance, when it says “I’ve added this to your task list”, the user immediately sees that task appear without a page refresh. This tight coupling of chat and application state is a key feature and advantage of CopilotKit’s approach.

## Module Structure and Implementation Details

We will implement the agent system as a self-contained module within the monorepo, organized for clarity and maintainability. Below is the proposed structure for the `apps/agent-system/` directory (and related files):

```
apps/
  agent-system/
    src/
      agents/
        orchestrator.ts        – Definition of the CEO Orchestrator agent (prompt and logic)
        budgetManager.ts       – Budget Manager agent class or config
        vendorManager.ts       – Vendor Manager agent class or config
        guestManager.ts        – Guest Manager agent class or config
        timelineManager.ts     – Timeline Manager agent class or config
        associateAgents.ts     – Module for spawning associate agents (could define generic AssociateAgent class)
      tools/
        budgetTools.ts         – Implementations of budget-related tools (DB queries and updates)
        vendorTools.ts         – Implementations of vendor-related tools (search API calls, etc.)
        guestTools.ts          – Implementations of guest list tools
        timelineTools.ts       – Implementations of timeline and task tools
        uiActions.ts           – Functions that wrap CopilotKit actions (modal triggers, toasts)
      memory/
        memoryManager.ts       – Utility for Supabase vector memory (functions to store/retrieve memory)
        supabaseClient.ts      – Initialized Supabase client (with service role if needed for server)
        embeddings.ts          – Functions to embed text (calling OpenAI embeddings API or similar)
      orchestratorGraph.ts     – LangGraph graph definition tying agents together
      agentRunner.ts           – High-level function to invoke the LangGraph graph given a user input, streaming events out
    tests/
      test_orchestration.spec.ts  – Unit tests for the orchestration logic
      test_memory.spec.ts         – Tests for memory recall and persistence
      test_tools.spec.ts          – Tests for each domain’s tools (with maybe a test DB or mocking Supabase)
    README.md                 – Documentation of the agent system for developers
```

And in the main Next.js `apps/web/` (assuming the main app is there), we will integrate:

```
apps/web/
  app/
    api/
      agent/route.ts          – (if using Next 13 API route) The HTTP endpoint that calls agentRunner and streams output
  components/ai/             – (new directory for AI-related UI components)
    ChatUI.tsx               – Chat interface component (using CopilotKit hooks, showing messages and handling actions)
    ConfirmBookingModal.tsx  – Example custom modal component triggered by copilot actions
    ... (other needed components or wrappers)
  context/CopilotProvider.tsx – Context provider to set up CopilotKit (if needed, e.g., to provide API base URL or config)
```

Some notes on these modules:

- **Agent Definition Files (`agents/*.ts`):** Each will likely export an object or class that encapsulates that agent’s behavior. This includes the system prompt (defining the agent’s persona and instructions), the list of tools accessible, and possibly a custom handler function if we need to intercept calls. For example, `budgetManager.ts` might export something like `export const BudgetManagerAgent = createAgent({ ...config as in pseudocode earlier... })`. The system prompt might be a template string: e.g., “You are an AI Budget Manager. You help users stick to their wedding budget. Always double-check the database for current budget numbers before giving advice. Tools: you can query budget info and suggest ways to save,” etc. We might also include example dialogs in the prompt to show it how to behave (few-shot examples, if necessary, to fine-tune style).

  We will align these prompts with the **tone and knowledge of the domain**. For instance, the Vendor Manager’s prompt might include something about being professional when drafting messages to vendors, or the Guest Manager’s prompt might emphasize privacy (don’t reveal guest info inappropriately). These will be crafted based on domain specifics and perhaps iterated during testing.

- **Tools Implementation (`tools/*.ts`):** These files contain actual code for the tools. They will use Supabase clients or other APIs. For DB calls, we’ll likely use the official Supabase JS client (v2, which works with edge runtimes). For example, `budgetTools.ts` might have:

  ```ts
  import { supabaseAdmin } from "../memory/supabaseClient"
  export async function getBudgetStatusFn({ weddingId }) {
    const { data } = await supabaseAdmin
      .from("budget")
      .select("*")
      .eq("wedding_id", weddingId)
    // process data to structure needed by agent...
    return {
      totalBudget: data.total,
      totalSpent: data.spent,
      byCategory: data.categories,
    }
  }
  ```

  and so on. We’ll ensure each function returns quickly (most queries are indexed and lightweight). For longer operations like external API calls, we might add small delays or timeouts and handle errors (e.g., catch fetch failures and return an error object that the agent can react to).

  We will also include in `uiActions.ts` functions that don’t directly change DB but trigger UI events. However, those aren’t called via Supabase but rather by sending events on the SSE channel. Implementation-wise, since `agentRunner` is managing the stream, a `showToast(message)` tool might just push an event into a buffer that `agentRunner` reads and sends out as `{type: "ACTION_REQUEST", action: "showToastMessage", params: {message}}`. Alternatively, we directly call a helper on the runner to emit that event. The architecture might have a central event emitter for the current agent run.

- **Memory Management (`memory/*.ts`):**

  - `supabaseClient.ts` will initialize a Supabase client using admin/service role privileges (so it can bypass RLS for certain operations like writing memory or reading any user’s data if needed). We’ll keep this secure (not exposing to client-side). For Edge Functions, we might use the Supabase Deno client; for Next API (Node or Edge), the JS client with the service key. This will also handle connecting to the vendor database (if separate) by possibly initializing two clients (one for main, one for vendor data).
  - `memoryManager.ts` will implement methods like `storeMemory(weddingId, content, embedding, metadata)` and `queryMemory(weddingId, queryEmbedding, filter?)`. This will use `supabaseClient.rpc('match_memory', {...})` if we create a PG function for vector search, or the upcoming Supabase Vector SQL directly (using `ilike` or `embedding <-> query` syntax). We’ll ensure this is tested with some sample data. Also might include a function to summarize and prune memory if it grows too large (maybe not needed initially).
  - `embeddings.ts`: to get embeddings for memory or for tool queries, we’ll use OpenAI’s API (or possibly local if privacy is a concern). Likely, we’ll call `openai.createEmbedding` with text. Because this is on server side and could slow things, we’ll pre-embed known data (like all vendor descriptions, or initial wedding details) and cache them. For new memory entries (like conversation logs), we can embed on the fly. This requires an OpenAI API key which we have. We should also keep an eye on rate limits; the memory queries should be light (embedding user’s single query or a few strings per chat).

- **Orchestrator Graph and Runner:**

  - `orchestratorGraph.ts` will assemble all the pieces: import the agents and tools, then use LangGraph APIs to connect them. If LangGraph JS offers a class like `GraphExecutor`, we’ll create an instance with our Orchestrator as the root. We might also define here how the CEO agent chooses a manager: e.g., by calling a classification tool or by equipping it with all manager-tools. We will encapsulate complex logic here so that `agentRunner.ts` can remain clean.
  - `agentRunner.ts`: This will be the function that our API route calls. It will take the user’s input (and possibly a context object with userId, weddingId, etc.), initialize the LangGraph execution, and handle streaming the output. In pseudocode:

    ```ts
    async function runAgentConversation(userInput, context, responseStream) {
      const events = WeddingPlannerAgentGraph.run({ input: userInput, context })
      for await (const event of events) {
        // translate internal events to AG-UI events
        const formatted = formatEventForAGUI(event)
        responseStream.send(formatted)
      }
    }
    ```

    The tricky part is `formatEventForAGUI`: this will map LangGraph’s notion of events (e.g., an LLM yielding a message, or a tool call starting, or a custom event we triggered) into the JSON structure expected by AG-UI. We will follow the AG-UI spec for this format (likely something like `{type: "TEXT_MESSAGE_CONTENT", content: "...", author: "assistant"}` for partial text, or `{type: "TOOL_CALL_START", tool: "searchVendors"}` etc.). If LangGraph already supports AG-UI (maybe not natively, but possibly via some integration), we will implement this manually.
    Additionally, `agentRunner` should catch if the user aborts the connection (we might get an exception on write or a signal) and then instruct the LangGraph execution to cancel. And if the agent finishes, we ensure to close the SSE properly.

- **Tests:** We will write tests to ensure each piece works:

  - **Unit tests for tools:** using a test database (or a mocking framework to simulate Supabase calls). For example, test that `searchVendors("florist")` returns results in expected format (we might seed a local SQLite or a Supabase test schema with a sample vendor).
  - **Agent logic tests:** we can simulate the LLMs by providing fake responses. LangGraph might allow injecting a fake model that echoes or a simple rules-based model for tests. For instance, test that if Orchestrator gets input "budget" keyword, it indeed calls BudgetManager. Or test that BudgetManager given a certain budget state and a request to cut costs outputs a suggestion and calls the right tools. We can stub the OpenAI calls by forcing the agent’s chain-of-thought: (This might involve hooking into LangChain's logic if we use their testing utilities).
  - **Integration test:** using Vitest or a custom script, call the actual `agentRunner` with a known scenario and verify the sequence of events emitted. This can be done by capturing the stream in a buffer and asserting that we got e.g. TEXT_MESSAGE with some content, then TOOL_CALL for "searchVendors", then ACTION_REQUEST for "confirmVendorBooking", etc., in the correct order. We’ll simulate the user confirmation by feeding an event into the pipeline (which might be challenging in a single-run test; we may need to run the orchestrator in steps: start it, then after first part, inject a confirmation event).
  - **Persistence tests:** start a scenario, let it store something in memory table, then run a new agent call that should retrieve that memory. Verify the outcome includes knowledge from first run. This ensures our memory mechanism works end-to-end.
  - **Fallback behavior tests:** For example, have a tool function throw an error or return an unexpected result. The agent should catch it and send an error message event. Or if an agent cannot classify a query, the Orchestrator might default to some “Sorry, I can’t handle that” response. We will simulate such cases by controlling the model outputs or tool outputs in test, verifying that the system responds with a polite failure and doesn’t crash.

We'll integrate these tests into the monorepo’s pipeline (the project already uses Vitest and likely has a test runner configured). Ensuring the agent system has thorough tests will give confidence before enabling it for real users.

**Integration into Bun/Vercel:**

- During development, running `bun dev` will build both the web and agent-system apps. The API route under `apps/web` will import from `apps/agent-system` to execute the logic. We must ensure that our code is Edge-runtime friendly if we want to deploy to Vercel Edge Functions (e.g., avoid Node-specific modules like fs, and use Web APIs for fetch, etc.). Using the Supabase Edge client and `fetch` for OpenAI will keep us compatible. If any part must run in a Node context (perhaps vector operations or openai library), we can mark the route as a Node.js function (not strictly edge). But since low latency is nice for streaming, we’ll aim for edge if possible.
- On Vercel, we will configure environment variables for the API keys (OpenAI, Anthropic, etc., as per the `.env.local` and Clerk integration). The `agent/route.ts` will have access to those via process.env or the edge runtime’s `env`.
- The Supabase connection details (URL, service role key) will also be provided in environment (already likely present for the existing app). We will use the service role key carefully (only server side) for unrestricted DB access as needed. Alternatively, if using Supabase Edge Functions, we might not need to expose that – but since likely we keep this in Next for simplicity, service role is fine for server.
- We will make sure to **not** bloat the edge bundle: e.g., LangChain can be heavy, but LangGraph is relatively low-level. We might selectively import just what’s needed. If size becomes an issue, an alternative is to deploy the agent system as a separate **Supabase Edge Function** (written in TypeScript for Deno). That is an option if we find Next’s environment limiting. In that scenario, the front-end would call the Supabase function endpoint instead of a Next API route. The architecture remains same, just deployment target differs. We’ll evaluate this, but initially a Next API route on Vercel should suffice and simplify integration with CopilotKit (which expects just an endpoint URL).

**Modularity and Future Extensions:**
The system is built such that adding a new Manager agent or tool is straightforward. For example, if we later introduce an **“Event Design Manager”** for decor and theme suggestions, we’d create a new agent file with its prompt and any specialized tools (maybe connecting to a decor inspiration API), then include it in the orchestrator’s routing logic. The Crew-like configuration of agents (goals & tools) can also be externalized. We might maintain a JSON/YAML (or TS config) listing each agent’s name, description (goal), and list of tool names it can use. This way, the orchestration code can load that and create agents dynamically. It’s similar to how **CrewAI** frameworks let you define team members. Even if not using CrewAI directly, our design borrows that clarity: each “crew member” (agent) has a defined role and a toolkit. This could allow an interface in the future where the user or dev can toggle agents on/off or tweak their goals without code changes.

We will also keep our **prompts and logic separated** for maintainability. For instance, the text of the system prompts for each agent could live in separate markdown or template files. This makes iteration on prompt wording easier (especially if we do prompt engineering to improve behavior). It also aligns with having a spec and ensures documentation and implementation stay in sync.

## Example Interaction Flows and Module Boundaries

To solidify understanding, here are a few **end-to-end examples** demonstrating data flow through the system, along with notes on which modules are involved at each step:

### Scenario 1: Budget Adjustment Suggestion (Domain Query)

**User:** _“We’re currently over budget. What can we do to reduce our expenses?”_

- **UI Layer:** The user types this into the chat. The CopilotKit `ChatUI` component packages the message and sends it via fetch POST to `/api/agent` along with the session auth. The chat UI shows the user message and a loading spinner for the assistant.
- **API Endpoint (agent/route.ts):** Receives the request. It verifies the user (e.g., checks auth token, determines `weddingId` from user’s session or query). It then calls `agentRunner.runAgentConversation(userInput, {weddingId, userId})`, passing along a streaming response object.
- **Orchestrator Agent:** Inside `agentRunner`, we initialize the LangGraph graph. The Orchestrator node (CEOAgent) is invoked with the user’s query. Possibly, it uses an OpenAI GPT-4.5 call to analyze the query. Its instructions make it decide which manager to use. The query clearly mentions “over budget” => likely Budget domain. The Orchestrator could either directly route based on a keyword match or (if we implemented with function calling) GPT might output a function call `invokeBudgetManager` with argument “reduce expenses”.

  - _Module boundary:_ This decision happens in `orchestrator.ts` (the logic or prompt of CEO agent). If rule-based, it’s coded there. If AI-based, it’s in the model’s response. Either way, control transitions to BudgetManager agent.

- **Budget Manager Agent:** Now the BudgetManagerNode is activated with the task: “find ways to reduce expenses because we are over budget.” Its LLM (e.g., GPT-4.5 or possibly a cheaper LLM model if configured for this agent) receives its system prompt (role as Budget expert) plus the user query and any additional context. Likely, we’ve also prefixed the prompt with current budget info from the DB. Suppose the budget data shows total budget \$50k, current projected spend \$55k, with Catering \$5k over, Venue on target, etc. We include that in the prompt (or the agent can call a tool to get it). Let’s say we have the agent call `getBudgetStatus` as its first step (most likely).

  - The agent’s chain-of-thought (CoT) decides: “I need to see where the overspending is. I will use getBudgetStatus.”
  - **Tool Call (getBudgetStatus):** This triggers an event in LangGraph. `budgetTools.getBudgetStatusFn` runs (in `budgetTools.ts`), querying Supabase. It returns the structured data of the budget.
  - **Streaming to UI:** When the tool call starts, `agentRunner` emits a TOOL_CALL_START event to UI: e.g., `{"type": "TOOL_CALL_START", "tool": "BudgetManager.getBudgetStatus"}`. The CopilotKit UI might show a small message like “BudgetManager is retrieving current budget...”. (We can customize how these appear, or even hide them if too technical, but it’s good for transparency.) The user thus knows the AI is fetching data.
  - After a very short time, the tool returns. `agentRunner` then emits TOOL_CALL_END (with maybe the result or just completion). We might not show the raw data to the user, unless we want to (likely not, we’ll just move on).
  - **Agent reasoning:** The Budget Manager LLM now has the budget data in context. It sees, for example, Catering is \$15k out of \$10k budget (over by 5k). It forms a plan: maybe reduce catering or find cheaper vendor, or cut guest count. It decides the biggest impact is catering. It crafts an answer for the user suggesting reducing catering costs. Perhaps it wants to use another tool: if it knows a vendor’s cost per guest, it could compute savings for removing 10 guests or switching a vendor. Let’s say it simply uses reasoning and decides to suggest cutting 10 guests to save, e.g., \$100 per person -> \$1000 saved (just hypothetical). This calculation can be internal or via a simple code tool. For realism, maybe it calls `suggestSavings` which we implemented to analyze budget items. Or it might not use another tool at all, just reason with the data it got.
  - **Drafting answer:** The agent begins writing its answer: “Your wedding is currently \$5,000 over budget. The biggest overage is in Catering. I suggest considering reducing your guest count by about 10, which could save approximately \$1,000. Additionally, we could look at the floral budget…”. As it formulates this text, those tokens stream out as TEXT_MESSAGE_CONTENT events. The UI displays this partial answer in the chat in real time (the user sees the message appearing).
  - Suppose the agent also wants to be proactive and offer to apply changes. It might append something like: “_I can automatically adjust your catering budget based on 10 fewer guests if you’d like._”. This hints at an action. It then decides to actually call an action tool to prompt the user. Perhaps it calls `requestConfirmation` with a message “Apply reduction of 10 guests to catering budget?”.
  - **Confirmation Action:** The agent triggers our `uiActions.confirmBudgetCut(10, amountSaved)` function, which emits an ACTION_REQUEST event: `{"type": "ACTION_REQUEST", "action": "confirmBudgetChange", "params": {"guestsToCut": 10, "estimatedSavings": 1000} }`. We have a CopilotKit action registered for "confirmBudgetChange".
  - **UI Modal:** The front-end receives this event. Our `useCopilotAction` hook for "confirmBudgetChange" is invoked. We defined it to, for example, set a state that shows a Modal: “Ella recommends cutting 10 guests to save \$1000 on catering. Do you want to update the budget accordingly?” with \[Yes] \[No] buttons. The UI might also freeze the chat input to await this decision. The partial assistant answer is likely complete up to the point of asking this (maybe it included “(Awaiting your decision)” or something).
  - The user clicks "Yes". The `handler` in our useCopilotAction (front-end) will then call a function to send the confirmation back. This could either be a special HTTP call or it may simply internally mark the action as resolved which CopilotKit then turns into an `ACTION_RESULT` event sent back over the same SSE connection. Let’s assume CopilotKit sends an event `{type: "ACTION_RESULT", action: "confirmBudgetChange", result: "accepted"}`.
  - **Resume Agent:** The agent system (LangGraph) receives this and unblocks the Budget Manager agent’s waiting state. Now it knows the user agreed. So it proceeds to execute the budget update. It calls the `updateBudgetItem` tool to reduce the catering budget line accordingly (or to record that 10 guests fewer are expected). This writes to DB (e.g., updates catering guest count or budget amount). The tool returns success. The agent then finalizes its answer with something like: “I’ve updated the catering budget to reflect 10 fewer guests. You should now be within your target budget. ✅” (It might include a check mark or some symbol to indicate success). These final tokens stream out as part of the TEXT_MESSAGE_CONTENT.
  - Concurrently, we also emit a STATE_DELTA or toast event to inform the UI of the change: e.g., `{"type": "ACTION_REQUEST", "action": "showToastMessage", "params": {"message": "Catering budget updated (-10 guests)"}}`. The front-end shows a brief toast confirming the action. If the Budget page is open, the user might see the numbers change (either via the toast or because the underlying data changed – if we have a React state hooked to Supabase data, that would refresh too).

- **Completion:** The Budget Manager is done and returns control to Orchestrator. Orchestrator doesn’t have more to add, so it might just end. `agentRunner` sends a final `{type: "COMPLETE"}` event and closes the SSE. The UI re-enables the chat input and possibly logs the interaction. The conversation in the UI now has: User question, assistant answer (with all the explanation and the note that it updated the budget). The user also got a toast and the actual budget is updated in the system.

**Boundary summary:** The Orchestrator handled routing, BudgetManager + tools handled logic and DB updates, UI action was handled by CopilotKit’s integration. All modules worked in concert: `budgetTools.ts` for DB ops, `uiActions.ts` for the confirm modal event, `agentRunner` for streaming, `ChatUI` and `useCopilotAction` on front-end for rendering and user input.

### Scenario 2: Vendor Booking with User Approval (Multi-agent, multi-step)

**User:** _“Find a florist in my area under \$1000 and book them for our wedding.”_

- **Orchestrator:** Detects vendor-related request (“florist” and “book” keywords). It delegates to **Vendor Manager**. (If the query also implies budget, Orchestrator might also involve Budget Manager to cross-check budget, but let’s assume the user gave a price limit so it’s straightforward vendor task).
- **Vendor Manager:** Starts with the user request and likely context like wedding date and location (the orchestrator or our system prompt injects “Wedding date: 2025-09-10; Location: Austin, TX”). The agent will:

  1. Use the `searchVendors` tool (category: florist, maxPrice: \$1000, location: Austin).

     - This hits the vendor Supabase (or an API) and returns say 3 vendors: `[ {id:1, name:"Rose & Co", price:900}, {id:2, name:"Bloom", price:1200}, {id:3, name:"Florals Inc", price:800} ]`.
     - UI sees TOOL_CALL_START (“Searching vendors...”) and TOOL_CALL_END events.

  2. The agent gets the list. It sees one vendor is over budget (\$1200), so likely discards that. Now maybe two options: Rose & Co (\$900) and Florals Inc (\$800). Suppose Rose & Co has good reviews, the agent chooses that as the preferred.
  3. The agent decides to confirm with the user which to book (maybe it should offer the choice instead of unilaterally picking). So it could respond with a message: “I found a couple of florists under \$1000: _Rose & Co._ at \$900 and _Florals Inc._ at \$800. Rose & Co has excellent reviews. Would you like to book Rose & Co for your wedding?”

     - It streams this text as a message.
     - Then it triggers an action for confirmation: `{"type": "ACTION_REQUEST", "action": "confirmVendorBooking", "params": {"vendorName": "Rose & Co", "price": 900, "date": "2025-09-10"} }`.
     - The chat UI shows the message listing options and then perhaps directly shows a modal “Book Rose & Co for \$900 on Sep 10, 2025?” \[Yes/No]. (We could also design it to list both options with buttons like “Book Rose & Co” and “Book Florals Inc” – but that complicates agent logic; simpler is one suggestion at a time. Alternatively, the agent might ask in text “Reply 1 or 2 to choose”, but using UI actions is more user-friendly).

  4. User clicks Yes to book Rose & Co. CopilotKit sends the confirmation back as before.
  5. Vendor Manager now moves to perform the booking. Possibly, it requires a series of steps:

     - It might spawn an Associate agent (say **BookingAgent**) to handle the actual booking transaction. Rationale: the Vendor Manager’s job was to find options and get approval; executing the booking might involve interacting with an external API or sending an email, which could be a separate concern. By using an Associate, we encapsulate that.
     - The Vendor Manager calls something like `BookingAgent.start(vendorId=1, details…)`. This Agent might use a template email or an API call tool:

       - If an API is available (maybe the vendor directory has an endpoint to create a booking request), the Associate calls `bookVendorAPI(vendorId, userContact, weddingDetails)`. Suppose it returns success = true.
       - If no API, the Associate might use an email-sending tool or even draft an email via LLM. For now, assume a direct booking entry is made in our DB (like writing to `vendor_bookings` table).

     - The Associate returns a result (e.g., “Booking confirmed with ID 123” or “Email sent to vendor for confirmation”). We capture that.
     - The Vendor Manager gets this result. It then composes the final reply to user: “Great news! I’ve booked Rose & Co for your date. 🎉 The florist will reach out to you shortly with details.” It might include a note if anything is pending (like “(This is a tentative booking until you sign the contract.)”, depending on our business logic).
     - That message is streamed as final TEXT content to the user.
     - Meanwhile, we also trigger any UI updates: we could insert the booked vendor into a “selected vendors” list in the database. The system might have a table for chosen vendors per wedding. If so, the agent (or the booking tool) would add an entry there. The UI’s Vendors page would then show Rose & Co as booked. We might also send a toast “Vendor booked!” now or rely on the chat message itself which clearly states it.

  6. The session ends. Orchestrator might notify Budget Manager to update budget usage with that \$900 expense in flowers, but that could be an automated subsequent step:

     - Actually, since the user gave a budget limit and presumably \$900 is within it, maybe we don’t adjust anything. If we wanted thoroughness: after booking, the Vendor Manager could communicate to Budget Manager (or directly update budget) that \$900 is allocated for florist. This could be done by calling `updateBudgetItem(flowers, 900)` as an additional step. This is cross-domain; in LangGraph we can allow Vendor Manager to use the budget tool or call Budget Manager as a sub-agent. That might be overkill for now.
     - Alternatively, we simply rely on the user to manually update or an overnight sync. But our system could be proactive: the memory of spending could be used next time Budget Manager is asked something.

**Module notes:** This scenario crosses multiple domains (Vendor and potentially Budget). It shows how sub-agents (BookingAgent) operate. Each of those agents (VendorManager and BookingAgent) would be defined in our `agents` module. Tools used: `searchVendors` from `vendorTools.ts`, possibly `bookVendor` from same or from `associateAgents.ts` if specialized. UI actions: `confirmVendorBooking` from `uiActions.ts` which ties to a front-end modal (as per the dev example, we saw how an action can open a modal with a handler that sets state). The streaming events and confirmations are handled by the same parts of the pipeline as Scenario 1.

This example highlights the system’s ability to do multi-step workflows with human approval mid-way, which is made seamless by AG-UI’s event stream approach (no new connection needed, it’s one continuous interaction). It also shows integration with external systems (vendor DB/API) done through tools.

### Scenario 3: Guest RSVP Update (Quick tool use with UI feedback)

**User (Planner) says:** _“Mark Emily Chen as attending the Johnson wedding.”_ (This presumably is entered in a chat context where the planner has selected the Johnson wedding project).

- **Orchestrator:** Recognizes a guest management intent (keywords “attending”, a person’s name). It routes to Guest Manager.
- **Guest Manager:** Takes the instruction. It will likely:

  1. Call `findGuestByName("Emily Chen")` or directly `updateRSVP(name="Emily Chen", status="attending")`. If we assume unique names per wedding, it could do in one step, but safer: search first.
  2. Suppose `findGuestByName` returns Emily’s guestId and current status (say she was invited but no response).
  3. The agent then calls `updateRSVP(guestId, "attending")` tool. This updates the `guests` table in Supabase.
  4. The tool returns success (maybe with the updated guest record).
  5. The agent doesn’t really need complex reasoning here – it’s a straightforward operation. It prepares a response like: “Emily Chen has been marked as attending.” It might add a little flourish like “✅ I’ve updated her RSVP.” That text streams out as the assistant’s message.
  6. Additionally, the agent triggers a small UI action: a toast confirmation. It calls `emitEvent("showToast", "RSVP updated: Emily Chen attending")` (via our `showToastMessage` action). The front-end receives that and displays a brief toast notification at the bottom, which is a nice confirmation for the planner. 【This is optional since the chat already says it, but to mimic how a manual click would show a toast, we include it.】
  7. The UI’s guest list (if open) would also update. Because we used Supabase to change the data, any listener or the next time the UI fetches guests, Emily’s status is now “attending”. If the UI uses real-time subscriptions on the `guests` table, it could even update live. If not, the toast plus chat message suffice to inform the user, and they’ll see the change on refresh.

- **No confirmation needed** since this is a user-initiated direct update (and not a critical or irreversible action; presumably planners can freely mark attendance). The whole interaction happens in one shot. The chat output is short and factual.

This scenario shows a simple query where the agent essentially acts as an intelligent API for the user. The benefit over the user clicking themselves is minor here, but in context, it could be part of a larger conversation (maybe the user asked “Who hasn’t RSVP’d yet?” then as follow-up, “Mark Emily as attending, I got her response offline.” – the AI can handle both QA and action).

**Module boundaries:** The GuestManager uses `guestTools.findGuest` and `guestTools.updateRSVP`. Those use Supabase calls in `guestTools.ts`. The result is communicated via chat text (from agent’s prompt templates) and a UI toast via `uiActions`. This is fairly straightforward and would be one of the first things to test as it involves all layers but minimally.

---

These examples illustrate how different types of user requests are handled by the system, emphasizing **which agent is responsible**, **which tools are invoked**, and **how the UI responds** at each step. They also demonstrate **modular boundaries** in action:

- The **Orchestrator** module decides where to send tasks.
- The **Manager agent modules** encapsulate domain-specific logic and coordinate any subtasks or tools for that domain.
- The **Tools modules** perform concrete operations on the database or external APIs.
- The **UI integration (CopilotKit)** module carries out front-end side effects (modals, toasts) when prompted by agent events.
- The **Memory/State layer** quietly underpins these flows by providing data (like budget figures or guest lists) to agents and recording new info (like decisions made or tasks added) for future reference.

Finally, throughout development we’ll ensure all these components work harmoniously within the **Bun + Next.js + Supabase stack**. Bun’s fast runtime will speed up local iteration, and our code avoids Node-only dependencies, making deployment to Vercel (which now supports Edge and Node runtimes) smooth. We’ll leverage Vercel’s logging for the API route to debug any issues in the agent runs in production. Supabase will give us a consolidated place to observe the “thinking” of the agent (e.g., we might log each agent’s final thought or each tool usage in a table for debugging). This observability is crucial for an AI system – we can add a feature to log full conversations or agent decision traces to a secure location (perhaps using Supabase log tables or an external logging service). That way, if the AI does something odd, developers can review the chain of events after the fact (with user permission).

In summary, this design will empower the VL Wedding Planner app with an AI-driven copilot that can not only chat and give advice, but also directly interact with the app’s data and interface. By structuring it with LangGraph and modular agents, we ensure the system is scalable, testable, and extensible – ready to incorporate more AI capabilities (like image generation for the vision board, or automated vendor communications) down the line. All these enhancements are implemented in a way that keeps the **human user in control**, with the AI acting as a smart assistant orchestrating the heavy lifting in the background. The integration with AG-UI/CopilotKit guarantees that the user experience remains interactive and engaging, bridging the gap between **agent automation and user-facing application** in real time.

## Testing and Validation

To guarantee reliability and safety of this agent system, we will implement a comprehensive testing strategy:

- **Unit Tests for Tools and DB Operations:** Each tool function (budget updates, vendor search, etc.) will have unit tests. Using a combination of **Vitest** (as the project is already set up with it) and perhaps a mock database, we will simulate inputs and verify outputs. For example, we’ll test `getBudgetStatusFn` with a fake DB response to ensure it returns the correct JSON shape to the agent. We’ll test `updateRSVP` to ensure it correctly updates a guest’s status and handles edge cases (like guest not found – should the tool throw an error or return a message for the agent?). For vendor search, we might mock the Supabase call to return a known list and see that our filtering logic (if any) works (e.g., ensure it filters by price).

- **Agent Behavior Tests (without LLM):** We will use dependency injection or monkey-patching to simulate the LLM outputs in specific scenarios. For instance, to test the Orchestrator routing logic, we can stub the Orchestrator’s decision to always choose a certain manager based on a fake “classification”. If using the actual LLM for tests is not feasible (due to cost or nondeterminism), we can abstract the decision logic behind an interface that we control in tests. Similarly, for a Manager agent, we can simulate its decision-making: e.g., fake that after receiving budget data it decides on certain suggestions, then ensure it calls the expected sequence of tools. LangGraph might have a testing mode or we can run the graph step by step manually in tests. The goal is to verify that, given certain conditions (like “over budget by \$5000 in catering”), the Budget Manager’s sequence of tool calls and actions matches expectations (it should call getBudgetStatus, then possibly suggestSavings or produce an answer referencing catering).

- **End-to-End Simulation with Stubbed LLM:** For more integrated tests, we might create a lightweight fake LLM model that follows a script. For example, a dummy model that, upon prompt containing a certain trigger, outputs a fixed function call. There are libraries or techniques to intercept OpenAI calls and return a prewritten completion. We could feed the orchestrator a prompt “something about budget” and have our fake GPT return a function call to BudgetManager. This way we can test the entire LangGraph flow including cross-agent transitions without calling the real API.

- **AG-UI Event Stream Tests:** We will simulate an entire conversation in a test environment by invoking the API handler function with a test request and capturing the stream of events. This might involve spawning a separate thread or using Node streams in test. We’ll verify:

  - That the sequence of events respects the protocol (e.g., starts with possibly some metadata or immediately with assistant content, tool events occur in order, ends with a COMPLETE).
  - That content is properly chunked (especially partial messages).
  - That upon a simulated user confirmation, the agent’s flow resumes and finishes.
    We might do this for a simple scenario like “Add guest” since it’s one-turn, and a complex one like “book vendor” where we simulate the user confirmation by injecting an action result at the right time.

- **Human-in-the-Loop Path Tests:** Specifically, we will test scenarios where the user says “No” to a confirmation or provides unexpected input. For example, if the agent asks “Should I do X?” and the user responds “No, do Y instead” (which might come as a new user message rather than just a yes/no click if they type it). The system should handle that gracefully – perhaps the orchestrator interprets a “No” as cancellation of that action. We should ensure the agent doesn’t proceed with the action and acknowledges the cancellation. This might be a bit tricky since our flow assumes useCopilotAction captures structured responses; but we should consider the possibility of the user intervening via chat message instead of the modal. We’ll document and possibly guard against that (maybe disable chat input during a modal prompt to force using the buttons).

- **Error and Fallback Tests:** We will intentionally cause failures to test fallback mechanisms:

  - Make a tool fail (throw an exception or return error code). For instance, simulate the vendor API being down. The Vendor Manager agent should catch that (maybe via try/catch around tool call) and then send the user an apologetic message or suggest trying later. We expect an ERROR event or a gracefully completed message like “I’m sorry, I couldn’t reach the vendor database right now. Let’s try again in a bit.”. We’ll assert that no unhandled exception bubbles up to crash the stream.
  - Simulate LLM failure or timeout. We can mock the OpenAI API to not respond or return a malformed response. Our agentRunner should detect a timeout (we’ll set a reasonable timeout on each model call, say 30 seconds). If triggered, we should abort that agent’s step and send an error event to UI. Possibly the orchestrator can step in: e.g., if a Manager doesn’t respond, Orchestrator could say “Sorry, I’m having trouble with that request right now.”. We’ll test that path.
  - Multi-model fallback: If configured to use e.g. GPT-4.5 primarily, we might set up a fallback to GPT-4o for when 4.5 is at capacity. Testing this might be just ensuring our code tries the alternate model key. (This could be part of config tests: ensure that if `ANTHROPIC_API_KEY` is provided, the system can use Claude as backup if OpenAI fails, as hinted by codebase multi-provider setup.)

- **Performance Tests:** Not exactly unit tests, but we will do some runs to ensure the system responds in a timely manner. We’ll profile how long common interactions take (since streaming, user will see partial output quickly, but we want full completion ideally in a few seconds for most tasks). If we identify slow points (maybe vector search or external API), we might add caching or prefetching. For example, caching vendor search results for a session so that if user asks similar queries it’s faster.

- **Compatibility Tests:** We will run the agent system in both Node and Edge runtime locally (Bun dev likely uses Node compatibility or Bun’s Node API, which is close to Node). We’ll verify that nothing crashes in Edge mode – for example, ensure we’re not using `fs` or `net` modules. Also test in a staging environment on Vercel if possible, with the SSE working over the network (CORS, auth all correct). Perhaps a staging Vercel deployment or a Supabase function invocation to ensure our streaming code works outside local. We’ll use Clerk test accounts to simulate the auth context if needed.

- **User Acceptance Tests:** Although not automated, we should run through the key scenarios (like the 3 described) in the actual UI with a test account. This will validate the end-to-end experience:

  - The chat interface displays messages properly, modals pop up at right times, actions complete and reflect in the UI data.
  - The memory persistence can be checked: e.g., after a few interactions, maybe inspect the Supabase `agent_memory` table to see entries, or ask a follow-up question that relies on memory and see it succeed.
  - Edge cases like asking something outside the defined domains: If user asks a completely unrelated question (“What’s the weather tomorrow?”), our orchestrator might not have a domain. We should decide how to handle that (maybe a default fallback agent that tries a general answer but likely we’ll just respond “I’m here to help with wedding planning specifics.”). We’ll test such an out-of-scope query to ensure it doesn’t break the system or produce a nonsense answer.

By covering these tests, we ensure the system meets quality standards before launch. The hierarchical agents should each behave within their remit, and not do anything crazy like the Budget Manager trying to answer vendor questions (we enforce this by tools and prompt). If something does go awry, our modular design and logging will help pinpoint which agent or tool misbehaved.

We will also include **telemetry/logging** in production as a safety net: using perhaps Supabase logs or a simple table where every event or significant action is recorded. This helps in debugging live issues and evaluating agent performance. For example, we can log each user query along with which manager was invoked and the outcome (success, failure, time taken). This data can inform future optimization (like if Vendor searches are slow or Budget suggestions often need user clarification, etc.). It will also let us catch any unintended outputs to the user: since all output goes through our pipeline, we could filter or review messages to ensure nothing violates policies or user expectations.

In summary, our testing plan is as robust as the implementation: verifying each part in isolation and in combination. Given that AI behavior can be variable, testing deterministic parts (tools, data handling, event emission) is crucial, and for the AI decisions themselves, we rely on prompt design and iterative manual testing, supplemented by mocking techniques to simulate scenarios. We’ll be mindful to preserve any **citations** or references in outputs (though in this system, outputs are likely original text or data-driven, not quoting external sources, so citation formatting may not apply except perhaps if the agent provides reference to vendor sources – not likely needed in this context). If needed, we ensure the agent formats any “source” info clearly (e.g., it might say “According to your budget data【source】” but since the source is our DB, we probably won’t use bracket citations to avoid confusion).

The entire design keeps in mind the **future vision** of the platform: adding more AI features (maybe an “AI Planner” that proactively checks in with suggestions, or multi-user collaboration where the agent interacts with both the couple and the planner). With LangGraph and AG-UI, we have a scalable foundation for those future expansions, while delivering immediate value in the current release.

**Sources:**

- LangGraph provides the low-level framework for building this multi-agent orchestration, supporting persistent, stateful agents and human-in-the-loop interactions.
- The AG-UI protocol from CopilotKit is used to stream JSON events (messages, tool usage, state updates) between our agent backend and the Next.js front-end in real time. This allows us to implement custom front-end actions triggered by the AI, such as modals and toasts, through React hooks like `useCopilotAction`, ensuring a rich interactive experience for the user.
