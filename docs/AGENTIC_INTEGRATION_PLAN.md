# Agentic Integration Plan

The updated implementation plan adds LangGraph‑based task orchestration, CopilotKit + AG‑UI event streaming, Supabase‑backed long‑term memory, and multi‑model LLM routing into your existing Next 15.3 + Turbopack monorepo. A CEO‑style Orchestrator agent hands work to domain‑specific Manager agents (<PERSON>, Vendor, Timeline, Guest, etc.); each Manager can spawn Associate agents for granular or parallel tasks. Agents persist state in Supabase (including pgvector embeddings) and surface UI actions through AG‑UI events handled by React hooks. The design supports open‑ended chat, human‑in‑the‑loop approvals, concurrent sub‑flows, and automatic fallback across OpenAI, Claude, and Gemini models. Below is the step‑by‑step build plan your Augment AI engineer can follow.

---

## 1. Architectural Decisions

### 1.1 Orchestration Framework

| Option           | Why we chose it                                                                                                                                                                            |
| ---------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **LangGraph JS** | Native checkpointing, pause/resume, interrupt primitives for human approval ([langchain-ai.lang.chat][1]); explicit graph API for branching & parallel nodes ([langchain-ai.lang.chat][2]) |
| CrewAI           | Kept in reserve for later A/B tests; its wrapper abstractions can be added on top if desired                                                                                               |

### 1.2 Event Transport

- **AG‑UI protocol** (CopilotKit) streams BaseEvent objects (TEXT_MESSAGE_CONTENT, TOOL_CALL_START, ACTION_REQUEST, etc.) over SSE ([docs.ag-ui.com][3], [docs.ag-ui.com][4]).
- React side uses `useCopilotAction` to register modal/toast handlers and send ACTION_RESULT back ([docs.ag-ui.com][5]).

### 1.3 Runtime & Build

- **Next.js 15.3** App Router with Turbopack for dev/build speed ([nextjs.org][6]).
- Functions run in Vercel **Edge Runtime** unless a tool requires Node APIs; otherwise deploy those tools as Supabase Edge Functions ([supabase.com][7]).

---

## 2. Repository & Module Layout

```
apps/
  agent-system/
    src/
      orchestratorGraph.ts      # LangGraph definition (entry node: CEO)
      agents/                   # Each file holds a Manager or Associate agent config
      tools/                    # DB & external‑API helpers (zod‑typed I/O)
      memory/                   # Supabase client, pgvector helpers, embedding utils
    tests/                      # Vitest suites (see §7)
```

_Root web app_ gains:

```
apps/web/
  app/api/agent/route.ts   # Streams AG‑UI events from orchestratorGraph
  components/ai/ChatUI.tsx # CopilotKit chat + action hooks
```

---

## 3. Agent Graph & Control Flow

### 3.1 Nodes

| Node                 | Responsibilities                                           | Tools Exposed                                                    |
| -------------------- | ---------------------------------------------------------- | ---------------------------------------------------------------- |
| **CEO Orchestrator** | Intent classification; delegate to Managers; merge results | `invoke<Manager>` (internal), `searchMemory`                     |
| Budget Manager       | reads budget, proposes savings, writes updates             | `getBudgetStatus`, `updateBudgetItem`, `suggestSavings`          |
| Vendor Manager       | searches directory, drafts messages, books vendors         | `searchVendors`, `checkAvailability`, `bookVendor`, `draftEmail` |
| Timeline Manager     | generates/updates tasks & milestones                       | `generateTimeline`, `addTask`, `updateTaskStatus`                |
| Guest Manager        | guest CRUD, RSVP, headcount                                | `getGuestList`, `addGuest`, `updateRSVP`, `sendInvitation`       |
| Generic Associate    | spawned with `taskDescription`; inherits parent tools      | none new                                                         |

### 3.2 Graph Definition

- Use LangGraph’s **conditional edges** for routing and **fan‑out edges** for parallel Managers when needed ([langchain-ai.lang.chat][2], [langchain-ai.lang.chat][8]).
- Enable **checkpointer** so every step is saved for later resume or audit ([langchain-ai.lang.chat][9]).
- Insert `interrupt()` before destructive tool calls; workflow pauses until AG‑UI ACTION_RESULT arrives (human‑in‑the‑loop) ([langchain-ai.lang.chat][1]).

### 3.3 Multi‑Model Strategy

- Wrap OpenAI 4‑Turbo, Claude 3‑Sonnet, and Gemini 1.5 models behind a **router utility**: choose fastest available for non‑critical tasks; fall back if primary returns a 429/5xx ([community.openai.com][10], [docs.anthropic.com][11], [ai.google.dev][12]).
- Agent config can specify `preferredModel` and `fallbacks` array.

---

## 4. Data & Memory Layer

| Table                   | Purpose                                                                            |
| ----------------------- | ---------------------------------------------------------------------------------- |
| `agent_runs`            | high‑level execution metadata                                                      |
| `agent_events`          | token stream for replay/debug                                                      |
| `agent_memory` (vector) | embeddings of decisions, summaries; indexed with **pgvector** ([supabase.com][13]) |
| Existing domain tables  | `guests`, `budget_items`, `vendors`, etc. updated directly by tools                |

Supabase **Row‑Level Security** is already enforced; service‑role client in Edge code bypasses RLS for writes while API layer ensures user ⇄ wedding scoping.

---

## 5. Front‑End Integration

### 5.1 Streaming

1. `/api/agent` returns an `EventEncoder` SSE stream ([docs.ag-ui.com][14]).
2. `ChatUI` subscribes via CopilotKit’s `HttpAgent` client ([docs.ag-ui.com][4]).
3. `TEXT_MESSAGE_CONTENT` deltas render incrementally; tool status and custom UI actions display via React hooks.

### 5.2 Custom Actions

| Action name            | React handler outcome                                |
| ---------------------- | ---------------------------------------------------- |
| `confirmVendorBooking` | Opens modal; resolves to `"accepted"` / `"rejected"` |
| `confirmBudgetChange`  | Modal with diff table; returns choice                |
| `showToastMessage`     | Non‑blocking Radix toast                             |
| `navigatePage`         | `router.push()` to supplied URL                      |

CopilotKit’s `useCopilotAction` defines schema and handler once; backend simply emits ACTION_REQUEST events ([docs.ag-ui.com][5]).

---

## 6. Supabase Edge Functions (Optional Heavy Tools)

| Function            | Triggered by        | Notes                                            |
| ------------------- | ------------------- | ------------------------------------------------ |
| `generate-image`    | VisionBoardManager  | Runs GPU model if OpenAI image fails             |
| `vendor-webhook`    | Vendor API callback | Pushes booking confirmations into `agent_events` |
| `rate-limit-shield` | All                 | Accepts `runId`, returns “allowed / retry” flag  |

Edge Functions give Deno runtime locality, fast cold starts ([supabase.com][7]).

---

## 7. Testing Strategy

### 7.1 Unit Tests (Vitest)

| Test file                     | Focus           | Example assertion                                            |
| ----------------------------- | --------------- | ------------------------------------------------------------ |
| `tools/budgetTools.spec.ts`   | DB reads/writes | `updateBudgetItem` returns new total and row exists          |
| `agents/orchestrator.spec.ts` | Routing         | Input containing “RSVP” yields `GuestManager` invocation     |
| `graph/hil.spec.ts`           | Human‑in‑loop   | Simulate interrupt; assert graph resumes after ACTION_RESULT |

### 7.2 Integration Tests

- **Stream validation:** invoke `/api/agent` with “Add John Doe to guest list”, capture SSE; expect TOOL_CALL_START→TOOL_CALL_END→STATE_DELTA order.
- **Model fallback:** stub OpenAI 429, ensure router switches to Claude.
- **Permission:** attempt cross‑wedding ID; RLS denies update.

### 7.3 End‑to‑End Cypress

- Flow: login → open AI panel → ask “Book a florist under \$1k” → approve modal → assert chat message and `/vendors` page show booked vendor.

---

## 8. Representative User Flows

| #                            | Trigger                          | Agent Path                               | Human Step                 | UI Side‑Effect               |
| ---------------------------- | -------------------------------- | ---------------------------------------- | -------------------------- | ---------------------------- |
| **UF‑1** Budget cut          | “We’re \$5k over budget”         | CEO→BudgetManager                        | Approve 10‑guest reduction | Toast + budget chart updated |
| **UF‑2** Vendor booking      | “Find florist under \$1k & book” | CEO→VendorManager→Associate              | Confirm booking            | Vendor list badge “Booked”   |
| **UF‑3** Guest update        | “Mark Emily Chen attending”      | CEO→GuestManager                         | none                       | Guest row status toggles     |
| **UF‑4** Timeline generation | “Build my 12‑month plan”         | CEO→TimelineManager → fan‑out Associates | Accept / edit plan         | 30+ tasks inserted           |

Each flow pauses on `interrupt()` when ACTION_REQUEST is emitted, then resumes after ACTION_RESULT ([langchain-ai.lang.chat][1]).

---

## 9. Deployment & Environment

| Concern           | Detail                                                                                       |
| ----------------- | -------------------------------------------------------------------------------------------- |
| **Env vars**      | `OPENAI_API_KEY`, `ANTHROPIC_API_KEY`, `GEMINI_API_KEY`, `SUPABASE_SERVICE_KEY`              |
| **Edge limits**   | Keep bundle < 1 MB; dynamic `import()` heavy SDKs (Gemini, Anthropic) only inside tool nodes |
| **Observability** | Insert Vercel edge logs + Supabase `agent_events` rows; enable trace sampling on production  |

---

## 10. Work Breakdown for Augment AI Engineer

| Seq | Deliverable                                                               | Ops Check                        |
| --- | ------------------------------------------------------------------------- | -------------------------------- |
| 1   | Scaffold `agent-system` modules & install deps                            | `bun test` passes blank suite    |
| 2   | Implement Supabase tables & pgvector migration                            | `SELECT * FROM agent_runs` works |
| 3   | Build core tools (`budgetTools`, `vendorTools`, …)                        | Unit tests green                 |
| 4   | Write Manager agents + prompts                                            | Stub model tests pass            |
| 5   | Assemble `orchestratorGraph.ts` with checkpointer                         | Routing test green               |
| 6   | Create `/api/agent` SSE handler using EventEncoder ([docs.ag-ui.com][14]) | Local stream visible in ChatUI   |
| 7   | Integrate CopilotKit actions & ChatUI                                     | Modal/Toast demos operate        |
| 8   | Add multi‑model router utility                                            | Fail‑over test green             |
| 9   | Author Vitest & Cypress suites                                            | CI all green                     |
| 10  | Deploy preview on Vercel & Supabase                                       | Live smoke test of UF‑1..UF‑3    |

---

**With these specifications your Augment AI engineer has a clear, actionable blueprint to wire LangGraph orchestration, AG‑UI streaming, Supabase memory, and multi‑model AI reasoning into the VL Wedding Planner monorepo.**

[1]: https://langchain-ai.lang.chat/langgraph/agents/human-in-the-loop/?utm_source=chatgpt.com "Human-in-the-loop"
[2]: https://langchain-ai.lang.chat/langgraphjs/how-tos/branching/?utm_source=chatgpt.com "How to create branches for parallel node execution"
[3]: https://docs.ag-ui.com/sdk/js/core/events?utm_source=chatgpt.com "Events - Agent User Interaction Protocol - docs.ag-ui.com"
[4]: https://docs.ag-ui.com/concepts/architecture?utm_source=chatgpt.com "Core architecture - Agent User Interaction Protocol"
[5]: https://docs.ag-ui.com/concepts/tools?utm_source=chatgpt.com "Tools - Agent User Interaction Protocol"
[6]: https://nextjs.org/blog/next-15-3?utm_source=chatgpt.com "Next.js 15.3 | Next.js"
[7]: https://supabase.com/docs/guides/functions?utm_source=chatgpt.com "Edge Functions | Supabase Docs"
[8]: https://langchain-ai.lang.chat/langgraphjs/how-tos/map-reduce/?utm_source=chatgpt.com "How to create map-reduce branches for parallel execution"
[9]: https://langchain-ai.lang.chat/langgraph/agents/agents/?utm_source=chatgpt.com "LangGraph quickstart - langchain-ai.lang.chat"
[10]: https://community.openai.com/t/emulated-multi-function-calls-within-one-request/269582?utm_source=chatgpt.com "Emulated multi-function calls within one request - API - OpenAI ..."
[11]: https://docs.anthropic.com/en/docs/claude-code/sdk?utm_source=chatgpt.com "SDK - Anthropic"
[12]: https://ai.google.dev/gemini-api/docs/quickstart?utm_source=chatgpt.com "Gemini API quickstart - Google AI for Developers"
[13]: https://supabase.com/docs/guides/database/extensions/pgvector?utm_source=chatgpt.com "pgvector: Embeddings and vector similarity | Supabase Docs"
[14]: https://docs.ag-ui.com/sdk/python/encoder/overview?utm_source=chatgpt.com "Overview - Agent User Interaction Protocol"
