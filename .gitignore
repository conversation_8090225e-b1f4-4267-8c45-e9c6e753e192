# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
bun.lock
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

alembic/*
alembic.ini

# Environment variables
.env

# Markdown Files
system_prompt.md

# Other
install_rooflow_conport.sh
apps/web/.next/*
.augment-guidelines

# Added by Task Master AI
dev-debug.log
# Dependency directories
node_modules/
.vscode
# OS specific
# Task files
tasks.json
tasks/ 