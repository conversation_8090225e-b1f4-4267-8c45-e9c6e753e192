import ToastContainer from '@/src/components/common/ToastContainer';
import '@/src/index.css';
import type { Metadata } from 'next';
import { Providers } from './providers';

export const metadata: Metadata = {
    title: 'Say Yes - Wedding Planner',
    description: 'An AI-powered platform for professional wedding planners.',
};

export default function RootLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    return (
        <html lang="en">
            <body>
                <Providers>
                    {children}
                    <ToastContainer />
                </Providers>
            </body>
        </html>
    );
}