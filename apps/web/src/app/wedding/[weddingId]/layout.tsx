"use client";

import LoadingSpinner from "@/src/components/common/LoadingSpinner";
import AppLayout from "@/src/components/layout/AppLayout";
import { NAV_ITEMS } from "@/src/constants";
import { useToast } from "@/src/contexts/ToastContext";
import { PlannerContext } from "@/src/contexts/WeddingContext";
import { useWeddingDataPopulation } from "@/src/hooks/useWeddingDataPopulation";
import { use, useContext, useEffect } from "react";

export default function WeddingLayout({
    children,
    params,
}: {
    children: React.ReactNode;
    params: Promise<{ weddingId: string }>;
}) {
    const plannerCtx = useContext(PlannerContext);
    const resolvedParams = use(params);
    const { weddingId } = resolvedParams; // Get weddingId from resolved params
    const { addToast } = useToast();

    useEffect(() => {
        if (plannerCtx && weddingId && weddingId !== plannerCtx.currentWeddingId) {
            plannerCtx.setCurrentWeddingId(weddingId);
        }
    }, [plannerCtx, weddingId]);

    const { isInitialLoading } = useWeddingDataPopulation({
        currentWedding: plannerCtx?.currentWedding || null,
        appMode: plannerCtx?.appMode || "dev",
        managedWeddings: plannerCtx?.managedWeddings || [],
        updateDataForCurrentWedding: plannerCtx?.updateDataForCurrentWedding || (() => { }),
        updateAIPopulatedStatusForCurrentWedding: plannerCtx?.updateAIPopulatedStatusForCurrentWedding || (() => { }),
        addChatMessageToCurrentWedding: plannerCtx?.addChatMessageToCurrentWedding || (() => { }),
        addToast: (message: string, type: string, duration?: number) => addToast(message, type as any, duration),
    });

    if (!plannerCtx || !plannerCtx.currentWedding || isInitialLoading) {
        let loadingText = "Loading Wedding...";
        if (
            plannerCtx?.currentWedding &&
            isInitialLoading &&
            !plannerCtx.currentWedding.isAIPopulated
        ) {
            loadingText =
                plannerCtx.appMode === "prod"
                    ? "Ella is personalizing this wedding portal..."
                    : "Preparing development environment...";
        }
        return (
            <div className="flex items-center justify-center h-screen">
                <LoadingSpinner text={loadingText} />
            </div>
        );
    }

    return (
        <AppLayout navItems={NAV_ITEMS} isWeddingSelected={true}>
            {children}
        </AppLayout>
    );
}