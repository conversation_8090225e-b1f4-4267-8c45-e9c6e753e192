'use client';

import SSRSafeProvider from '@/src/components/common/SSRSafeProvider';
import { ToastProvider } from '@/src/contexts/ToastContext';
import { PlannerProvider } from '@/src/contexts/WeddingContext';
import { ReactNode } from 'react';

/**
 * Providers wraps the entire application and provides the necessary
 * context to the application.
 * @param children - The children of the Providers component.
 * @returns The Providers component.
 */
export function Providers({ children }: { children: ReactNode }) {
    return (
        <SSRSafeProvider fallback={null}>
            <ToastProvider>
                <PlannerProvider>{children}</PlannerProvider>
            </ToastProvider>
        </SSRSafeProvider>
    );
}