import { NextRequest, NextResponse } from 'next/server';
import { crossDomainCoordinator } from '@agent-system/coordination/crossDomainCoordinator';

export const runtime = 'edge';

interface WorkflowRequest {
  workflowName: string;
  description: string;
  weddingId: string;
  userId: string;
  steps: Array<{
    stepName: string;
    stepOrder: number;
    agentType: string;
    inputData?: Record<string, any>;
    conditions?: Record<string, any>;
    dependencies?: string[];
  }>;
  inputData?: Record<string, any>;
  metadata?: Record<string, any>;
}

interface CoordinationRequest {
  primaryRunId: string;
  secondaryRunId: string;
  coordinationType: 'sequential' | 'parallel' | 'conditional' | 'merge';
  sharedState?: Record<string, any>;
  priority?: number;
  conditions?: Record<string, any>;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type } = body;

    switch (type) {
      case 'workflow':
        const workflowData: WorkflowRequest = body.data;
        
        // Validate required fields
        if (!workflowData.workflowName || !workflowData.weddingId || !workflowData.userId || !workflowData.steps) {
          return NextResponse.json(
            { error: 'Missing required fields: workflowName, weddingId, userId, steps' },
            { status: 400 }
          );
        }

        // Execute workflow
        const workflowId = await crossDomainCoordinator.executeWorkflow(
          {
            workflowName: workflowData.workflowName,
            description: workflowData.description,
            steps: workflowData.steps,
            metadata: workflowData.metadata
          },
          workflowData.weddingId,
          workflowData.userId,
          workflowData.inputData || {}
        );

        return NextResponse.json({ workflowId, success: true });

      case 'coordination':
        const coordinationData: CoordinationRequest = body.data;
        
        // Validate required fields
        if (!coordinationData.primaryRunId || !coordinationData.secondaryRunId || !coordinationData.coordinationType) {
          return NextResponse.json(
            { error: 'Missing required fields: primaryRunId, secondaryRunId, coordinationType' },
            { status: 400 }
          );
        }

        // Create coordination
        const coordinationId = await crossDomainCoordinator.createCoordination({
          primaryRunId: coordinationData.primaryRunId,
          secondaryRunId: coordinationData.secondaryRunId,
          coordinationType: coordinationData.coordinationType,
          sharedState: coordinationData.sharedState,
          priority: coordinationData.priority || 5,
          conditions: coordinationData.conditions
        });

        return NextResponse.json({ coordinationId, success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid type. Use: workflow or coordination' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error processing workflow request:', error);
    return NextResponse.json(
      { error: 'Failed to process workflow request' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const id = searchParams.get('id');

    switch (type) {
      case 'shared-state':
        if (!id) {
          return NextResponse.json(
            { error: 'Missing coordination ID' },
            { status: 400 }
          );
        }

        const sharedState = await crossDomainCoordinator.getSharedState(id);
        return NextResponse.json({ sharedState });

      case 'active-coordinations':
        const activeCoordinations = await crossDomainCoordinator.getActiveCoordinations();
        return NextResponse.json({ coordinations: activeCoordinations });

      default:
        return NextResponse.json(
          { error: 'Invalid type. Use: shared-state or active-coordinations' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error getting workflow data:', error);
    return NextResponse.json(
      { error: 'Failed to get workflow data' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, coordinationId, updates } = body;

    switch (type) {
      case 'shared-state':
        if (!coordinationId || !updates) {
          return NextResponse.json(
            { error: 'Missing required fields: coordinationId, updates' },
            { status: 400 }
          );
        }

        await crossDomainCoordinator.updateSharedState(coordinationId, updates);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid type. Use: shared-state' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error updating workflow data:', error);
    return NextResponse.json(
      { error: 'Failed to update workflow data' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const coordinationId = searchParams.get('coordinationId');

    if (!coordinationId) {
      return NextResponse.json(
        { error: 'Missing coordination ID' },
        { status: 400 }
      );
    }

    const success = await crossDomainCoordinator.cancelCoordination(coordinationId);
    return NextResponse.json({ success });

  } catch (error) {
    console.error('Error cancelling coordination:', error);
    return NextResponse.json(
      { error: 'Failed to cancel coordination' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
