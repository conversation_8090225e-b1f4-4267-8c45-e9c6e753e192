import { NextRequest, NextResponse } from 'next/server';
import { performanceAnalytics } from '@agent-system/analytics/performanceAnalytics';

export const runtime = 'edge';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const agentType = searchParams.get('agentType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const timePeriod = searchParams.get('timePeriod') as 'hour' | 'day' | 'week' | 'month';

    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required parameters: startDate, endDate' },
        { status: 400 }
      );
    }

    const start = new Date(startDate);
    const end = new Date(endDate);

    switch (type) {
      case 'dashboard':
        const dashboard = await performanceAnalytics.getSystemDashboard(start, end);
        return NextResponse.json(dashboard);

      case 'agent-metrics':
        if (!agentType || !timePeriod) {
          return NextResponse.json(
            { error: 'Missing required parameters for agent metrics: agentType, timePeriod' },
            { status: 400 }
          );
        }
        const metrics = await performanceAnalytics.getAgentMetrics(agentType, timePeriod, start, end);
        return NextResponse.json(metrics);

      case 'recommendations':
        const recommendations = await performanceAnalytics.generateOptimizationRecommendations(agentType || undefined);
        return NextResponse.json(recommendations);

      default:
        return NextResponse.json(
          { error: 'Invalid type parameter. Use: dashboard, agent-metrics, or recommendations' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error getting analytics:', error);
    return NextResponse.json(
      { error: 'Failed to get analytics data' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, data } = body;

    switch (type) {
      case 'metric':
        await performanceAnalytics.recordMetric(data);
        return NextResponse.json({ success: true });

      case 'satisfaction':
        await performanceAnalytics.recordSatisfaction(data);
        return NextResponse.json({ success: true });

      default:
        return NextResponse.json(
          { error: 'Invalid type. Use: metric or satisfaction' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error recording analytics data:', error);
    return NextResponse.json(
      { error: 'Failed to record analytics data' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
