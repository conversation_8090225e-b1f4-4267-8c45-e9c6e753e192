import { NextRequest, NextResponse } from 'next/server';
import { feedbackSystem } from '@agent-system/learning/feedbackSystem';

export const runtime = 'edge';

interface FeedbackRequest {
  runId: string;
  userId: string;
  weddingId: string;
  feedbackType: 'rating' | 'thumbs' | 'text' | 'correction';
  rating?: number;
  thumbsDirection?: 'up' | 'down';
  feedbackText?: string;
  correctionData?: Record<string, any>;
  agentResponseId?: string;
  contextData?: Record<string, any>;
}

export async function POST(request: NextRequest) {
  try {
    const body: FeedbackRequest = await request.json();

    // Validate required fields
    if (!body.runId || !body.userId || !body.weddingId || !body.feedbackType) {
      return NextResponse.json(
        { error: 'Missing required fields: runId, userId, weddingId, feedbackType' },
        { status: 400 }
      );
    }

    // Collect feedback
    await feedbackSystem.collectFeedback({
      runId: body.runId,
      userId: body.userId,
      weddingId: body.weddingId,
      feedbackType: body.feedbackType,
      rating: body.rating,
      thumbsDirection: body.thumbsDirection,
      feedbackText: body.feedbackText,
      correctionData: body.correctionData,
      agentResponseId: body.agentResponseId,
      contextData: body.contextData
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error collecting feedback:', error);
    return NextResponse.json(
      { error: 'Failed to collect feedback' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const agentType = searchParams.get('agentType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    if (!agentType || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required parameters: agentType, startDate, endDate' },
        { status: 400 }
      );
    }

    // Get feedback analytics
    const analytics = await feedbackSystem.getFeedbackAnalytics(
      agentType,
      {
        start: new Date(startDate),
        end: new Date(endDate)
      }
    );

    return NextResponse.json(analytics);

  } catch (error) {
    console.error('Error getting feedback analytics:', error);
    return NextResponse.json(
      { error: 'Failed to get feedback analytics' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  });
}
