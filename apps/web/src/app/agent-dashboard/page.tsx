"use client";
import AppLayout from "@/src/components/layout/AppLayout";
import AgentDashboardPage from "@/src/components/views/AgentDashboardPage";
import { PLANNER_NAV_ITEMS } from "@/src/constants";

export default function AgentDashboardRoutePage() {
    return (
        <AppLayout navItems={PLANNER_NAV_ITEMS} isWeddingSelected={false}>
            <AgentDashboardPage />
        </AppLayout>
    );
}