import { AssociateAgent } from "./AssociateAgent"

export class ManagerAgent {
  private associates: AssociateAgent[] = []
  protected domain: string

  constructor(domain: string) {
    this.domain = domain
  }

  addAssociate(associate: AssociateAgent) {
    this.associates.push(associate)
  }

  assignTask(_task: any) {
    console.log(`Task assignment not implemented for domain: ${this.domain}`)
  }

  reportStatus() {
    return {
      domain: this.domain,
      associateCount: this.associates.length,
      associates: this.associates.map((a) => ({ taskType: a.taskType })),
    }
  }

  protected getAssociates(): AssociateAgent[] {
    return this.associates
  }

  get getDomain(): string {
    return this.domain
  }
}
