import { createClient } from "@supabase/supabase-js"

// Supabase client for the vendor data project (sayyes_data)
const supabaseDataUrl = process.env.NEXT_PUBLIC_SUPABASE_DATA_URL || ""

const supabaseDataKey = process.env.NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY || ""

if (!supabaseDataUrl || !supabaseDataKey) {
  console.error(
    "Supabase Data configuration missing. Please check your environment variables."
  )
  console.log("Looking for:", {
    url: "NEXT_PUBLIC_SUPABASE_DATA_URL",
    key: "NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY",
  })
}

export const supabaseData = createClient(supabaseDataUrl, supabaseDataKey)
