import { EllaOrchestrator } from "./EllaOrchestrator";
import { BudgetManagerAgent } from "./BudgetManagerAgent";
import { TimelineManagerAgent } from "./TimelineManagerAgent";
import { GuestManagerAgent } from "./GuestManagerAgent";
import { VendorManagerAgent } from "./VendorManagerAgent";
import { VisionManagerAgent } from "./VisionManagerAgent";
import { StylingManagerAgent } from "./StylingManagerAgent";
import { AssociateAgent } from "./AssociateAgent";

// Instantiate agents
const ella = new EllaOrchestrator({});
const budgetManager = new BudgetManagerAgent();
budgetManager.addAssociate(new AssociateAgent("add-expense"));
budgetManager.addAssociate(new AssociateAgent("update-budget"));
ella.addManager(budgetManager);
const timelineManager = new TimelineManagerAgent();
timelineManager.addAssociate(new AssociateAgent("add-milestone"));
timelineManager.addAssociate(new AssociateAgent("update-timeline"));
ella.addManager(timelineManager);
const guestManager = new GuestManagerAgent();
guestManager.addAssociate(new AssociateAgent("add-guest"));
guestManager.addAssociate(new AssociateAgent("update-rsvp"));
ella.addManager(guestManager);
const vendorManager = new VendorManagerAgent();
vendorManager.addAssociate(new AssociateAgent("add-vendor"));
vendorManager.addAssociate(new AssociateAgent("update-vendor"));
ella.addManager(vendorManager);
const visionManager = new VisionManagerAgent();
visionManager.addAssociate(new AssociateAgent("add-image"));
visionManager.addAssociate(new AssociateAgent("update-board"));
ella.addManager(visionManager);
const stylingManager = new StylingManagerAgent();
stylingManager.addAssociate(new AssociateAgent("add-outfit"));
stylingManager.addAssociate(new AssociateAgent("update-style"));
ella.addManager(stylingManager);

export const crew = { orchestrator: ella, managers: [budgetManager, timelineManager, guestManager, vendorManager, visionManager, stylingManager] };
