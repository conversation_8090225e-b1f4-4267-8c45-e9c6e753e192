import { ManagerAgent } from "./ManagerAgent"

export class GuestManagerAgent extends ManagerAgent {
  constructor() {
    super("guest")
  }

  assignTask(task: any): any {
    const associate = this.getAssociates().find(
      (a) => a.taskType === "add-guest" || a.taskType === "update-rsvp"
    )
    if (associate) {
      return associate.performTask(task)
    }
    return super.assignTask(task)
  }
}
