import { createClient } from "@supabase/supabase-js"

// Support multiple environment variable formats for maximum compatibility
const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL || ""

const supabaseKey =
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ||
  process.env.SUPABASE_ANON_KEY ||
  ""

if (!supabaseUrl || !supabaseKey) {
  console.error(
    "Supabase configuration missing. Please check your environment variables."
  )
  console.log("Looking for:", {
    url: "NEXT_PUBLIC_SUPABASE_URL",
    key: "NEXT_PUBLIC_SUPABASE_ANON_KEY",
  })
}

export const supabase = createClient(supabaseUrl, supabaseKey)
