// Example for VendorManagerAgent
import { ManagerAgent } from "./ManagerAgent"

export class VendorManagerAgent extends ManagerAgent {
  constructor() {
    super("vendor") // Call parent constructor with domain
  }

  async assignTask(task: any) {
    if (task.type === "add-vendor") {
      const associate = this.getAssociates().find(
        (a) => a.taskType === "add-vendor"
      )
      if (associate) {
        return await associate.performTask(task)
      }
    }
    // Call parent implementation for unhandled tasks
    super.assignTask(task)
  }
}
