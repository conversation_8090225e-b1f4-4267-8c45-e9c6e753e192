import { ManagerAgent } from "./ManagerAgent"

export class TimelineManagerAgent extends ManagerAgent {
  constructor() {
    super("timeline")
  }

  assignTask(task: any) {
    const associate = this.getAssociates().find(
      (a) => a.taskType === "add-milestone" || a.taskType === "update-timeline"
    )
    if (associate) {
      return associate.performTask(task)
    }
    super.assignTask(task)
  }
}
