import { crew } from "./crewWorkflow"
import { supabase } from "./supabaseClient"
import { VendorManagerAgent } from "./VendorManagerAgent"

// Example: Add a new vendor for a client via agentic workflow
async function addVendorForClient(clientId: string, vendorData: any) {
  // Step 1: <PERSON> receives the task
  const ella = crew.orchestrator

  // Step 2: <PERSON> delegates to VendorManager
  const vendorManager = crew.managers.find(
    (m) => m.getDomain === "vendor"
  ) as VendorManagerAgent
  if (!vendorManager) throw new Error("VendorManager not found")

  // Step 3: VendorManager assigns to an AssociateAgent
  // Note: We can't directly access associates from outside, so we use the assignTask method
  const task = { type: "add-vendor", clientId, vendor: vendorData }

  try {
    // Step 4: Let the VendorManager handle the task assignment internally
    const result = await vendorManager.assignTask(task)

    // Step 5: Report result up the agent chain
    const status = vendorManager.reportStatus()
    ella.delegateTask({ type: "vendor-added", data: result, status })

    return result
  } catch (error) {
    console.warn(
      "Agent workflow failed, falling back to direct database operation:",
      error
    )

    // Fallback: Direct Supabase operation if agent workflow fails
    const { data, error: dbError } = await supabase
      .from("vendors")
      .insert([{ ...vendorData, client_id: clientId }])
      .select()

    if (dbError) throw dbError
    // Ensure consistent return format with agent workflow
    return { success: true, data, source: "fallback" }
  }
}

export { addVendorForClient }
