import { ManagerAgent } from "./ManagerAgent"

export class BudgetManagerAgent extends ManagerAgent {
  constructor() {
    super("budget") // Call parent constructor with domain
  }

  assignTask(task: any) {
    // Find appropriate associate for budget tasks
    const associate = this.getAssociates().find(
      (a) => a.taskType === "add-expense" || a.taskType === "update-budget"
    )
    if (associate) {
      return associate.performTask(task)
    }
    // Call parent implementation for unhandled tasks
    return super.assignTask(task)
  }
}
