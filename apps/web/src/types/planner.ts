import { AppMode } from "@/src/types/context"
import {
  AIAgentLogEntry,
  BudgetItem,
  ChatMessage,
  Guest,
  Task,
  Vendor,
  VisionBoardItem,
  WeddingDetails,
} from "@/src/types/domain"

// Represents a single wedding managed by the planner
export interface ManagedWedding {
  id: string // Unique ID for this wedding
  weddingDetails: WeddingDetails
  chatHistory: ChatMessage[]
  budgetItems: BudgetItem[]
  tasks: Task[]
  guests: Guest[]
  vendors: Vendor[]
  visionBoardItems: VisionBoardItem[]
  agentLogs: AIAgentLogEntry[] // Added for AI agent logs per wedding
  // other wedding-specific data arrays...
  isAIPopulated: boolean // AI data population status for this specific wedding
  createdAt: string // ISO date string
  workforceConfig?: WorkforceConfiguration // Per-client workforce configuration
}

// Basic profile for the wedding planner
export interface PlannerProfile {
  name?: string
  email?: string
  companyName?: string
  website?: string
  yearsExperience?: string // e.g., "0-2 years", "3-5 years", "5-10 years", "10+ years"
  specialties?: string // Comma-separated or a descriptive string
  serviceArea?: string
  // New fields for enhanced profile
  tagline?: string
  bio?: string
  servicesOffered?: string[] // Stored as an array, managed as comma-separated in UI
  pricingStructure?: string
  typicalClientBudget?: string
  socialMediaLinks?: Array<{
    platform:
      | "Instagram"
      | "Pinterest"
      | "Facebook"
      | "LinkedIn"
      | "Other"
      | string
    url: string
  }>
  // avatarUrl is handled by constants for now
}

export type EllaAutonomyLevel = "review_all" | "balanced" | "proactive"

export type AgentType = "orchestrator" | "manager" | "associate"
export type LLMModel =
  | "gemini-2.5-flash-preview-05-20"
  | "imagen-3.0-generate-002"
  | "gpt-4.1"
  | "claude-sonnet-4-0"
  | "other" // Extended model support

// Workforce Configuration Types for Phase 2
export interface AgentPerformanceMetrics {
  tasksCompleted: number
  avgResponseTime: number // in milliseconds
  successRate: number // 0-1
  costPerTask: number // in USD
  lastActiveDate?: string // ISO date string
}

export interface WorkforceAgentConfig {
  agentId: string
  isEnabled: boolean
  llmModel: LLMModel
  systemInstructionOverride?: string
  autonomyLevel: EllaAutonomyLevel
  maxConcurrentTasks: number
  connectedDataSourceIds: string[]
  customSettings: Record<string, any> // Domain-specific settings
  performanceMetrics?: AgentPerformanceMetrics
}

export interface WorkforceConfiguration {
  id: string
  weddingId: string
  name: string // e.g., "Premium Package for Alex & Jamie"
  templateId?: string // Reference to a base template
  description?: string
  agents: WorkforceAgentConfig[]
  globalSettings: {
    autonomyLevel: EllaAutonomyLevel
    costBudget?: number // Monthly budget for AI operations
    performanceThresholds: {
      minSuccessRate: number
      maxResponseTime: number
    }
  }
  dataSourceConfig: {
    globalDataSourceIds: string[] // Data sources available to all agents
    domainSpecificSources: Record<string, string[]> // Domain -> data source IDs
  }
  isActive: boolean
  createdAt: string
  lastModified: string
}

export interface WorkforceTemplate {
  id: string
  name: string
  description: string
  category: "basic" | "premium" | "enterprise" | "custom"
  defaultAgents: Omit<WorkforceAgentConfig, "performanceMetrics">[]
  defaultGlobalSettings: WorkforceConfiguration["globalSettings"]
  recommendedFor: string[] // e.g., ["small weddings", "luxury events"]
  estimatedMonthlyCost: number
  isPublic: boolean // Can other planners use this template
  createdBy?: string // Planner ID who created it
}

// New types for AI Agent Management
export interface PredefinedAgent {
  id: string
  name: string
  description: string
  icon: React.FC<React.SVGProps<SVGSVGElement>>
  defaultSystemInstruction: string
  agentType: AgentType
  parentId?: string // ID of the parent agent in the hierarchy
  defaultLlmModel: LLMModel
  capabilities?: string[] // What this agent can do
  estimatedCostPerTask?: number // Estimated cost in USD
}

export interface AgentWeddingOverride {
  isActiveForWedding: boolean
  systemInstructionOverride?: string
}

// RAG Data Source Types
export type DataSourceType = "txt" | "pdf" | "csv" | "url" | "text_input"
export type DataSourceStatus = "Ready" | "Processing" | "Error" | "Unavailable"

export interface DataSource {
  id: string
  name: string
  type: DataSourceType
  status: DataSourceStatus
  uploadDate: string // ISO date string
  contentPreview?: string // For TXT or small text_input
  originalFileName?: string // For file uploads
  size?: number // File size in bytes
  sourceUrl?: string // For URL type
  fullContent?: string // For 'text_input' or small 'txt' files
  tags?: string[] // For categorization and filtering
  isGlobal?: boolean // Available to all clients vs client-specific
}

export interface AgentConfig extends PredefinedAgent {
  // Extends PredefinedAgent to inherit base properties
  isActiveGlobal: boolean // Global activation status
  llmModel: LLMModel // Current LLM model for the agent
  // defaultSystemInstruction is part of PredefinedAgent
  weddingOverrides: {
    [weddingId: string]: AgentWeddingOverride
  }
  connectedDataSourceIds: string[] // IDs of connected DataSources
}

export interface PlannerContextType {
  isPlannerOnboarded: boolean // Minimal planner setup flag
  setIsPlannerOnboarded: (status: boolean) => void
  plannerProfile: PlannerProfile | null
  setPlannerProfile: (profile: PlannerProfile | null) => void

  managedWeddings: ManagedWedding[]
  addManagedWedding: (
    newWeddingData: Pick<ManagedWedding, "weddingDetails">
  ) => ManagedWedding // Returns the new wedding object
  updateManagedWedding: (
    weddingId: string,
    updates: Partial<ManagedWedding>
  ) => void
  deleteManagedWedding: (weddingId: string) => void

  currentWeddingId: string | null
  setCurrentWeddingId: (weddingId: string | null) => void

  currentWedding: ManagedWedding | null // Derived state

  appMode: AppMode
  setAppMode: (mode: AppMode) => void

  ellaAutonomyLevel: EllaAutonomyLevel
  setEllaAutonomyLevel: (level: EllaAutonomyLevel) => void

  geminiApiKey: string
  setGeminiApiKey: (key: string) => void

  // Functions to interact with the current wedding's data
  addChatMessageToCurrentWedding: (message: ChatMessage) => void
  clearChatHistoryForCurrentWedding: () => void
  setInitialAIBudgetForCurrentWedding: (items: BudgetItem[]) => void
  setInitialAITimelineForCurrentWedding: (items: Task[]) => void
  updateAIPopulatedStatusForCurrentWedding: (status: boolean) => void
  addAIAgentLogToCurrentWedding: (logEntry: AIAgentLogEntry) => void

  updateDataForCurrentWedding: <K extends keyof ManagedWedding>(
    key: K,
    data: ManagedWedding[K]
  ) => void

  // AI Agent Management
  agentConfigurations: AgentConfig[]
  updateAgentConfiguration: (
    agentId: string,
    configUpdate: Partial<
      Omit<
        AgentConfig,
        | "id"
        | "name"
        | "description"
        | "icon"
        | "agentType"
        | "parentId"
        | "defaultLlmModel"
      >
    >
  ) => void
  getAgentConfig: (agentId: string) => AgentConfig | undefined

  // Data Sources Management
  dataSources: DataSource[]
  addDataSource: (
    dataSource: Omit<DataSource, "id" | "uploadDate" | "status">
  ) => Promise<DataSource>
  deleteDataSource: (dataSourceId: string) => void

  // Phase 2: Workforce Configuration Management
  workforceTemplates: WorkforceTemplate[]
  addWorkforceTemplate: (
    template: Omit<WorkforceTemplate, "id" | "createdBy">
  ) => WorkforceTemplate
  updateWorkforceTemplate: (
    templateId: string,
    updates: Partial<WorkforceTemplate>
  ) => void
  deleteWorkforceTemplate: (templateId: string) => void

  // Workforce configuration for current wedding
  updateWorkforceConfigForCurrentWedding: (
    config: Partial<WorkforceConfiguration>
  ) => void
  getWorkforceConfigForWedding: (
    weddingId: string
  ) => WorkforceConfiguration | null

  // Performance tracking
  updateAgentPerformanceMetrics: (
    weddingId: string,
    agentId: string,
    metrics: Partial<AgentPerformanceMetrics>
  ) => void
  getPerformanceMetricsForWedding: (
    weddingId: string
  ) => Record<string, AgentPerformanceMetrics>
}
