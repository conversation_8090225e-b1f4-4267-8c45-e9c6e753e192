/**
 * NavItemType interface for the application.
 * @param id - The id of the NavItemType component.
 * @param label - The label of the NavItemType component.
 * @param path - The path of the NavItemType component.
 * @param icon - The icon of the NavItemType component.
 */

export interface NavItemType {
  id: string
  label: string
  path: string
  icon: React.FC<React.SVGProps<SVGSVGElement>>
}

export type ToastType = "success" | "error" | "info" | "warning"

export interface ToastMessage {
  id: string
  message: string
  type: ToastType
  duration?: number // in milliseconds
  icon?: React.FC<React.SVGProps<SVGSVGElement>> // Optional: For specific icons per toast
}
