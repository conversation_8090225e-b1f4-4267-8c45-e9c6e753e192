
export interface GenerateContentResponse {
  text: string;
  candidates?: Array<{
    groundingMetadata?: {
      groundingChunks?: Array<{ web?: { uri: string; title: string } }>;
    };
  }>;
}

export interface GenerateImagesResponse {
  generatedImages: Array<{
    image?: { // The 'image' object itself is optional on each element
      imageBytes?: string; // Base64 encoded image
    };
    // seed?: string; // Example if other properties were needed
  }>;
}