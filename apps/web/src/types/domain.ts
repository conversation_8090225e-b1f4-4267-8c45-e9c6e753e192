


export interface WeddingDetails {
  coupleNames: string;
  weddingDate: string; 
  guestCount: number;
  vibe: string;
  location: string;
  initialBudget?: number;
}

export interface ChatMessage {
  id: string;
  text: string;
  sender: 'user' | 'ella' | 'system';
  timestamp: Date;
  imageUrl?: string; 
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  dueDate?: string; 
  isCompleted: boolean;
  assignedTo?: string; // Added assignedTo field
  parentId?: string;   // For sub-task relationship
  isPlannerInternal?: boolean; // New: For tasks visible only to the planner
}

export interface BudgetItem {
  id: string;
  category: string;
  estimatedCost: number;
  actualCost: number;
  vendor?: string;
  paymentDueDate?: string;
  isPaid: boolean;
}

export interface Guest {
  id: string;
  name: string;
  rsvpStatus: 'Pending' | 'Accepted' | 'Declined';
  dietaryRestrictions?: string;
  plusOne?: boolean;
  plusOneName?: string;
  group?: string; 
}

export type VendorStatus = 
  | 'Researching' 
  | 'Contacted' 
  | 'Awaiting Quote' 
  | 'Quote Received' 
  | 'Shortlisted' 
  | 'Booked' 
  | 'Declined';

// Fix: Expanded VendorCategory to include more options based on usage and AI prompt examples.
export type VendorCategory = 
  | 'Venue' | 'Photographer' | 'Caterer' | 'Florist' | 'DJ/Band' 
  | 'Attire' | 'Stationery' | 'Transportation' | 'Cake' | 'Officiant'
  | 'Videographer' | 'Wedding Planner' | 'Hair & Makeup' | 'Favors' | 'Other';


export interface Vendor {
  id: string;
  name: string;
  category: VendorCategory;
  contactPerson?: string;
  email?: string;
  phone?: string;
  status: VendorStatus;
  quotedPrice?: number;
  actualCost?: number; 
  notes?: string;
}

export interface AIAgentLogEntry {
  id: string;
  timestamp: Date;
  agent: 'Ella' | 'Venue Manager' | 'Vendor Manager' | 'Guest Manager' | 'Budget Manager' | 'Styling Manager' | 'Timeline Manager' | 'Associate Agent' | 'System';
  action: string;
  details?: string;
}

export interface LookItem {
  id: string;
  category: 'dress' | 'suit' | 'accessories' | 'hair' | 'makeup';
  description: string;
  imageUrl: string; 
  vendorLink?: string;
}

export interface VisionBoardItem {
  id: string;
  imageUrl: string;
  caption?: string;
  tags?: string[];
}