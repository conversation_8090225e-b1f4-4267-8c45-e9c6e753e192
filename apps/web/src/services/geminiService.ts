import {
  AIAgentLogEntry,
  AppMode,
  BudgetItem,
  ChatMessage,
  EllaAutonomyLevel,
  Guest,
  ManagedWedding,
  Task,
  GenerateContentResponse as UIGenerateContentResponse,
  GenerateImagesResponse as UIGenerateImagesResponse,
  Vendor,
  VendorCategory,
  VisionBoardItem,
  WeddingDetails,
} from "@/src/types/index"
import { getGeminiApiKey, getGeminiApiKeySource } from "@/src/utils/apiKeyUtils"
import { parseDateStringToLocalMidnight } from "@/src/utils/dateUtils" // Added import
import { generateClientSideId } from "@/src/utils/idUtils"
import { GoogleGenAI } from "@google/genai"

const NODE_ENV =
  typeof process !== "undefined" && process.env && process.env.NODE_ENV
    ? process.env.NODE_ENV
    : "development" // Default to 'development' if not set

// Initialize AI instance dynamically
const getGeminiAI = (): GoogleGenAI | null => {
  const API_KEY = getGeminiApiKey()

  if (!API_KEY) {
    return null
  }

  try {
    return new GoogleGenAI({ apiKey: API_KEY })
  } catch (error) {
    console.error("Error initializing GoogleGenAI SDK with API Key:", error)
    if (NODE_ENV === "production") {
      console.error(
        "FATAL: AI SDK failed to initialize in production due to API key issue."
      )
    }
    return null
  }
}

const TEXT_MODEL = "gemini-2.5-flash-preview-04-17"
const IMAGE_MODEL = "imagen-3.0-generate-002"

export { getGeminiAI }

export const isAIAvailable = (): boolean => {
  const ai = getGeminiAI()
  return ai !== null
}

export const shouldUseMockData = (appMode: AppMode): boolean => {
  return appMode === "dev" || !isAIAvailable()
}

// Export function to get current API key source for debugging
export const getApiKeySource = (): "user" | "environment" | "none" => {
  return getGeminiApiKeySource()
}

const mockDelay = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms))

const parseJsonResponse = <T>(text: string, fallback: T): T => {
  let jsonStr = text.trim()

  // Attempt to extract from markdown fence if present
  // More flexible regex for fences: ```json ... ``` or ``` ... ```
  const fenceRegex = /```(?:\w*\s*\n)?([\s\S]*?)\n?\s*```/
  const fenceMatch = jsonStr.match(fenceRegex)

  if (fenceMatch && fenceMatch[1]) {
    jsonStr = fenceMatch[1].trim()
  } else {
    // If no markdown fence, try to find the first '{' or '[' and its corresponding '}' or ']'.
    // This helps if the JSON is embedded within other text without proper fences.
    let firstChar = ""
    let lastChar = ""
    let startIndex = -1
    let endIndex = -1

    const firstBracket = jsonStr.indexOf("[")
    const firstBrace = jsonStr.indexOf("{")

    // Determine if an array or object is the likely outermost structure
    if (
      firstBracket !== -1 &&
      (firstBrace === -1 || firstBracket < firstBrace)
    ) {
      startIndex = firstBracket
      firstChar = "["
      lastChar = "]"
    } else if (
      firstBrace !== -1 &&
      (firstBracket === -1 || firstBrace < firstBracket)
    ) {
      startIndex = firstBrace
      firstChar = "{"
      lastChar = "}"
    }

    if (startIndex !== -1) {
      let balance = 0
      let inString = false
      for (let i = startIndex; i < jsonStr.length; i++) {
        const char = jsonStr[i]
        if (char === "\\" && i + 1 < jsonStr.length) {
          // Handle escaped characters
          i++ // Skip next character
          continue
        }
        if (char === '"') {
          inString = !inString
        }
        if (!inString) {
          // Only adjust balance if not inside a string
          if (char === firstChar) {
            balance++
          } else if (char === lastChar) {
            balance--
          }
        }
        if (balance === 0 && i >= startIndex) {
          // Found the end of the outermost structure
          endIndex = i
          break
        }
      }
      if (endIndex !== -1) {
        jsonStr = jsonStr.substring(startIndex, endIndex + 1)
      }
      // If endIndex is not found (imbalanced structure), jsonStr remains as is,
      // and parsing will likely fail, which is handled by the catch block.
    }
  }

  try {
    return JSON.parse(jsonStr) as T
  } catch (e) {
    const originalTextSample =
      text.length > 500 ? text.substring(0, 497) + "..." : text
    const attemptedParseSample =
      jsonStr.length > 500 ? jsonStr.substring(0, 497) + "..." : jsonStr
    console.error(
      "Failed to parse JSON response. Error:",
      e,
      "\nAttempted to parse:",
      attemptedParseSample,
      "\nOriginal full text (sample):",
      originalTextSample
    )
    return fallback
  }
}

// Updated onboarding responses for Planner context
const plannerOnboardingEllaResponses: { [key: number]: string } = {
  0: "Hello there! I'm Ella, your personal AI wedding assistant. I'm excited to help you manage your client's big day! To start, could you tell me the names of the happy couple for this new wedding entry?",
  1: "That's wonderful! And when are they thinking of having the wedding? A specific date, or perhaps a season and year?",
  2: "Lovely! Roughly how many guests are they planning to invite?",
  3: "Great! How would you describe the vibe or style they're dreaming of for their wedding? (e.g., romantic, modern, rustic, boho, classic)",
  4: "Sounds amazing! Do they have a specific location or city in mind, or are you still exploring options with them?",
  5: "Perfect! One last thing for now - do they have an initial budget idea in mind? Even a rough range is helpful, but no worries if not!",
  6: "Thank you so much! I have a clearer picture for this client. I'm preparing a 'Vision Snapshot' for them. It'll be ready in a moment! In the meantime, I'm setting up their Wedding Portal within your planner dashboard. Get ready for a seamless planning experience for this client! ✨",
}

const getPlannerEllaOnboardingResponse = (
  step: number,
  userInput?: string
): string => {
  if (step === 1 && userInput)
    return `Great, ${userInput}! It's a pleasure to start planning for them. When are they thinking of having the wedding? A specific date, or perhaps a season and year?`
  return (
    plannerOnboardingEllaResponses[step] ||
    "I'm gathering the details for this new wedding entry... Just a moment!"
  )
}

export const fetchPlannerEllaOnboardingReply = async (
  step: number,
  userInput?: string,
  currentDetails?: Partial<WeddingDetails>,
  appMode: AppMode = "dev"
): Promise<ChatMessage> => {
  const ai = getGeminiAI()
  if (appMode === "dev" || !ai) {
    await mockDelay(1000 + Math.random() * 1000)
  }

  let text = getPlannerEllaOnboardingResponse(step, userInput)

  if (step === 6 && currentDetails) {
    text = `Thank you! For your clients, ${
      currentDetails.coupleNames || "this couple"
    }, based on a ${currentDetails.vibe || "lovely"} vibe for around ${
      currentDetails.guestCount || "their"
    } guests in ${currentDetails.location || "their chosen location"} around ${
      currentDetails.weddingDate || "their date"
    }, I'm so excited!

I'm preparing a 'Vision Snapshot' for them. It'll be ready in a moment! In the meantime, I'm setting up their Wedding Portal within your planner dashboard.`
  }

  return {
    id: `ella-planner-onboarding-${Date.now()}`,
    text: text,
    sender: "ella",
    timestamp: new Date(),
  }
}

export const generateVisionSnapshot = async (
  details: WeddingDetails,
  appMode: AppMode = "dev"
): Promise<ChatMessage> => {
  const coupleNames = details.coupleNames || "the couple"
  const vibe = details.vibe || "beautiful"
  const ai = getGeminiAI()

  if (appMode === "prod" && ai) {
    try {
      const prompt = `Generate a single, evocative image representing a wedding vision. Details: Couple ${coupleNames}, Vibe: ${vibe}, Location hints: ${details.location}, Guest count: ${details.guestCount}, Date/Season: ${details.weddingDate}. Focus on a beautiful, inspiring wedding scene.`
      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: prompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        sdkResponse.generatedImages &&
        sdkResponse.generatedImages.length > 0
      ) {
        const firstImage = sdkResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          return {
            id: `vision-${Date.now()}`,
            text: `Here's a sneak peek of the vision for ${coupleNames}! A little something to get the inspiration flowing for their ${vibe} wedding. ✨`,
            sender: "ella",
            timestamp: new Date(),
            imageUrl: `data:image/jpeg;base64,${firstImage.image.imageBytes}`,
          }
        }
      }
    } catch (error) {
      console.error("Error generating vision snapshot with AI:", error)
    }
  }

  await mockDelay(2000)
  const weddingIcon = `data:image/svg+xml;base64,${btoa(`
    <svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="600" height="400" fill="#F8FAFC"/>
      <circle cx="300" cy="200" r="80" fill="#FEF3C7" stroke="#F59E0B" stroke-width="2"/>
      <path d="M260 180 L280 200 L340 140" stroke="#F59E0B" stroke-width="3" fill="none"/>
      <text x="300" y="320" text-anchor="middle" fill="#374151" font-family="Arial" font-size="24">${vibe} Wedding Vision</text>
      <text x="300" y="350" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="16">AI-Generated Inspiration Coming Soon</text>
    </svg>
  `)}`

  return {
    id: `vision-mock-${Date.now()}`,
    text: `Here's a sneak peek of the vision for ${coupleNames}! A little something to get the inspiration flowing for their ${vibe} wedding. ✨`, // Removed (Mock Image)
    sender: "ella",
    timestamp: new Date(),
    imageUrl: weddingIcon,
  }
}

export const askEllaGeneral = async (
  promptText: string,
  weddingDetails?: WeddingDetails | null,
  appMode: AppMode = "dev"
): Promise<UIGenerateContentResponse> => {
  const coupleName = weddingDetails?.coupleNames || "the current client"
  const weddingVibe = weddingDetails?.vibe || "not set yet"
  const ai = getGeminiAI()

  if (appMode === "prod" && ai) {
    try {
      const systemInstruction = `You are Ella, an emotionally intelligent AI wedding planning assistant for a professional wedding planner. The planner is currently working on a wedding for ${coupleName}. Their wedding vibe is ${weddingVibe}. Be warm, supportive, concise, and highly professional. Offer actionable advice and suggestions tailored to a planner's needs.`
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: promptText,
        config: { systemInstruction: systemInstruction },
      })
      return { text: response.text || "" }
    } catch (error) {
      console.error("Error asking Ella (prod):", error)
      return {
        text: "I seem to be having a little trouble connecting right now. Please try again in a moment.",
      }
    }
  }

  await mockDelay(1500 + Math.random() * 1000)
  let responseText = "I'm processing your request... "
  if (
    promptText.toLowerCase().includes("hello") ||
    promptText.toLowerCase().includes("hi")
  ) {
    responseText = weddingDetails
      ? `Hello! How can I help you with ${coupleName}'s wedding today?`
      : "Hello Planner! How can I assist you?"
  } else if (promptText.toLowerCase().includes("countdown")) {
    responseText = weddingDetails?.weddingDate
      ? `The countdown for ${coupleName} is on their Overview page!`
      : "Select a wedding and set a date, and I'll show you the countdown!"
  } else {
    responseText = `That's an interesting question about "${promptText.substring(
      0,
      30
    )}...". I'm designed to help with wedding planning for your clients.`
  }
  return { text: responseText }
}

export const generateLookIdeas = async (
  preferences: string,
  appMode: AppMode = "dev",
  weddingDetails?: WeddingDetails | null
): Promise<UIGenerateImagesResponse> => {
  const forWhom = weddingDetails?.coupleNames
    ? `for ${weddingDetails.coupleNames}'s wedding`
    : "for a wedding look"
  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: `Generate a wedding look visual ${forWhom}, based on these preferences: ${preferences}. Focus on bridal fashion, elegant, high quality. If multiple items are described, try to incorporate them into a cohesive look or a small set of related images.`,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })
      return {
        generatedImages: (sdkResponse.generatedImages || []).map((sdkImg) => ({
          image: sdkImg.image
            ? { imageBytes: sdkImg.image.imageBytes }
            : undefined,
        })),
      }
    } catch (error) {
      console.error("Error generating look ideas (prod):", error)
      return { generatedImages: [] }
    }
  }

  await mockDelay(2500 + Math.random() * 1000)
  const placeholderBase64 =
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
  return {
    generatedImages: [{ image: { imageBytes: placeholderBase64 } }],
  }
}

const mockEllaSuggestInitialBudget = async (
  details: WeddingDetails
): Promise<BudgetItem[]> => {
  await mockDelay(1000)
  const baseBudget = details.initialBudget || 50000
  return [
    {
      id: generateClientSideId(),
      category: "Venue",
      estimatedCost: baseBudget * 0.3,
      actualCost: 0,
      isPaid: false,
    },
    {
      id: generateClientSideId(),
      category: "Catering",
      estimatedCost: baseBudget * 0.25,
      actualCost: 0,
      isPaid: false,
    },
    {
      id: generateClientSideId(),
      category: "Photography",
      estimatedCost: baseBudget * 0.1,
      actualCost: 0,
      isPaid: false,
    },
  ]
}

export const ellaSuggestInitialBudget = async (
  details: WeddingDetails,
  appMode: AppMode
): Promise<BudgetItem[]> => {
  const prompt = `
    For a wedding planner setting up a client's budget. Client details: Vibe: ${
      details.vibe
    }, Guests: ${details.guestCount}, Location: ${
    details.location
  }, Initial Budget Idea: ${
    details.initialBudget ? "$" + details.initialBudget : "N/A"
  }.
    Generate 3-5 essential wedding budget categories and estimated costs for this client.
    VERY IMPORTANT: Respond ONLY with a valid JSON array of objects, where each object has "category" (string) and "estimatedCost" (number). Do NOT include any other text, explanations, or thoughts.
    Example JSON: [{"category": "Venue", "estimatedCost": 15000}, {"category": "Catering", "estimatedCost": 10000}]
  `

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      console.log("AI: Generating initial budget for client:", details)
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      const parsedItems = parseJsonResponse<
        Array<Omit<BudgetItem, "id" | "actualCost" | "isPaid">>
      >(response.text || "", [])
      return parsedItems.map((item) => ({
        ...item,
        id: generateClientSideId(),
        actualCost: 0,
        isPaid: false,
      }))
    } catch (error) {
      console.error("AI Error (ellaSuggestInitialBudget):", error)
      return mockEllaSuggestInitialBudget(details)
    }
  }
  return mockEllaSuggestInitialBudget(details)
}

const generateComprehensiveMockTimeline = (details: WeddingDetails): Task[] => {
  const weddingDateObj =
    parseDateStringToLocalMidnight(details.weddingDate) ||
    new Date(new Date().setFullYear(new Date().getFullYear() + 1)) // Default to one year from now if no date

  const calculateDueDate = (
    monthsBefore: number,
    daysBefore: number = 0
  ): string => {
    const dueDate = new Date(weddingDateObj.getTime())
    dueDate.setUTCMonth(dueDate.getUTCMonth() - monthsBefore)
    dueDate.setUTCDate(dueDate.getUTCDate() - daysBefore)
    return dueDate.toISOString().split("T")[0]
  }

  const tasks: Task[] = [
    // 12+ Months Out
    {
      id: generateClientSideId(),
      title: "Define Wedding Vision & Style with Client",
      description: "Discuss overall theme, colors, and desired atmosphere.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Establish Client's Preliminary Budget",
      description: "Work with client to set a realistic initial budget.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Draft Initial Guest List (Client Task)",
      description: "Client to provide a rough estimate of guest numbers.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Select Potential Wedding Dates/Season with Client",
      description: "Narrow down preferred dates or season.",
      dueDate: calculateDueDate(11),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Research & Scout Potential Venues",
      description:
        "Identify venues matching client's style, capacity, and budget.",
      dueDate: calculateDueDate(11),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 9-12 Months Out
    {
      id: generateClientSideId(),
      title: "Book Wedding Venue with Client",
      description: "Finalize and book the chosen wedding venue.",
      dueDate: calculateDueDate(10),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Photographer",
      description: "Research, interview, and book photographer.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Videographer",
      description: "Research, interview, and book videographer.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Caterer (if separate)",
      description: "Select and book catering services.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Book Officiant",
      description: "Secure an officiant for the ceremony.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Start Wedding Website (Client Task)",
      description: "Client to set up basic wedding website with key details.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Assemble Client's Vision Board",
      description: "Collect visual inspiration for decor, attire, etc.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Choose Wedding Party (Client Task)",
      description: "Client to decide on bridesmaids, groomsmen, etc.",
      dueDate: calculateDueDate(8),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 6-9 Months Out
    {
      id: generateClientSideId(),
      title: "Hire Florist",
      description: "Select and book a florist.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Band/DJ",
      description: "Book entertainment for reception.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Order Save-the-Dates (Client Task)",
      description: "Design and order save-the-date cards.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Guest List (Client Task)",
      description: "Client to confirm final guest list and addresses.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Shop for Wedding Attire (Client Task)",
      description: "Client to begin looking for wedding dress, suits, etc.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Arrange Hotel Room Blocks for Guests",
      description: "Reserve blocks of hotel rooms for out-of-town guests.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Engagement Photo Shoot (Client Task, if desired)",
      description: "Schedule and plan engagement photos.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
      isPlannerInternal: true,
    },

    // 4-6 Months Out
    {
      id: generateClientSideId(),
      title: "Send Save-the-Dates (Client Task)",
      description: "Client to mail out save-the-date cards.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Order Wedding Invitations",
      description: "Design and order wedding invitations and stationery.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Book Hair & Makeup Artists",
      description: "Secure stylists for the wedding day.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Ceremony Details",
      description: "Outline ceremony structure, readings, music with client.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Reception Details",
      description: "Outline reception timeline, decor, layout with client.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Order Wedding Cake",
      description: "Taste and order the wedding cake.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Arrange Transportation",
      description: "Book transportation for wedding party and/or guests.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Purchase Wedding Rings (Client Task)",
      description: "Client to select and purchase wedding bands.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Register for Gifts (Client Task, if applicable)",
      description: "Client to set up gift registries.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 2-3 Months Out
    {
      id: generateClientSideId(),
      title: "Mail Wedding Invitations (Client Task)",
      description: "Client to send out wedding invitations.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Menu with Caterer",
      description: "Confirm final food and beverage selections.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Schedule Attire Fittings (Client Task)",
      description: "Client to schedule dress/suit fittings.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Floral Selections",
      description: "Confirm all floral arrangements with florist.",
      dueDate: calculateDueDate(2, 15),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Rehearsal Dinner",
      description: "Organize details for the rehearsal dinner.",
      dueDate: calculateDueDate(2, 15),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Create Seating Chart",
      description: "Develop the guest seating arrangement for reception.",
      dueDate: calculateDueDate(2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Purchase Wedding Favors (Client Task, if applicable)",
      description: "Client to buy or make wedding favors.",
      dueDate: calculateDueDate(2),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Write Vows (Client Task)",
      description: "Client to write personal wedding vows.",
      dueDate: calculateDueDate(2),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 1 Month Out
    {
      id: generateClientSideId(),
      title: "Apply for Marriage License (Client Task)",
      description: "Client to obtain marriage license.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Confirm Final Details with All Vendors",
      description: "Reconfirm arrival times, services, and payments.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Wedding Day Timeline",
      description: "Create a detailed schedule for the wedding day.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Confirm RSVP Count with Venue/Caterer",
      description: "Provide final guest numbers.",
      dueDate: calculateDueDate(0, 21),
      isCompleted: false,
      assignedTo: "Planner",
    }, // 3 weeks before
    {
      id: generateClientSideId(),
      title: "Pack for Honeymoon (Client Task)",
      description: "Client to prepare for their honeymoon.",
      dueDate: calculateDueDate(0, 14),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Final Attire Fitting (Client Task)",
      description: "Client's last fitting for wedding attire.",
      dueDate: calculateDueDate(0, 14),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 1-2 Weeks Out
    {
      id: generateClientSideId(),
      title: "Prepare Welcome Bags for Guests (if applicable)",
      description: "Assemble welcome bags for out-of-town guests.",
      dueDate: calculateDueDate(0, 10),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Review Wedding Day Timeline with Wedding Party",
      description: "Ensure everyone knows the schedule.",
      dueDate: calculateDueDate(0, 7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Confirm Beauty Appointments (Client Task)",
      description: "Client to reconfirm hair/makeup schedule.",
      dueDate: calculateDueDate(0, 7),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Delegate Wedding Day Tasks (Planner internal)",
      description: "Assign small day-of responsibilities if needed.",
      dueDate: calculateDueDate(0, 7),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Pick Up Wedding Attire (Client Task)",
      description: "Client to collect cleaned/altered attire.",
      dueDate: calculateDueDate(0, 3),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // Wedding Week
    {
      id: generateClientSideId(),
      title: "Final Vendor Confirmations & Payments",
      description: "Ensure all vendors are set and final payments made.",
      dueDate: calculateDueDate(0, 2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Prepare Tip Envelopes",
      description: "Organize tips for vendors.",
      dueDate: calculateDueDate(0, 2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Rehearsal & Rehearsal Dinner",
      description: "Conduct ceremony rehearsal and host dinner.",
      dueDate: calculateDueDate(0, 1),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Get a Good Night's Sleep! (Client Task)",
      description: "Advise client to rest before the big day.",
      dueDate: calculateDueDate(0, 1),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // Wedding Day
    {
      id: generateClientSideId(),
      title: "Oversee Venue Setup & Vendor Coordination",
      description: "Manage all on-site logistics.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Manage Wedding Day Timeline Execution",
      description: "Ensure everything runs smoothly and on schedule.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Distribute Personal Flowers",
      description: "Ensure wedding party has bouquets/boutonnieres.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Handle Last-Minute Issues",
      description: "Troubleshoot any unexpected problems.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Ensure Gifts & Personal Items are Secure",
      description: "Coordinate collection of gifts and couple's belongings.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Distribute Final Payments/Tips to Vendors",
      description: "Handle remaining payments and gratuities.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // Post-Wedding
    {
      id: generateClientSideId(),
      title: "Oversee Venue Teardown/Cleanup",
      description: "Manage post-event cleanup as per contract.",
      dueDate: calculateDueDate(-0, -1),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    }, // Negative months = after wedding
    {
      id: generateClientSideId(),
      title: "Return Rented Items",
      description: "Ensure all rentals are returned on time.",
      dueDate: calculateDueDate(-0, -2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Send Thank You Notes (Client Task)",
      description: "Client to write and send thank you cards to guests.",
      dueDate: calculateDueDate(-1, 0),
      isCompleted: false,
      assignedTo: "Couple",
    }, // 1 month after
    {
      id: generateClientSideId(),
      title: "Follow Up with Photographer/Videographer for Deliverables",
      description: "Check on photo/video delivery timeline.",
      dueDate: calculateDueDate(-1, -15),
      isCompleted: false,
      assignedTo: "Planner",
    }, // 1.5 months after
    {
      id: generateClientSideId(),
      title: "Review Vendor Services & Provide Feedback",
      description: "Collect feedback for future reference.",
      dueDate: calculateDueDate(-2, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Finalize Client's Wedding Album/Prints (if part of service)",
      description: "Coordinate album design and ordering.",
      dueDate: calculateDueDate(-3, 0),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Preserve Wedding Dress/Suit (Client Task)",
      description: "Advise client on attire preservation.",
      dueDate: calculateDueDate(-1, 0),
      isCompleted: false,
      assignedTo: "Couple",
    },
  ]
  return tasks
}

const mockEllaGenerateInitialTimeline = async (
  details: WeddingDetails
): Promise<Task[]> => {
  await mockDelay(1000)
  return generateComprehensiveMockTimeline(details)
}

export const ellaGenerateInitialTimeline = async (
  details: WeddingDetails,
  appMode: AppMode
): Promise<Task[]> => {
  const prompt = `
    For a wedding planner managing a wedding for ${
      details.coupleNames
    } on/around ${
    details.weddingDate
  } (this is the wedding date, format YYYY-MM-DD). The wedding vibe is "${
    details.vibe
  }".
    Generate a comprehensive wedding planning timeline with 70-100 tasks.
    Tasks should cover all typical phases from 12+ months before the wedding to post-wedding activities.
    For each task, provide a "title" (string).
    Optionally, include "description" (string), and "assignedTo" (string - e.g., "Planner", "${
      details.coupleNames?.split(" & ")[0]
    }", "Couple").
    Crucially, if you provide a "dueDate", it MUST be in YYYY-MM-DD format and calculated appropriately relative to the wedding date: ${
      details.weddingDate
    }. Default to "Planner" or "Couple" for assignedTo if unsure.
    Prioritize a logical flow and cover a wide range of typical wedding planning activities.
    VERY IMPORTANT: Respond ONLY with a valid JSON array of objects. Do NOT include any other text, explanations, or thoughts.
    Example JSON object within the array: {"title": "Book Venue", "dueDate": "YYYY-MM-DD", "description": "Secure the main location.", "assignedTo": "Planner"}
    (Replace YYYY-MM-DD in the example with a date calculated relative to ${
      details.weddingDate
    })
  `
  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      console.log(
        "AI: Generating comprehensive initial timeline for client:",
        details
      )
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      const parsedItems = parseJsonResponse<
        Array<Omit<Task, "id" | "isCompleted">>
      >(response.text || "", [])
      if (parsedItems.length < 50) {
        // If AI returns too few, use mock as fallback
        console.warn(
          `AI returned only ${parsedItems.length} tasks, falling back to comprehensive mock.`
        )
        return mockEllaGenerateInitialTimeline(details)
      }
      return parsedItems.map((item) => ({
        ...item,
        id: generateClientSideId(),
        isCompleted: false,
      }))
    } catch (error) {
      console.error(
        "AI Error (ellaGenerateInitialTimeline - comprehensive):",
        error
      )
      return mockEllaGenerateInitialTimeline(details)
    }
  }
  return mockEllaGenerateInitialTimeline(details)
}

export const ellaSuggestBudgetItem = async (
  currentBudget: BudgetItem[],
  details: WeddingDetails,
  appMode: AppMode,
  autonomyLevel: EllaAutonomyLevel = "balanced"
): Promise<Partial<BudgetItem>> => {
  let promptInstruction =
    "Suggest *one* new, essential budget item category and a reasonable estimatedCost for this client. Exclude categories already present."
  let returnFormat = `Return JSON: {"category": "string", "estimatedCost": number}.`

  if (autonomyLevel === "proactive") {
    promptInstruction =
      "Suggest *one* new, essential budget item (category, estimatedCost) for this client. Provide values for vendor (optional string, e.g., 'Research Needed'), paymentDueDate (optional YYYY-MM-DD, e.g., a common due date relative to wedding date if applicable), and isPaid (boolean, default false). Exclude categories already present."
    returnFormat = `Return JSON: {"category": "string", "estimatedCost": number, "vendor": "string"?, "paymentDueDate": "YYYY-MM-DD"?, "isPaid": boolean?}. If no obvious suggestion, return an empty object {}.`
  } else if (autonomyLevel === "review_all") {
    promptInstruction =
      "Suggest *one* new budget item category and estimatedCost for the planner to *consider* adding for this client. Exclude categories already present."
    returnFormat = `Return JSON: {"category": "string", "estimatedCost": number}. If no obvious suggestion, return an empty object {}.`
  }

  const prompt = `You are Ella, an AI wedding planning assistant for a professional wedding planner.
Planner's Autonomy Preference: ${autonomyLevel}.
Client Wedding Details: Vibe: ${details.vibe}, Guests: ${
    details.guestCount
  }, Budget: ${
    details.initialBudget ? "$" + details.initialBudget : "N/A"
  }. Wedding Date: ${details.weddingDate || "Not set"}.
Current Budget Categories for this client: ${JSON.stringify(
    currentBudget.map((i) => i.category)
  )}.
${promptInstruction}
VERY IMPORTANT: Respond ONLY with the requested valid JSON object. Do NOT include any other text, explanations, or thoughts.
${returnFormat}`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      const suggestion = parseJsonResponse<Partial<BudgetItem>>(
        response.text || "",
        {}
      )

      if (
        autonomyLevel === "proactive" &&
        suggestion.category &&
        suggestion.estimatedCost !== undefined
      ) {
        return {
          vendor: "",
          paymentDueDate: "",
          isPaid: false,
          ...suggestion,
        }
      }
      return suggestion
    } catch (error) {
      console.error("AI Error (ellaSuggestBudgetItem):", error)
      return {}
    }
  }

  await mockDelay(1000)
  const mockBaseCost = (details.initialBudget || 50000) * 0.05
  const mockSuggestions: Partial<BudgetItem>[] = [
    {
      category: "Contingency Fund",
      estimatedCost: mockBaseCost,
      isPaid: false,
      vendor: "N/A",
    },
    {
      category: "Wedding Rings",
      estimatedCost: 1500,
      isPaid: false,
      vendor: "Jeweler X",
    },
    {
      category: "Stationery - Invitations",
      estimatedCost: 800,
      isPaid: false,
      vendor: "Paper Co.",
    },
  ]
  const existingCategories = currentBudget.map((i) => i.category)
  const validSuggestion = mockSuggestions.find(
    (s) => !existingCategories.includes(s.category || "")
  )

  if (validSuggestion && autonomyLevel === "proactive") return validSuggestion
  if (validSuggestion)
    return {
      category: validSuggestion.category,
      estimatedCost: validSuggestion.estimatedCost,
    }
  return {}
}

export const ellaSuggestTimelineTask = async (
  currentTasks: Task[],
  details: WeddingDetails,
  appMode: AppMode
): Promise<Partial<Task>> => {
  const weddingDateObj = parseDateStringToLocalMidnight(details.weddingDate)
  const weddingDateForPrompt = weddingDateObj
    ? details.weddingDate
    : "Not set, assume 12 months from now"

  const prompt = `You are Ella, an AI wedding planning assistant for a professional wedding planner.
Client Wedding: ${details.coupleNames}, Date: ${weddingDateForPrompt}, Vibe: ${
    details.vibe
  }.
Current Tasks for this client: ${JSON.stringify(
    currentTasks.map((t) => t.title).slice(-10)
  )}. (Showing last 10 for brevity)
Suggest *one* new, important timeline task for this client that is likely missing and relevant given the current tasks and wedding details.
Provide a "title", a brief "description".
Optionally, provide a "dueDate" (YYYY-MM-DD) calculated relative to the wedding date (${weddingDateForPrompt}), and optionally an "assignedTo" (e.g., "Planner", "${
    details.coupleNames?.split(" & ")[0]
  }", "Couple").
Exclude tasks already present if very similar.
VERY IMPORTANT: Respond ONLY with the requested valid JSON object. Do NOT include any other text, explanations, or thoughts.
Return JSON: {"title": "string", "description": "string"?, "dueDate": "YYYY-MM-DD"?, "assignedTo": "string"?}. If no obvious suggestion, return an empty object {}.`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      return parseJsonResponse<Partial<Task>>(response.text || "", {})
    } catch (error) {
      console.error("AI Error (ellaSuggestTimelineTask):", error)
      return {}
    }
  }
  await mockDelay(1000)
  // Simplified mock, as comprehensive list is now default
  const mockSuggestions: Partial<Task>[] = [
    {
      title: "Review Final Vendor Contracts",
      description: "Ensure all details are correct before wedding week.",
      dueDate: new Date(
        new Date(weddingDateObj || new Date()).setMonth(
          (weddingDateObj || new Date()).getMonth() - 0,
          14
        )
      )
        .toISOString()
        .split("T")[0],
      assignedTo: "Planner",
    },
  ]
  const existingTitles = currentTasks.map((t) => t.title)
  const validSuggestion = mockSuggestions.find(
    (s) => !existingTitles.includes(s.title || "")
  )
  return validSuggestion || {}
}

export const ellaSuggestGuest = async (
  currentGuests: Guest[],
  details: WeddingDetails,
  appMode: AppMode
): Promise<Partial<Guest>> => {
  const prompt = `You are Ella, an AI wedding planning assistant for a professional wedding planner.
Client Wedding Details: ${JSON.stringify(details)}.
Current Guest Groups for this client: ${JSON.stringify(
    Array.from(new Set(currentGuests.map((g) => g.group).filter(Boolean)))
  )}.
Suggest *one* new guest type or common group the planner might consider for this client (e.g., "Client's Work Colleagues", "Parents' Close Friends").
VERY IMPORTANT: Respond ONLY with the requested valid JSON object. Do NOT include any other text, explanations, or thoughts.
Return JSON: {"name": "Suggested Guest for Client", "group": "string"}. If no obvious suggestion, return an empty object {}.`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      return parseJsonResponse<Partial<Guest>>(response.text || "", {})
    } catch (error) {
      console.error("AI Error (ellaSuggestGuest):", error)
      return {}
    }
  }
  await mockDelay(1000)
  return { name: "Consider These Guests", group: "Client's University Friends" }
}

export const ellaSuggestVendor = async (
  currentVendors: Vendor[],
  details: WeddingDetails,
  appMode: AppMode
): Promise<Partial<Vendor>> => {
  const prompt = `You are Ella, an AI wedding planning assistant for a professional wedding planner.
Client Wedding Details: ${JSON.stringify(details)}.
Current Vendor Categories for this client: ${JSON.stringify(
    Array.from(new Set(currentVendors.map((v) => v.category)))
  )}.
Suggest *one* new vendor category that might be needed for this client and has not been added yet.
Example categories: Venue, Photographer, Caterer, Florist, DJ/Band, Attire, Stationery, Transportation, Cake, Officiant, Wedding Planner, Videographer, Hair & Makeup, Favors, Other.
VERY IMPORTANT: Respond ONLY with the requested valid JSON object. Do NOT include any other text, explanations, or thoughts.
Return JSON: {"name": "Suggested Vendor for Client's [Category]", "category": "VendorCategoryString", "status": "Researching"}. Replace [Category] with the actual suggested category.
If no obvious suggestion, return an empty object {}.`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      const suggestion = parseJsonResponse<Partial<Vendor>>(
        response.text || "",
        {}
      )
      const validVendorCategories: VendorCategory[] = [
        "Venue",
        "Photographer",
        "Caterer",
        "Florist",
        "DJ/Band",
        "Attire",
        "Stationery",
        "Transportation",
        "Cake",
        "Officiant",
        "Videographer",
        "Wedding Planner",
        "Hair & Makeup",
        "Favors",
        "Other",
      ]
      if (
        suggestion.category &&
        !validVendorCategories.includes(suggestion.category as VendorCategory)
      ) {
        console.warn(
          `AI suggested an unknown vendor category: ${suggestion.category}. Defaulting to 'Other'.`
        )
        suggestion.category = "Other"
      }
      return suggestion
    } catch (error) {
      console.error("AI Error (ellaSuggestVendor):", error)
      return {}
    }
  }
  await mockDelay(1000)
  const mockCategories: VendorCategory[] = ["Favors", "Transportation", "Other"]
  const existingCategories = currentVendors.map((v) => v.category)
  const validCategory = mockCategories.find(
    (cat) => !existingCategories.includes(cat as VendorCategory)
  )
  return validCategory
    ? {
        name: `Example ${validCategory} Vendor`,
        category: validCategory as VendorCategory,
        status: "Researching",
      }
    : {}
}

export const getAIAgentActivity = async (
  currentWedding: ManagedWedding | null,
  recentActivityHint: string,
  appMode: AppMode = "dev"
): Promise<AIAgentLogEntry> => {
  const clientContext = currentWedding
    ? `for ${currentWedding.weddingDetails.coupleNames}'s wedding (${currentWedding.weddingDetails.vibe} vibe in ${currentWedding.weddingDetails.location})`
    : "for general planner tasks"

  let agentNameStr: AIAgentLogEntry["agent"] = "Associate Agent" // Default
  let actionStr = "Processed a background task."
  const detailsStr: string | undefined = `Status: Complete`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const clientDetailsForPrompt = currentWedding
        ? `Client: ${currentWedding.weddingDetails.coupleNames}, Vibe: ${currentWedding.weddingDetails.vibe}, Location: ${currentWedding.weddingDetails.location}, Guests: ${currentWedding.weddingDetails.guestCount}.`
        : "No specific client wedding active."

      const prompt = `You are an AI Agent System logger for SayYes Planner, a wedding planner's platform.
The planner is currently ${
        currentWedding
          ? `focused on ${clientContext}`
          : "on their main dashboard"
      }.
A recent general activity by the planner was: "${recentActivityHint}".
${clientDetailsForPrompt}

Generate a single, concise, and plausible system log entry. This log entry should represent a specific background task or analysis performed by one of Ella's specialized sub-agents.
The sub-agents are: Ella Orchestrator, Venue Manager AI, Budget Manager AI, Timeline Manager AI, Guest Manager AI, Venue Scout Associate, Venue Availability Checker, Budget Data Analyst, Styling Assistant AI, Client Comms AI.

The log entry MUST be in the format: "Agent Name: Specific action taken, often with a brief outcome or relation to the client's wedding details."
Make the action sound intelligent and relevant to wedding planning.
If a client wedding is active, incorporate elements from their wedding details naturally into the action if it makes sense.

Examples of good log entries:
"Budget Manager AI: Flagged potential overspend in 'Floral Decor' category for ${
        currentWedding?.weddingDetails.coupleNames || "client"
      } based on current ${
        currentWedding?.weddingDetails.vibe || "selected"
      } vibe and vendor quotes."
"Venue Scout Associate: Identified 2 new rustic barn venues in the ${
        currentWedding?.weddingDetails.location || "target"
      } area suitable for ${
        currentWedding?.weddingDetails.guestCount || "guest count"
      } guests."
"Timeline Manager AI: Adjusted task deadlines for ${
        currentWedding?.weddingDetails.coupleNames || "client"
      } due to vendor response delays."
"Styling Assistant AI: Curated a new mood board focusing on '${
        currentWedding?.weddingDetails.vibe || "requested"
      }' color palettes for ${
        currentWedding?.weddingDetails.coupleNames || "client"
      }."
"Client Comms AI: Analyzed client sentiment from recent communications for ${
        currentWedding?.weddingDetails.coupleNames || "client"
      } and suggested a follow-up strategy to the planner."

Do NOT include any explanations, conversational text, or any text other than the single log entry.`

      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
      })
      const activityDescription =
        response.text ||
        "AI Agent: Processed a background task for the planner."
      const parts = activityDescription.split(": ")
      if (parts.length > 1) {
        agentNameStr = parts[0] as AIAgentLogEntry["agent"] // Potential cast issue, see below
        actionStr = parts.slice(1).join(": ")
      } else {
        actionStr = activityDescription
      }
    } catch (error) {
      console.error("Error generating AI agent activity (prod):", error)
      actionStr =
        "Experienced an internal error while logging activity for the planner."
    }
  } else {
    // Mock for dev mode
    await mockDelay(Math.random() * 1500 + 300)
    const agents: Array<AIAgentLogEntry["agent"]> = [
      "Ella",
      "Venue Manager",
      "Vendor Manager",
      "Guest Manager",
      "Budget Manager",
      "Styling Manager",
      "Timeline Manager",
      "Associate Agent",
      "System",
    ]
    const actions = [
      `analyzing user preferences ${clientContext}`,
      `researching vendor options ${clientContext}`,
      `updating guest list ${clientContext}`,
      `cross-referencing budget constraints ${clientContext}`,
      "drafting reminder email template for clients",
      "scheduling follow-up tasks for planner",
      `generating timeline adjustments based on recent changes ${clientContext}`,
      `compiling venue comparison report ${clientContext}`,
      `checking florist availability for wedding date ${clientContext}`,
      "synthesizing information for planner summary and next steps",
      `identifying potential scheduling conflicts ${clientContext}`,
      `generating creative decor ideas based on '${
        currentWedding?.weddingDetails.vibe || "general"
      }' vibe`,
      `updating contact list for ${clientContext}`,
      `cross-checking new task dependencies ${clientContext}`,
      `reviewing vendor contracts for key clauses ${clientContext}`,
      `preparing weekly progress summary for planner`,
    ]
    agentNameStr = agents[Math.floor(Math.random() * agents.length)]
    let mockAction = actions[Math.floor(Math.random() * actions.length)]

    if (
      mockAction.includes(clientContext) &&
      clientContext !== "for general planner tasks"
    ) {
      mockAction = mockAction.replace(clientContext, "").trim()
      if (mockAction.endsWith("for"))
        mockAction = mockAction.substring(0, mockAction.length - 3).trim()
      actionStr = `${mockAction} ${clientContext}`
    } else {
      actionStr = mockAction
    }
  }

  // Helper to try and map string to the AIAgentLogEntry['agent'] union type
  const mapAgentStringToType = (
    agentName: string
  ): AIAgentLogEntry["agent"] => {
    const nameLower = agentName.toLowerCase()
    const validAgents: Array<AIAgentLogEntry["agent"]> = [
      "Ella",
      "Venue Manager",
      "Vendor Manager",
      "Guest Manager",
      "Budget Manager",
      "Styling Manager",
      "Timeline Manager",
      "Associate Agent",
      "System",
    ]
    for (const validAgent of validAgents) {
      // This regex cleans up " AI", " Bot", " Manager", " Assistant" from the validAgent string before checking inclusion.
      if (
        nameLower.includes(
          validAgent
            .toLowerCase()
            .replace(" ai", "")
            .replace(" bot", "")
            .replace(" manager", "")
            .replace(" assistant", "")
        )
      ) {
        return validAgent
      }
    }
    // Fallback if no specific match, or refine with more keywords
    if (nameLower.includes("system")) return "System"
    if (nameLower.includes("ella")) return "Ella"
    if (nameLower.includes("venue")) return "Venue Manager"
    if (nameLower.includes("vendor")) return "Vendor Manager"
    if (nameLower.includes("guest")) return "Guest Manager"
    if (nameLower.includes("budget")) return "Budget Manager"
    if (nameLower.includes("styling") || nameLower.includes("style"))
      return "Styling Manager"
    if (nameLower.includes("timeline") || nameLower.includes("schedule"))
      return "Timeline Manager"
    return "Associate Agent" // Default fallback
  }

  return {
    id: generateClientSideId(),
    timestamp: new Date(),
    agent: mapAgentStringToType(agentNameStr),
    action: actionStr,
    details: detailsStr,
  }
}

export const ellaSuggestBudgetSolutions = async (
  problemItem: BudgetItem,
  allBudgetItems: BudgetItem[],
  weddingDetails: WeddingDetails,
  appMode: AppMode
): Promise<string> => {
  const overBudgetAmount = problemItem.actualCost - problemItem.estimatedCost

  const prompt = `You are Ella, a helpful and empathetic AI wedding planning assistant to a professional wedding planner.
The planner is working with ${weddingDetails.coupleNames}, who are planning a ${
    weddingDetails.vibe
  } wedding.
Their client's budget item "${
    problemItem.category
  }" is currently over its estimated cost by $${overBudgetAmount.toFixed(
    2
  )} (Actual: $${problemItem.actualCost.toFixed(
    2
  )}, Estimated: $${problemItem.estimatedCost.toFixed(2)}).
The client's overall initial budget idea was $${
    weddingDetails.initialBudget?.toFixed(2) || "not specified"
  }.

Here are their other budget items for context:
${allBudgetItems
  .filter((item) => item.id !== problemItem.id)
  .map(
    (item) =>
      `- ${item.category}: Est $${item.estimatedCost.toFixed(
        2
      )}, Actual $${item.actualCost.toFixed(2)}`
  )
  .join("\n")}

Please provide the planner with 2-3 concise, actionable, and empathetic suggestions to help their client address this.
For example, suggest reallocating from specific less critical categories if possible, finding alternatives for the problematic category, or (as a last resort) discussing a slight adjustment to the overall budget if other options are limited.
Present the suggestions in a friendly, professional, easy-to-read format. Start by acknowledging the situation from the planner's perspective.
VERY IMPORTANT: Respond ONLY with the suggestions. Do NOT include any other text, explanations, or conversational filler.`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
      })
      return (
        response.text ||
        "I'm having a bit of trouble analyzing the budget right now, but let's look at ways to adjust things for your client!"
      )
    } catch (error) {
      console.error("AI Error (ellaSuggestBudgetSolutions):", error)
      return "Oh dear, I encountered a small hiccup trying to find solutions. Could you try asking again in a moment?"
    }
  }

  await mockDelay(1500)
  return `I see your client's "${
    problemItem.category
  }" is over budget by $${overBudgetAmount.toFixed(
    2
  )}. Let's find some solutions for them! Here are a few ideas you could discuss:

1.  **Review other categories:** Could they potentially reallocate some funds from a category like "Decorations" or "Favors" if those are less critical for their ${
    weddingDetails.vibe
  } vibe?
2.  **Explore alternatives for ${
    problemItem.category
  }:** Sometimes a different supplier or a slightly modified approach can make a big difference for the client.
3.  **Contingency Check:** Does the client have a contingency fund? This might be a good time to advise them to tap into it if other options don't work.

Let me know how you'd like to proceed with advising your client!`
}

export const ellaDraftVendorInquiry = async (
  vendor: Vendor,
  weddingDetails: WeddingDetails,
  appMode: AppMode,
  plannerName?: string
): Promise<string> => {
  const plannerIntro = plannerName
    ? `My name is ${plannerName}, and I am the wedding planner for the couple.`
    : `I am assisting the couple with their wedding planning.`
  const coupleNameIntro = weddingDetails.coupleNames

  const prompt = `You are Ella, an AI wedding planning assistant to a professional wedding planner.
The planner is making an inquiry for their clients, ${coupleNameIntro}.
Clients' wedding details: ${weddingDetails.vibe} wedding on ${
    weddingDetails.weddingDate
  } in ${weddingDetails.location} for approximately ${
    weddingDetails.guestCount
  } guests.

Draft a polite and professional inquiry email from the planner to the vendor:
Vendor Name: ${vendor.name}
Vendor Category: ${vendor.category}

The email should:
1.  ${
    plannerName
      ? `Introduce the planner (${plannerName}) and mention they are planning for ${coupleNameIntro}.`
      : `State the planner is representing ${coupleNameIntro}.`
  }
2.  Mention the clients' wedding date and location.
3.  Inquire about the vendor's availability for the wedding date.
4.  Request information on their services/packages relevant to their category (${
    vendor.category
  }) for the clients.
5.  Ask for pricing information, a brochure, or the next steps to get a quote for the clients.
6.  Maintain a warm, helpful, and highly professional tone appropriate for a wedding planner.
7.  Include a clear suggested subject line like: "Wedding Inquiry: [Planner Name/Company] for [Couple Names] - [Wedding Date] - [Vendor Category]"

VERY IMPORTANT: Respond ONLY with the full email text, starting with "Subject: [Your Suggested Subject Line]". Do NOT include any other explanations or conversational text.`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
      })
      return (
        response.text ||
        "I'm having trouble drafting this email right now. Please try again."
      )
    } catch (error) {
      console.error("AI Error (ellaDraftVendorInquiry):", error)
      return "Apologies, I couldn't draft the email. Please try again or manually compose it."
    }
  }

  await mockDelay(1500)
  const subjectLine = `Subject: Wedding Inquiry: ${
    plannerName || "Planner"
  } for ${coupleNameIntro} - ${weddingDetails.weddingDate} - ${vendor.category}`
  return `${subjectLine}

Dear ${vendor.name || "Vendor Team"},

I hope this email finds you well.

${plannerIntro} I am organizing the wedding for ${coupleNameIntro}.
Their special day is set for ${weddingDetails.weddingDate} in ${
    weddingDetails.location
  }, and they are expecting around ${
    weddingDetails.guestCount
  } guests. They are aiming for a ${weddingDetails.vibe} atmosphere.

We came across your services as a ${
    vendor.category
  } and are very interested on behalf of our clients. We would love to know if you are available on their wedding date.

Could you please provide us with more information about your ${
    vendor.category
  } services and packages suitable for them? We'd also appreciate any details on pricing or your brochure if available.

Thank you for your time and consideration. We look forward to hearing from you!

Warmly,
${plannerName || "The Wedding Planner"}
(On behalf of ${coupleNameIntro})
(Assisted by Ella - Your AI Wedding Planner Assistant)`
}

export const ellaBreakdownTask = async (
  task: Task,
  weddingDetails: WeddingDetails,
  appMode: AppMode
): Promise<{
  subTasks: Array<Omit<Task, "id" | "isCompleted">>
  parentTaskUpdate?: Partial<Omit<Task, "id" | "isCompleted">>
}> => {
  const weddingDateForPrompt =
    weddingDetails.weddingDate || "Not set, assume 12 months from now"
  const prompt = `You are Ella, an AI assistant to a professional wedding planner.
The planner is managing a wedding for ${
    weddingDetails.coupleNames
  }, scheduled for ${weddingDateForPrompt}.
They have a parent task for this client: "${task.title}" (Due: ${
    task.dueDate || "N/A"
  }, Description: ${task.description || "N/A"}, Assigned To: ${
    task.assignedTo || "Planner/Couple"
  }).

Break this parent task into 2-4 smaller, actionable sub-tasks for the planner or the client.
For each sub-task, provide a "title". Optionally, provide a brief "description", a "dueDate" (YYYY-MM-DD) calculated relative to the parent task's due date or the wedding date (${weddingDateForPrompt}), and an "assignedTo" (e.g., "Planner", one of the couple's names, "Couple", or inherit from parent if logical).
Additionally, if it makes sense, suggest an update for the parent task's "title" or "description" to reflect its new role as a summary (e.g., append "(Summary)" to title, or rephrase description).

VERY IMPORTANT: Respond ONLY with the requested valid JSON object in the exact format specified below. Do NOT include any other text, explanations, or thoughts.
Format:
{
  "subTasks": [{"title": "string", "description": "string"?, "dueDate": "YYYY-MM-DD"?, "assignedTo": "string"?}, ...],
  "parentTaskUpdate": {"title": "string"?, "description": "string"?}
}
If no meaningful breakdown is possible, return empty subTasks array and no parentTaskUpdate, e.g., {"subTasks": [], "parentTaskUpdate": {}}.
Example sub-task: {"title": "Research venue styles for client", "description": "Look for venues matching client's boho vibe", "assignedTo": "Planner"}
Example parent update: {"title": "${task.title} (Client Overview)"}
`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const response = await ai.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      return parseJsonResponse(response.text || "", { subTasks: [] })
    } catch (error) {
      console.error("AI Error (ellaBreakdownTask):", error)
      return { subTasks: [] }
    }
  }

  await mockDelay(1500)
  const mockSubTasks: Array<Omit<Task, "id" | "isCompleted">> = [
    {
      title: `Sub-task 1 for "${task.title}"`,
      description: "Detail for sub-task 1",
      dueDate: task.dueDate,
      assignedTo: task.assignedTo || "Planner",
    },
    {
      title: `Sub-task 2 for "${task.title}"`,
      description: "Detail for sub-task 2",
      assignedTo: weddingDetails.coupleNames?.split(" & ")[0] || "Client",
    },
  ]
  const mockParentUpdate: Partial<Omit<Task, "id" | "isCompleted">> = {
    title: `${task.title} (Summary)`,
    description: `This task now summarizes sub-tasks. Original: ${
      task.description || ""
    }`,
  }
  return { subTasks: mockSubTasks, parentTaskUpdate: mockParentUpdate }
}

export const ellaSuggestVisionBoardImage = async (
  weddingVibe: string,
  appMode: AppMode
): Promise<VisionBoardItem | null> => {
  const defaultCaptionProd = `AI-suggested inspiration for a ${weddingVibe} wedding.`
  const devModeWeddingDressCaption = `AI-suggested modern wedding dress style for your client.`
  const devModeWeddingDressImageUrl = `data:image/svg+xml;base64,${btoa(`
    <svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="600" height="400" fill="#FDF2F8"/>
      <circle cx="300" cy="200" r="100" fill="#FCE7F3" stroke="#EC4899" stroke-width="3"/>
      <path d="M250 170 L280 200 L350 130" stroke="#EC4899" stroke-width="4" fill="none"/>
      <text x="300" y="330" text-anchor="middle" fill="#BE185D" font-family="Arial" font-size="20">Wedding Inspiration</text>
      <text x="300" y="360" text-anchor="middle" fill="#DB2777" font-family="Arial" font-size="16">AI-Generated Vision Coming Soon</text>
    </svg>
  `)}`

  const ai = getGeminiAI()
  if (appMode === "prod" && ai) {
    try {
      const imagePrompt = `Generate an inspiring and visually distinct image suitable for a wedding vision board. The wedding vibe is "${weddingVibe}". The image should focus on one or two key elements like unique decor, table setting, floral arrangement, color palette, lighting, or overall ambiance. Avoid text or people. High-quality, artistic, and inspiring.`

      const imageResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: imagePrompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        imageResponse.generatedImages &&
        imageResponse.generatedImages.length > 0
      ) {
        const firstImage = imageResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          const visionItem: VisionBoardItem = {
            id: generateClientSideId(),
            imageUrl: `data:image/jpeg;base64,${firstImage.image.imageBytes}`,
            caption: defaultCaptionProd,
            tags: [
              weddingVibe.toLowerCase().replace(/\s+/g, "-"),
              "ai-suggested",
            ],
          }
          return visionItem
        }
      }
      console.warn(
        "AI Vision Board Image: Image generation did not return expected data."
      )
      return null
    } catch (error) {
      console.error(
        "AI Error (ellaSuggestVisionBoardImage - image generation):",
        error
      )
      return null
    }
  }

  // Dev mode:
  await mockDelay(1500)
  const mockItem: VisionBoardItem = {
    id: generateClientSideId(),
    imageUrl: devModeWeddingDressImageUrl,
    caption: devModeWeddingDressCaption,
    tags: [
      "modern-wedding-dress",
      "dev-mock",
      weddingVibe.toLowerCase().replace(/\s+/g, "-"),
    ],
  }
  return mockItem
}
