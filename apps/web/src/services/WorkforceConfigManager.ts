import { PREDEFINED_AGENTS } from "@/src/constants"
import {
  AgentPerformanceMetrics,
  EllaAutonomyLevel,
  LLMModel,
  PredefinedAgent,
  WorkforceConfiguration,
  WorkforceTemplate,
} from "@/src/types/index"

export class WorkforceConfigManager {
  // Default workforce templates
  static getDefaultTemplates(): WorkforceTemplate[] {
    return [
      {
        id: "basic-wedding-template",
        name: "Basic Wedding Assistant",
        description:
          "Essential AI agents for smaller weddings and budget-conscious clients",
        category: "basic",
        defaultAgents: [
          {
            agentId: "ellaOrchestrator",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "review_all",
            maxConcurrentTasks: 2,
            connectedDataSourceIds: [],
            customSettings: {
              responseStyle: "concise",
              decisionMaking: "conservative",
            },
          },
          {
            agentId: "budgetManagerAI",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "balanced",
            maxConcurrentTasks: 1,
            connectedDataSourceIds: [],
            customSettings: {
              alertThreshold: 0.9,
              autoOptimization: false,
            },
          },
          {
            agentId: "timelineManagerAI",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "balanced",
            maxConcurrentTasks: 1,
            connectedDataSourceIds: [],
            customSettings: {
              reminderFrequency: "weekly",
              milestoneTracking: true,
            },
          },
        ],
        defaultGlobalSettings: {
          autonomyLevel: "review_all",
          costBudget: 50, // $50/month
          performanceThresholds: {
            minSuccessRate: 0.85,
            maxResponseTime: 5000, // 5 seconds
          },
        },
        recommendedFor: [
          "Small weddings (< 50 guests)",
          "Budget-conscious clients",
          "Simple venue setups",
        ],
        estimatedMonthlyCost: 35,
        isPublic: true,
      },
      {
        id: "premium-wedding-template",
        name: "Premium Wedding Concierge",
        description:
          "Full-service AI workforce for premium weddings with advanced capabilities",
        category: "premium",
        defaultAgents: [
          {
            agentId: "ellaOrchestrator",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "balanced",
            maxConcurrentTasks: 5,
            connectedDataSourceIds: [],
            customSettings: {
              responseStyle: "detailed",
              decisionMaking: "proactive",
              clientCommunication: "frequent",
            },
          },
          {
            agentId: "budgetManagerAI",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "proactive",
            maxConcurrentTasks: 3,
            connectedDataSourceIds: [],
            customSettings: {
              alertThreshold: 0.8,
              autoOptimization: true,
              investmentAdvice: true,
            },
          },
          {
            agentId: "timelineManagerAI",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "proactive",
            maxConcurrentTasks: 3,
            connectedDataSourceIds: [],
            customSettings: {
              reminderFrequency: "daily",
              milestoneTracking: true,
              contingencyPlanning: true,
            },
          },
          {
            agentId: "guestManagerAI",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "balanced",
            maxConcurrentTasks: 2,
            connectedDataSourceIds: [],
            customSettings: {
              rsvpFollowUp: "auto",
              dietaryTracking: true,
              seatingOptimization: true,
            },
          },
          {
            agentId: "venueManagerAI",
            isEnabled: true,
            llmModel: "gemini-2.5-flash-preview-05-20",
            autonomyLevel: "balanced",
            maxConcurrentTasks: 2,
            connectedDataSourceIds: [],
            customSettings: {
              venueScoring: "comprehensive",
              availabilityMonitoring: true,
              negotiationSupport: true,
            },
          },
          {
            agentId: "stylingAssistantAI",
            isEnabled: true,
            llmModel: "imagen-3.0-generate-002",
            autonomyLevel: "balanced",
            maxConcurrentTasks: 2,
            connectedDataSourceIds: [],
            customSettings: {
              imageGeneration: true,
              trendAnalysis: true,
              colorCoordination: "advanced",
            },
          },
        ],
        defaultGlobalSettings: {
          autonomyLevel: "balanced",
          costBudget: 200, // $200/month
          performanceThresholds: {
            minSuccessRate: 0.92,
            maxResponseTime: 3000, // 3 seconds
          },
        },
        recommendedFor: [
          "Premium weddings (100+ guests)",
          "Luxury venues",
          "Complex coordination needs",
        ],
        estimatedMonthlyCost: 165,
        isPublic: true,
      },
      {
        id: "enterprise-wedding-template",
        name: "Enterprise Wedding Operations",
        description:
          "Maximum AI workforce for high-end weddings and wedding planning businesses",
        category: "enterprise",
        defaultAgents: PREDEFINED_AGENTS.map((agent) => ({
          agentId: agent.id,
          isEnabled: true,
          llmModel: this.getOptimalModelForAgent(agent),
          autonomyLevel: "proactive" as EllaAutonomyLevel,
          maxConcurrentTasks:
            agent.agentType === "orchestrator"
              ? 10
              : agent.agentType === "manager"
              ? 5
              : 3,
          connectedDataSourceIds: [],
          customSettings: this.getEnterpriseSettingsForAgent(agent),
        })),
        defaultGlobalSettings: {
          autonomyLevel: "proactive",
          costBudget: 500, // $500/month
          performanceThresholds: {
            minSuccessRate: 0.95,
            maxResponseTime: 2000, // 2 seconds
          },
        },
        recommendedFor: [
          "Luxury weddings (200+ guests)",
          "Celebrity/VIP events",
          "Multi-day celebrations",
        ],
        estimatedMonthlyCost: 425,
        isPublic: true,
      },
    ]
  }

  // Create a new workforce configuration for a wedding
  static createWorkforceConfig(
    weddingId: string,
    templateId?: string,
    customName?: string
  ): WorkforceConfiguration {
    const template = templateId
      ? this.getDefaultTemplates().find((t) => t.id === templateId)
      : this.getDefaultTemplates().find((t) => t.category === "basic")

    if (!template) {
      throw new Error("Template not found")
    }

    const config: WorkforceConfiguration = {
      id: `wfc-${weddingId}-${Date.now()}`,
      weddingId,
      name: customName || `${template.name} Configuration`,
      templateId: template.id,
      description: `AI workforce configuration based on ${template.name}`,
      agents: template.defaultAgents.map((agent) => ({
        ...agent,
        performanceMetrics: this.getDefaultPerformanceMetrics(),
      })),
      globalSettings: { ...template.defaultGlobalSettings },
      dataSourceConfig: {
        globalDataSourceIds: [],
        domainSpecificSources: {},
      },
      isActive: true,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
    }

    return config
  }

  // Update workforce configuration
  static updateWorkforceConfig(
    config: WorkforceConfiguration,
    updates: Partial<WorkforceConfiguration>
  ): WorkforceConfiguration {
    return {
      ...config,
      ...updates,
      lastModified: new Date().toISOString(),
    }
  }

  // Get optimal model for agent type
  private static getOptimalModelForAgent(agent: PredefinedAgent): LLMModel {
    if (agent.id.includes("styling") || agent.id.includes("vision")) {
      return "imagen-3.0-generate-002"
    }
    if (agent.agentType === "orchestrator") {
      return "gpt-4.1"
    }
    if (agent.agentType === "manager") {
      return "gemini-2.5-flash-preview-05-20"
    }
    return "gemini-2.5-flash-preview-05-20"
  }

  // Get enterprise settings for agent
  private static getEnterpriseSettingsForAgent(
    agent: PredefinedAgent
  ): Record<string, any> {
    const baseSettings = {
      responseStyle: "comprehensive",
      decisionMaking: "proactive",
      learningEnabled: true,
      analyticsTracking: true,
    }

    switch (agent.id) {
      case "ellaOrchestrator":
        return {
          ...baseSettings,
          globalCoordination: true,
          escalationManagement: "auto",
        }
      case "budgetManagerAI":
        return {
          ...baseSettings,
          autoOptimization: true,
          investmentAdvice: true,
          riskAnalysis: true,
        }
      case "timelineManagerAI":
        return {
          ...baseSettings,
          contingencyPlanning: true,
          resourceOptimization: true,
        }
      case "guestManagerAI":
        return {
          ...baseSettings,
          behavioralAnalysis: true,
          personalization: "advanced",
        }
      case "venueManagerAI":
        return {
          ...baseSettings,
          marketAnalysis: true,
          negotiationSupport: true,
        }
      case "stylingAssistantAI":
        return { ...baseSettings, trendForecasting: true, customDesign: true }
      default:
        return baseSettings
    }
  }

  // Get default performance metrics
  private static getDefaultPerformanceMetrics(): AgentPerformanceMetrics {
    return {
      tasksCompleted: 0,
      avgResponseTime: 0,
      successRate: 1.0,
      costPerTask: 0,
      lastActiveDate: new Date().toISOString(),
    }
  }

  // Calculate estimated costs
  static calculateEstimatedCosts(config: WorkforceConfiguration): {
    daily: number
    monthly: number
    breakdown: Record<string, number>
  } {
    const breakdown: Record<string, number> = {}
    let totalMonthlyCost = 0

    config.agents.forEach((agent) => {
      const predefinedAgent = PREDEFINED_AGENTS.find(
        (p) => p.id === agent.agentId
      )
      if (predefinedAgent && agent.isEnabled) {
        const baseCost = predefinedAgent.estimatedCostPerTask || 0.05
        const modelMultiplier = this.getModelCostMultiplier(agent.llmModel)
        const taskMultiplier = agent.maxConcurrentTasks

        const monthlyCost = baseCost * modelMultiplier * taskMultiplier * 30 // 30 days
        breakdown[agent.agentId] = monthlyCost
        totalMonthlyCost += monthlyCost
      }
    })

    return {
      daily: totalMonthlyCost / 30,
      monthly: totalMonthlyCost,
      breakdown,
    }
  }

  // Get cost multiplier for different models
  private static getModelCostMultiplier(model: LLMModel): number {
    switch (model) {
      case "gpt-4.1":
        return 3.0
      case "gemini-2.5-flash-preview-05-20":
        return 2.5
      case "imagen-3.0-generate-002":
        return 2.0
      case "gemini-2.5-flash-preview-05-20":
        return 1.0
      default:
        return 1.5
    }
  }

  // Performance analysis
  static analyzePerformance(config: WorkforceConfiguration): {
    overallScore: number
    bottlenecks: string[]
    recommendations: string[]
    costEfficiency: number
  } {
    const enabledAgents = config.agents.filter((a) => a.isEnabled)
    const metrics = enabledAgents
      .map((a) => a.performanceMetrics)
      .filter(Boolean)

    if (metrics.length === 0) {
      return {
        overallScore: 0,
        bottlenecks: ["No performance data available"],
        recommendations: [
          "Deploy agents and run tasks to collect performance data",
        ],
        costEfficiency: 0,
      }
    }

    const avgSuccessRate =
      metrics.reduce((sum, m) => sum + m!.successRate, 0) / metrics.length
    const avgResponseTime =
      metrics.reduce((sum, m) => sum + m!.avgResponseTime, 0) / metrics.length
    const totalTasks = metrics.reduce((sum, m) => sum + m!.tasksCompleted, 0)

    const overallScore =
      avgSuccessRate * 0.6 +
      ((10000 - Math.min(avgResponseTime, 10000)) / 10000) * 0.4

    const bottlenecks: string[] = []
    const recommendations: string[] = []

    if (
      avgSuccessRate <
      config.globalSettings.performanceThresholds.minSuccessRate
    ) {
      bottlenecks.push("Low success rate detected")
      recommendations.push(
        "Consider upgrading models or adjusting agent settings"
      )
    }

    if (
      avgResponseTime >
      config.globalSettings.performanceThresholds.maxResponseTime
    ) {
      bottlenecks.push("High response times")
      recommendations.push(
        "Reduce concurrent tasks or upgrade to faster models"
      )
    }

    const costs = this.calculateEstimatedCosts(config)
    const costEfficiency = totalTasks > 0 ? totalTasks / costs.monthly : 0

    return {
      overallScore,
      bottlenecks,
      recommendations,
      costEfficiency,
    }
  }

  // Agent recommendations based on wedding details
  static getAgentRecommendations(weddingDetails: {
    guestCount?: string
    initialBudget?: string
    vibe?: string
    location?: string
  }): {
    recommendedTemplate: string
    suggestedAgents: string[]
    reasonings: string[]
  } {
    const guestCount = parseInt(weddingDetails.guestCount || "0")
    const budget = weddingDetails.initialBudget?.toLowerCase() || ""
    const vibe = weddingDetails.vibe?.toLowerCase() || ""

    let recommendedTemplate = "basic-wedding-template"
    const suggestedAgents: string[] = ["ellaOrchestrator"]
    const reasonings: string[] = []

    // Budget-based recommendations
    if (budget.includes("luxury") || budget.includes("unlimited")) {
      recommendedTemplate = "enterprise-wedding-template"
      reasonings.push(
        "Luxury budget detected - enterprise template recommended"
      )
    } else if (budget.includes("premium") || guestCount > 100) {
      recommendedTemplate = "premium-wedding-template"
      reasonings.push(
        "Premium budget or large guest count - full service recommended"
      )
    }

    // Guest count considerations
    if (guestCount > 50) {
      suggestedAgents.push("guestManagerAI")
      reasonings.push("Large guest count requires dedicated guest management")
    }

    // Vibe-based recommendations
    if (
      vibe.includes("elegant") ||
      vibe.includes("formal") ||
      vibe.includes("luxury")
    ) {
      suggestedAgents.push("stylingAssistantAI", "venueManagerAI")
      reasonings.push(
        "Elegant/formal style benefits from styling and venue expertise"
      )
    }

    // Always include core agents
    suggestedAgents.push("budgetManagerAI", "timelineManagerAI")

    return {
      recommendedTemplate,
      suggestedAgents: Array.from(new Set(suggestedAgents)), // Remove duplicates
      reasonings,
    }
  }
}
