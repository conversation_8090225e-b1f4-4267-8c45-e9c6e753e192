import {
  VendorsService,
  categoryToTableMap,
} from "@/src/services/vendorsService"
import { beforeEach, describe, expect, it, mock } from "bun:test"

// Create a proper mock chain for Supabase queries
const createMockQuery = (returnData: any = [], returnError: any = null) => {
  const mockQuery = {
    select: mock(() => mockQuery),
    eq: mock(() => mockQuery),
    range: mock(() => mockQuery),
    or: mock(() => mockQuery),
    ilike: mock(() => mockQuery),
    order: mock(() => mockQuery),
    single: mock(() => ({ data: returnData[0] || null, error: returnError })),
    then: mock((callback: any) =>
      Promise.resolve(callback({ data: returnData, error: returnError }))
    ),
  }

  // Make the query return a promise when called directly
  Object.defineProperty(mockQuery, Symbol.for("Symbol.toStringTag"), {
    value: "Promise",
    configurable: true,
  })

  return mockQuery
}

// Mock the supabaseData client with a proper query chain
const mockSupabaseData = {
  from: mock(() => createMockQuery()),
}

// Mock the supabaseData import
mock.module("@/src/agents/supabaseDataClient", () => ({
  supabaseData: mockSupabaseData,
}))

describe("VendorsService", () => {
  beforeEach(() => {
    // Clear mock calls but don't restore to avoid breaking the module mock
    mockSupabaseData.from.mockClear?.()
  })

  describe("categoryToTableMap", () => {
    it("should map categories to correct table names", () => {
      expect(categoryToTableMap["Venue"]).toBe("venues")
      expect(categoryToTableMap["Photographer"]).toBe("photographers")
      expect(categoryToTableMap["Wedding Planner"]).toBe("wedding_planners")
      expect(categoryToTableMap["Other"]).toBe("venues") // fallback
    })

    it("should have entries for all expected categories", () => {
      const expectedCategories = [
        "Venue",
        "Photographer",
        "Videographer",
        "Caterer",
        "Florist",
        "DJ/Band",
        "Hair & Makeup",
        "Cake",
        "Officiant",
        "Wedding Planner",
        "Transportation",
        "Attire",
        "Stationery",
        "Favors",
        "Other",
      ]

      expectedCategories.forEach((category) => {
        expect(categoryToTableMap[category]).toBeDefined()
      })
    })
  })

  describe("searchVendorsByCategory", () => {
    it("should use the correct table name for a given category", async () => {
      const mockVendors = [
        {
          id: 1,
          business_name: "Test Venue",
          description: "A great venue",
          city: "San Francisco",
          state: "CA",
          website: "https://test.com",
          phone: "555-1234",
          email: "<EMAIL>",
          address: "123 Main St",
        },
      ]

      // Update the mock to return our test data
      mockSupabaseData.from = mock(() => createMockQuery(mockVendors))

      const result = await VendorsService.searchVendorsByCategory("Venue")

      // Verify the table name was used correctly
      expect(mockSupabaseData.from).toHaveBeenCalledWith("venues")
      expect(result).toEqual(mockVendors)
    })

    it("should apply search filters when provided", async () => {
      const searchParams = {
        search: "wedding",
        city: "San Francisco",
        state: "CA",
        limit: 10,
        offset: 0,
      }

      const mockVendors = [
        {
          id: 1,
          business_name: "Wedding Venue",
          description: "A great wedding venue",
          city: "San Francisco",
          state: "CA",
          website: "https://test.com",
          phone: "555-1234",
          email: "<EMAIL>",
          address: "123 Main St",
        },
      ]

      mockSupabaseData.from = mock(() => createMockQuery(mockVendors))

      const result = await VendorsService.searchVendorsByCategory(
        "Venue",
        searchParams
      )

      expect(mockSupabaseData.from).toHaveBeenCalledWith("venues")
      expect(result).toEqual(mockVendors)
    })
  })

  describe("getAvailableCategories", () => {
    it("should return categories with counts", async () => {
      // Mock the count query to return count directly
      const mockCountQuery = {
        select: mock(() => mockCountQuery),
        eq: mock(() => mockCountQuery),
        then: mock((callback: any) =>
          Promise.resolve(callback({ count: 5, error: null }))
        ),
      }

      mockSupabaseData.from = mock(() => mockCountQuery)

      const result = await VendorsService.getAvailableCategories()

      expect(Array.isArray(result)).toBe(true)
      expect(mockSupabaseData.from).toHaveBeenCalled()
      // Each category should be queried
      expect(mockSupabaseData.from.mock.calls.length).toBeGreaterThan(0)
    })
  })
})
