import { IMAGE_MODEL, mockDelay } from "@/src/constants/aiConstants"
import { getGeminiAI } from "@/src/services/geminiService"
import { AppMode } from "@/src/types/index"

let cachedEllaAvatar: string | null = null

export const generateEllaAvatar = async (
  appMode: AppMode = "dev"
): Promise<string> => {
  // Return cached avatar if available
  if (cachedEllaAvatar) {
    return cachedEllaAvatar
  }

  const ai = getGeminiAI()

  if (appMode === "prod" && ai) {
    try {
      const prompt = `Generate a professional, friendly AI assistant avatar image. Create a beautiful, approachable woman with warm eyes and a gentle smile, representing an AI wedding planning assistant named <PERSON>. The image should be professional headshot style with a clean, neutral background. She should appear competent, trustworthy, and warm - someone you'd want helping you plan your wedding. Focus on creating a realistic, professional appearance with a modern, elegant style.`

      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: prompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        sdkResponse.generatedImages &&
        sdkResponse.generatedImages.length > 0
      ) {
        const firstImage = sdkResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          const avatarUrl = `data:image/jpeg;base64,${firstImage.image.imageBytes}`
          cachedEllaAvatar = avatarUrl
          return avatarUrl
        }
      }
    } catch (error) {
      console.error("Error generating Ella's avatar with AI:", error)
    }
  }

  // Fallback to a simple AI-generated placeholder
  await mockDelay(500)
  const fallbackAvatar =
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiNGRjk1QTAiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0QzE0IDUuMSAxMy4xIDYgMTIgNkMxMC45IDYgMTAgNS4xIDEwIDRDMTAgMi45IDEwLjkgMiAxMiAyWk0yMSAxOVYyMEgzVjE5QzMgMTYuMzMgOC4zMyAxNSAxMiAxNUMxNS42NyAxNSAyMSAxNi4zMyAyMSAxOVpNMTIgMTNDMTQuNzYgMTMgMTcgMTAuNzYgMTcgOEMxNyA1LjI0IDE0Ljc2IDMgMTIgM0M5LjI0IDMgNyA1LjI0IDcgOEM3IDEwLjc2IDkuMjQgMTMgMTIgMTNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
  cachedEllaAvatar = fallbackAvatar
  return fallbackAvatar
}

export const generateUserAvatar = async (
  appMode: AppMode = "dev"
): Promise<string> => {
  const ai = getGeminiAI()

  if (appMode === "prod" && ai) {
    try {
      const prompt = `Generate a professional, elegant avatar image representing a wedding planner. Create a competent, stylish professional who specializes in wedding planning. The person should appear confident, organized, and approachable - someone couples would trust to plan their special day. Professional headshot style with a clean, neutral background. Gender-neutral appearance, focusing on professionalism and expertise in event planning.`

      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: prompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        sdkResponse.generatedImages &&
        sdkResponse.generatedImages.length > 0
      ) {
        const firstImage = sdkResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          return `data:image/jpeg;base64,${firstImage.image.imageBytes}`
        }
      }
    } catch (error) {
      console.error("Error generating user avatar with AI:", error)
    }
  }

  // Fallback to a simple AI-generated placeholder
  await mockDelay(500)
  return "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM2MzY2RjEiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDJDMTMuMSAyIDE0IDIuOSAxNCA0QzE0IDUuMSAxMy4xIDYgMTIgNkMxMC45IDYgMTAgNS4xIDEwIDRDMTAgMi45IDEwLjkgMiAxMiAyWk0yMSAxOVYyMEgzVjE5QzMgMTYuMzMgOC4zMyAxNSAxMiAxNUMxNS42NyAxNSAyMSAxNi4zMyAyMSAxOVpNMTIgMTNDMTQuNzYgMTMgMTcgMTAuNzYgMTcgOEMxNyA1LjI0IDE0Ljc2IDMgMTIgM0M5LjI0IDMgNyA1LjI0IDcgOEM3IDEwLjc2IDkuMjQgMTMgMTIgMTNaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+"
}
