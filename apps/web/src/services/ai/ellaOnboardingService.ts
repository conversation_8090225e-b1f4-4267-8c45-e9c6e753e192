import {
  mockDelay,
  plannerOnboardingEllaResponses,
} from "@/src/constants/aiConstants"
import { AppMode, ChatMessage, WeddingDetails } from "@/src/types/index"

const getPlannerEllaOnboardingResponse = (
  step: number,
  userInput?: string
): string => {
  if (step === 1 && userInput)
    return `Great, ${userInput}! It's a pleasure to start planning for them. When are they thinking of having the wedding? A specific date, or perhaps a season and year?`
  return (
    plannerOnboardingEllaResponses[step] ||
    "I'm gathering the details for this new wedding entry... Just a moment!"
  )
}

export const fetchPlannerEllaOnboardingReply = async (
  step: number,
  userInput?: string,
  currentDetails?: Partial<WeddingDetails>,
  appMode: AppMode = "dev"
): Promise<ChatMessage> => {
  if (appMode === "dev") {
    await mockDelay(1000 + Math.random() * 1000)
  }

  let text = getPlannerEllaOnboardingResponse(step, userInput)

  if (step === 6 && currentDetails) {
    text = `Thank you! For your clients, ${
      currentDetails.coupleNames || "this couple"
    }, based on a ${currentDetails.vibe || "lovely"} vibe for around ${
      currentDetails.guestCount || "their"
    } guests in ${currentDetails.location || "their chosen location"} around ${
      currentDetails.weddingDate || "their date"
    }, I'm so excited!

I'm preparing a 'Vision Snapshot' for them. It'll be ready in a moment! In the meantime, I'm setting up their Wedding Portal within your planner dashboard.`
  }

  return {
    id: `ella-planner-onboarding-${Date.now()}`,
    text: text,
    sender: "ella",
    timestamp: new Date(),
  }
}
