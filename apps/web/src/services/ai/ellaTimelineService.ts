import { TEXT_MODEL, mockDelay } from "@/src/constants/aiConstants"
import { getGeminiAI, shouldUseMockData } from "@/src/services/geminiService"
import { AppMode, Task, WeddingDetails } from "@/src/types/index"
import { parseJsonResponse } from "@/src/utils/aiResponseParsers"
import { parseDateStringToLocalMidnight } from "@/src/utils/dateUtils"
import { generateClientSideId } from "@/src/utils/idUtils"

const generateComprehensiveMockTimeline = (details: WeddingDetails): Task[] => {
  const weddingDateObj =
    parseDateStringToLocalMidnight(details.weddingDate) ||
    new Date(new Date().setFullYear(new Date().getFullYear() + 1)) // Default to one year from now if no date

  const calculateDueDate = (
    monthsBefore: number,
    daysBefore: number = 0
  ): string => {
    const dueDate = new Date(weddingDateObj.getTime())
    dueDate.setUTCMonth(dueDate.getUTCMonth() - monthsBefore)
    dueDate.setUTCDate(dueDate.getUTCDate() - daysBefore)
    return dueDate.toISOString().split("T")[0]
  }

  const tasks: Task[] = [
    // 12+ Months Out
    {
      id: generateClientSideId(),
      title: "Define Wedding Vision & Style with Client",
      description: "Discuss overall theme, colors, and desired atmosphere.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Establish Client's Preliminary Budget",
      description: "Work with client to set a realistic initial budget.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Draft Initial Guest List (Client Task)",
      description: "Client to provide a rough estimate of guest numbers.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Select Potential Wedding Dates/Season with Client",
      description: "Narrow down preferred dates or season.",
      dueDate: calculateDueDate(11),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Research & Scout Potential Venues",
      description:
        "Identify venues matching client's style, capacity, and budget.",
      dueDate: calculateDueDate(11),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 9-12 Months Out
    {
      id: generateClientSideId(),
      title: "Book Wedding Venue with Client",
      description: "Finalize and book the chosen wedding venue.",
      dueDate: calculateDueDate(10),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Photographer",
      description: "Research, interview, and book photographer.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Videographer",
      description: "Research, interview, and book videographer.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Caterer (if separate)",
      description: "Select and book catering services.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Book Officiant",
      description: "Secure an officiant for the ceremony.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Start Wedding Website (Client Task)",
      description: "Client to set up basic wedding website with key details.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Assemble Client's Vision Board",
      description: "Collect visual inspiration for decor, attire, etc.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Choose Wedding Party (Client Task)",
      description: "Client to decide on bridesmaids, groomsmen, etc.",
      dueDate: calculateDueDate(8),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 6-9 Months Out
    {
      id: generateClientSideId(),
      title: "Hire Florist",
      description: "Select and book a florist.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Band/DJ",
      description: "Book entertainment for reception.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Order Save-the-Dates (Client Task)",
      description: "Design and order save-the-date cards.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Guest List (Client Task)",
      description: "Client to confirm final guest list and addresses.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Shop for Wedding Attire (Client Task)",
      description: "Client to begin looking for wedding dress, suits, etc.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Arrange Hotel Room Blocks for Guests",
      description: "Reserve blocks of hotel rooms for out-of-town guests.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 4-6 Months Out
    {
      id: generateClientSideId(),
      title: "Send Save-the-Dates (Client Task)",
      description: "Client to mail out save-the-date cards.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Order Wedding Invitations",
      description: "Design and order wedding invitations and stationery.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Book Hair & Makeup Artists",
      description: "Secure stylists for the wedding day.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Ceremony Details",
      description: "Outline ceremony structure, readings, music with client.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Reception Details",
      description: "Outline reception timeline, decor, layout with client.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Order Wedding Cake",
      description: "Taste and order the wedding cake.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Arrange Transportation",
      description: "Book transportation for wedding party and/or guests.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Purchase Wedding Rings (Client Task)",
      description: "Client to select and purchase wedding bands.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 2-3 Months Out
    {
      id: generateClientSideId(),
      title: "Mail Wedding Invitations (Client Task)",
      description: "Client to send out wedding invitations.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Menu with Caterer",
      description: "Confirm final food and beverage selections.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Schedule Attire Fittings (Client Task)",
      description: "Client to schedule dress/suit fittings.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Floral Selections",
      description: "Confirm all floral arrangements with florist.",
      dueDate: calculateDueDate(2, 15),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Rehearsal Dinner",
      description: "Organize details for the rehearsal dinner.",
      dueDate: calculateDueDate(2, 15),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Create Seating Chart",
      description: "Develop the guest seating arrangement for reception.",
      dueDate: calculateDueDate(2),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 1 Month Out
    {
      id: generateClientSideId(),
      title: "Apply for Marriage License (Client Task)",
      description: "Client to obtain marriage license.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Confirm Final Details with All Vendors",
      description: "Reconfirm arrival times, services, and payments.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Wedding Day Timeline",
      description: "Create a detailed schedule for the wedding day.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Confirm RSVP Count with Venue/Caterer",
      description: "Provide final guest numbers.",
      dueDate: calculateDueDate(0, 21),
      isCompleted: false,
      assignedTo: "Planner",
    }, // 3 weeks before

    // 1-2 Weeks Out
    {
      id: generateClientSideId(),
      title: "Review Wedding Day Timeline with Wedding Party",
      description: "Ensure everyone knows the schedule.",
      dueDate: calculateDueDate(0, 7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Pick Up Wedding Attire (Client Task)",
      description: "Client to collect cleaned/altered attire.",
      dueDate: calculateDueDate(0, 3),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // Wedding Week
    {
      id: generateClientSideId(),
      title: "Final Vendor Confirmations & Payments",
      description: "Ensure all vendors are set and final payments made.",
      dueDate: calculateDueDate(0, 2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Prepare Tip Envelopes",
      description: "Organize tips for vendors.",
      dueDate: calculateDueDate(0, 2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Rehearsal & Rehearsal Dinner",
      description: "Conduct ceremony rehearsal and host dinner.",
      dueDate: calculateDueDate(0, 1),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },

    // Wedding Day
    {
      id: generateClientSideId(),
      title: "Oversee Venue Setup & Vendor Coordination",
      description: "Manage all on-site logistics.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Manage Wedding Day Timeline Execution",
      description: "Ensure everything runs smoothly and on schedule.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },

    // Post-Wedding
    {
      id: generateClientSideId(),
      title: "Oversee Venue Teardown/Cleanup",
      description: "Manage post-event cleanup as per contract.",
      dueDate: calculateDueDate(-0, -1),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    }, // Negative months = after wedding
    {
      id: generateClientSideId(),
      title: "Return Rented Items",
      description: "Ensure all rentals are returned on time.",
      dueDate: calculateDueDate(-0, -2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Send Thank You Notes (Client Task)",
      description: "Client to write and send thank you cards to guests.",
      dueDate: calculateDueDate(-1, 0),
      isCompleted: false,
      assignedTo: "Couple",
    }, // 1 month after
    {
      id: generateClientSideId(),
      title: "Follow Up with Photographer/Videographer for Deliverables",
      description: "Check on photo/video delivery timeline.",
      dueDate: calculateDueDate(-1, -15),
      isCompleted: false,
      assignedTo: "Planner",
    }, // 1.5 months after
  ]
  return tasks
}

const mockEllaGenerateInitialTimeline = async (
  details: WeddingDetails
): Promise<Task[]> => {
  await mockDelay(1000)
  return generateComprehensiveMockTimeline(details)
}

export const ellaGenerateInitialTimeline = async (
  details: WeddingDetails,
  appMode: AppMode
): Promise<Task[]> => {
  const prompt = `
    For a wedding planner managing a wedding for ${
      details.coupleNames
    } on/around ${
    details.weddingDate
  } (this is the wedding date, format YYYY-MM-DD). The wedding vibe is "${
    details.vibe
  }".
    Generate a comprehensive wedding planning timeline with 70-100 tasks.
    Tasks should cover all typical phases from 12+ months before the wedding to post-wedding activities.
    For each task, provide a "title" (string).
    Optionally, include "description" (string), and "assignedTo" (string - e.g., "Planner", "${
      details.coupleNames?.split(" & ")[0]
    }", "Couple").
    Crucially, if you provide a "dueDate", it MUST be in YYYY-MM-DD format and calculated appropriately relative to the wedding date: ${
      details.weddingDate
    }. Default to "Planner" or "Couple" for assignedTo if unsure.
    Prioritize a logical flow and cover a wide range of typical wedding planning activities.
    VERY IMPORTANT: Respond ONLY with a valid JSON array of objects. Do NOT include any other text, explanations, or thoughts.
    Example JSON object within the array: {"title": "Book Venue", "dueDate": "YYYY-MM-DD", "description": "Secure the main location.", "assignedTo": "Planner"}
    (Replace YYYY-MM-DD in the example with a date calculated relative to ${
      details.weddingDate
    })
  `

  const ai = getGeminiAI()
  if (appMode === "prod" && getGeminiAI()) {
    try {
      console.log(
        "AI: Generating comprehensive initial timeline for client:",
        details
      )
      const response = await ai!.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      const parsedItems = parseJsonResponse<
        Array<Omit<Task, "id" | "isCompleted">>
      >(response.text || "", [])
      if (parsedItems.length < 50) {
        // If AI returns too few, use mock as fallback
        console.warn(
          `AI returned only ${parsedItems.length} tasks, falling back to comprehensive mock.`
        )
        return mockEllaGenerateInitialTimeline(details)
      }
      return parsedItems.map((item) => ({
        ...item,
        id: generateClientSideId(),
        isCompleted: false,
      }))
    } catch (error) {
      console.error("AI Error (ellaGenerateInitialTimeline):", error)
      return mockEllaGenerateInitialTimeline(details)
    }
  }
  return mockEllaGenerateInitialTimeline(details)
}

export const ellaSuggestTimelineTask = async (
  currentTasks: Task[],
  details: WeddingDetails,
  appMode: AppMode
): Promise<Partial<Task>> => {
  const existingTitles = currentTasks.map((task) => task.title).join(", ")
  const prompt = `
    For a wedding planner managing ${details.coupleNames}'s wedding on ${
    details.weddingDate
  }. Wedding vibe: ${details.vibe}, Guests: ${details.guestCount}, Location: ${
    details.location
  }.
    Current tasks: ${existingTitles || "None yet"}.
    Suggest ONE new important task that would be valuable for this wedding timeline but is missing from current tasks.
    VERY IMPORTANT: Respond ONLY with a valid JSON object with "title" (string), optionally "description" (string), "dueDate" (YYYY-MM-DD format), and "assignedTo" (string). Do NOT include any other text, explanations, or thoughts.
    Example JSON: {"title": "Book Florist", "description": "Select and book floral arrangements", "dueDate": "2024-06-15", "assignedTo": "Planner"}
  `

  const ai = getGeminiAI()
  if (shouldUseMockData(appMode)) {
    await mockDelay(1500 + Math.random() * 1000)
    const mockSuggestions = [
      {
        title: "Schedule Venue Walkthrough",
        description: "Final venue inspection with client",
        assignedTo: "Planner",
      },
      {
        title: "Order Wedding Favors",
        description: "Select and order guest favors",
        assignedTo: "Couple",
      },
      {
        title: "Book Transportation",
        description: "Arrange transportation for wedding party",
        assignedTo: "Planner",
      },
      {
        title: "Plan Rehearsal Dinner",
        description: "Organize rehearsal dinner details",
        assignedTo: "Planner/Couple",
      },
    ]
    const availableSuggestions = mockSuggestions.filter(
      (suggestion) =>
        !currentTasks.some((existing) =>
          existing.title
            .toLowerCase()
            .includes(suggestion.title.toLowerCase().split(" ")[0])
        )
    )
    return availableSuggestions.length > 0 ? availableSuggestions[0] : {}
  }

  try {
    const response = await ai!.models.generateContent({
      model: TEXT_MODEL,
      contents: prompt,
      config: { responseMimeType: "application/json" },
    })
    return parseJsonResponse<Partial<Task>>(response.text || "", {})
  } catch (error) {
    console.error("AI Error (ellaSuggestTimelineTask):", error)
    return {}
  }
}

export const ellaBreakdownTask = async (
  task: Task,
  weddingDetails: WeddingDetails,
  appMode: AppMode
): Promise<{
  subTasks: Array<Omit<Task, "id" | "isCompleted">>
  parentTaskUpdate?: Partial<Omit<Task, "id" | "isCompleted">>
}> => {
  const prompt = `
    For a wedding planner managing ${
      weddingDetails.coupleNames
    }'s wedding. Break down this task into 2-4 specific sub-tasks:
    
    Task: "${task.title}"
    Description: "${task.description || "No description"}"
    Due Date: ${task.dueDate}
    Assigned To: ${task.assignedTo}
    
    Wedding context: ${weddingDetails.vibe} style, ${
    weddingDetails.guestCount
  } guests, ${weddingDetails.location}.
    
    Provide sub-tasks that are actionable and specific. Each sub-task should have a "title", optionally "description", "dueDate" (YYYY-MM-DD), and "assignedTo".
    Also suggest if the parent task title/description should be updated to be more of an overview.
    
    VERY IMPORTANT: Respond ONLY with a valid JSON object with "subTasks" (array) and optionally "parentTaskUpdate" (object). Do NOT include any other text.
    Example: {"subTasks": [{"title": "Research vendors", "dueDate": "2024-06-01", "assignedTo": "Planner"}], "parentTaskUpdate": {"title": "Coordinate Catering (Overview)"}}
  `

  const ai = getGeminiAI()
  if (shouldUseMockData(appMode)) {
    await mockDelay(2000 + Math.random() * 1000)
    const mockSubTasks = [
      {
        title: `Research options for: ${task.title}`,
        description: "Initial research and vendor identification",
        assignedTo: task.assignedTo,
      },
      {
        title: `Get quotes for: ${task.title}`,
        description: "Obtain pricing and availability",
        assignedTo: task.assignedTo,
      },
      {
        title: `Finalize selection for: ${task.title}`,
        description: "Make final decision and book",
        assignedTo: task.assignedTo,
      },
    ]
    return {
      subTasks: mockSubTasks,
      parentTaskUpdate: {
        title: `${task.title} (Overview)`,
        description: "Coordinate all aspects of this task",
      },
    }
  }

  try {
    const response = await ai!.models.generateContent({
      model: TEXT_MODEL,
      contents: prompt,
      config: { responseMimeType: "application/json" },
    })
    return parseJsonResponse<{
      subTasks: Array<Omit<Task, "id" | "isCompleted">>
      parentTaskUpdate?: Partial<Omit<Task, "id" | "isCompleted">>
    }>(response.text || "", { subTasks: [] })
  } catch (error) {
    console.error("AI Error (ellaBreakdownTask):", error)
    return { subTasks: [] }
  }
}
