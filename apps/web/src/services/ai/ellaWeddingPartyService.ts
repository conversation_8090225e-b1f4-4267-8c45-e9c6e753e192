import { IMAGE_MODEL, mockDelay } from "@/src/constants/aiConstants"
import { getGeminiAI } from "@/src/services/geminiService"
import { AppMode, WeddingDetails } from "@/src/types/index"

export interface WeddingPartyMember {
  id: string
  name: string
  role: string
  avatar: string
  description?: string
}

export const generateWeddingPartyAvatar = async (
  memberName: string,
  role: string,
  weddingDetails: WeddingDetails,
  appMode: AppMode = "dev"
): Promise<string> => {
  const ai = getGeminiAI()
  const weddingVibe = weddingDetails.vibe || "elegant"
  const isForMale =
    role.toLowerCase().includes("man") ||
    role.toLowerCase().includes("groomsman") ||
    role.toLowerCase().includes("best man") ||
    role.toLowerCase().includes("usher")

  if (appMode === "prod" && ai) {
    try {
      const prompt = `Generate a professional, elegant portrait-style image of a ${
        isForMale ? "handsome man" : "beautiful woman"
      } who would be a wedding party member. This person is the ${role} for a ${weddingVibe} wedding. Create a clean, professional headshot with a neutral background, appropriate for a wedding party member. The person should be well-dressed and looking directly at the camera with a warm, friendly expression. Focus on creating a realistic, professional appearance suitable for wedding planning materials.`

      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: prompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        sdkResponse.generatedImages &&
        sdkResponse.generatedImages.length > 0
      ) {
        const firstImage = sdkResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          return `data:image/jpeg;base64,${firstImage.image.imageBytes}`
        }
      }
    } catch (error) {
      console.error("Error generating wedding party avatar with AI:", error)
    }
  }

  // Fallback to SVG avatar placeholders
  await mockDelay(1000)
  const avatarColor = isForMale ? "#3B82F6" : "#EC4899"
  const svgAvatar = `data:image/svg+xml;base64,${btoa(`
    <svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle cx="48" cy="48" r="48" fill="${avatarColor}"/>
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" x="24" y="24">
        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19C3 16.33 8.33 15 12 15C15.67 15 21 16.33 21 19ZM12 13C14.76 13 17 10.76 17 8C17 5.24 14.76 3 12 3C9.24 3 7 5.24 7 8C7 10.76 9.24 13 12 13Z" fill="white"/>
      </svg>
    </svg>
  `)}`
  return svgAvatar
}

export const generateInitialWeddingParty = async (
  weddingDetails: WeddingDetails,
  appMode: AppMode = "dev"
): Promise<WeddingPartyMember[]> => {
  const coupleNames = weddingDetails.coupleNames || "the couple"
  const [person1, person2] = coupleNames.split(" & ")

  // Create typical wedding party structure
  const partyMembers: Omit<WeddingPartyMember, "avatar">[] = [
    {
      id: "wp1",
      name: "Sophia Martinez",
      role: "Maid of Honor",
      description: `${person2 || "Bride"}'s best friend and closest confidant`,
    },
    {
      id: "wp2",
      name: "Alex Chen",
      role: "Best Man",
      description: `${person1 || "Groom"}'s brother and lifelong friend`,
    },
    {
      id: "wp3",
      name: "Olivia Davis",
      role: "Bridesmaid",
      description: `College roommate and dear friend`,
    },
    {
      id: "wp4",
      name: "James Wilson",
      role: "Groomsman",
      description: `Childhood friend and wedding support`,
    },
    {
      id: "wp5",
      name: "Emma Rodriguez",
      role: "Bridesmaid",
      description: `Work colleague turned close friend`,
    },
    {
      id: "wp6",
      name: "Michael Thompson",
      role: "Groomsman",
      description: `College buddy and adventure partner`,
    },
  ]

  // Generate avatars for each member
  const membersWithAvatars: WeddingPartyMember[] = []

  for (const member of partyMembers) {
    const avatar = await generateWeddingPartyAvatar(
      member.name,
      member.role,
      weddingDetails,
      appMode
    )

    membersWithAvatars.push({
      ...member,
      avatar,
    })
  }

  return membersWithAvatars
}

export const generateSingleWeddingPartyMember = async (
  name: string,
  role: string,
  weddingDetails: WeddingDetails,
  appMode: AppMode = "dev"
): Promise<WeddingPartyMember> => {
  const avatar = await generateWeddingPartyAvatar(
    name,
    role,
    weddingDetails,
    appMode
  )

  return {
    id: `wp-${Date.now()}`,
    name,
    role,
    avatar,
    description: `${role} for the wedding`,
  }
}
