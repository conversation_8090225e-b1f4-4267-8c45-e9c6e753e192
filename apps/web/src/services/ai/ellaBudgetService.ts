import { TEXT_MODEL, mockDelay } from "@/src/constants/aiConstants"
import { getGeminiAI, shouldUseMockData } from "@/src/services/geminiService"
import {
  AppMode,
  BudgetItem,
  EllaAutonomyLevel,
  WeddingDetails,
} from "@/src/types/index"
import { parseJsonResponse } from "@/src/utils/aiResponseParsers"
import { generateClientSideId } from "@/src/utils/idUtils"

const mockEllaSuggestInitialBudget = async (
  details: WeddingDetails
): Promise<BudgetItem[]> => {
  await mockDelay(1000)
  const baseBudget = details.initialBudget || 50000
  return [
    {
      id: generateClientSideId(),
      category: "Venue",
      estimatedCost: baseBudget * 0.3,
      actualCost: 0,
      isPaid: false,
      vendor: "",
      paymentDueDate: "",
    },
    {
      id: generateClientSideId(),
      category: "Catering",
      estimatedCost: baseBudget * 0.25,
      actualCost: 0,
      isPaid: false,
      vendor: "",
      paymentDueDate: "",
    },
    {
      id: generateClientSideId(),
      category: "Photography",
      estimatedCost: baseBudget * 0.1,
      actualCost: 0,
      isPaid: false,
      vendor: "",
      paymentDueDate: "",
    },
  ]
}

export const ellaSuggestInitialBudget = async (
  details: WeddingDetails,
  appMode: AppMode
): Promise<BudgetItem[]> => {
  const prompt = `
    For a wedding planner setting up a client's budget. Client details: Vibe: ${
      details.vibe
    }, Guests: ${details.guestCount}, Location: ${
    details.location
  }, Initial Budget Idea: ${
    details.initialBudget ? "$" + details.initialBudget : "N/A"
  }.
    Generate 3-5 essential wedding budget categories and estimated costs for this client.
    VERY IMPORTANT: Respond ONLY with a valid JSON array of objects, where each object has "category" (string) and "estimatedCost" (number). Do NOT include any other text, explanations, or thoughts.
    Example JSON: [{"category": "Venue", "estimatedCost": 15000}, {"category": "Catering", "estimatedCost": 10000}]
  `

  const ai = getGeminiAI()
  if (appMode === "prod" && getGeminiAI()) {
    try {
      console.log("AI: Generating initial budget for client:", details)
      const response = await ai!.models.generateContent({
        model: TEXT_MODEL,
        contents: prompt,
        config: { responseMimeType: "application/json" },
      })
      const parsedItems = parseJsonResponse<
        Array<
          Omit<
            BudgetItem,
            "id" | "actualCost" | "isPaid" | "vendor" | "paymentDueDate"
          >
        >
      >(response.text || "", [])
      return parsedItems.map((item) => ({
        ...item,
        id: generateClientSideId(),
        actualCost: 0,
        isPaid: false,
        vendor: "",
        paymentDueDate: "",
      }))
    } catch (error) {
      console.error("AI Error (ellaSuggestInitialBudget):", error)
      return mockEllaSuggestInitialBudget(details)
    }
  }
  return mockEllaSuggestInitialBudget(details)
}

export const ellaSuggestBudgetItem = async (
  currentBudget: BudgetItem[],
  details: WeddingDetails,
  appMode: AppMode,
  autonomyLevel: EllaAutonomyLevel = "balanced"
): Promise<Partial<BudgetItem>> => {
  const existingCategories = currentBudget
    .map((item) => item.category)
    .join(", ")
  const totalBudgetSoFar = currentBudget.reduce(
    (sum, item) => sum + item.estimatedCost,
    0
  )

  const prompt = `
    For a wedding planner managing a client's budget. Client details: ${
      details.coupleNames
    }, Vibe: ${details.vibe}, Guests: ${details.guestCount}, Location: ${
    details.location
  }, Initial Budget: ${
    details.initialBudget ? "$" + details.initialBudget : "N/A"
  }.
    Current budget categories: ${existingCategories || "None yet"}.
    Current total estimated: $${totalBudgetSoFar}.
    Suggest ONE new budget category that would be important for this wedding but is missing from their current budget.
    Consider the autonomy level: ${autonomyLevel} (conservative = essential items only, balanced = mix of essential and nice-to-have, proactive = comprehensive including details).
    VERY IMPORTANT: Respond ONLY with a valid JSON object with "category" (string) and "estimatedCost" (number). Do NOT include any other text, explanations, or thoughts.
    Example JSON: {"category": "Flowers", "estimatedCost": 2500}
  `

  const ai = getGeminiAI()
  if (shouldUseMockData(appMode)) {
    await mockDelay(1500 + Math.random() * 1000)
    const mockSuggestions = [
      {
        category: "Flowers & Decor",
        estimatedCost: Math.round((details.initialBudget || 50000) * 0.08),
      },
      {
        category: "Music & Entertainment",
        estimatedCost: Math.round((details.initialBudget || 50000) * 0.06),
      },
      {
        category: "Transportation",
        estimatedCost: Math.round((details.initialBudget || 50000) * 0.03),
      },
      {
        category: "Wedding Favors",
        estimatedCost: Math.round((details.initialBudget || 50000) * 0.02),
      },
    ]
    const availableSuggestions = mockSuggestions.filter(
      (suggestion) =>
        !currentBudget.some((existing) =>
          existing.category
            .toLowerCase()
            .includes(suggestion.category.toLowerCase().split(" ")[0])
        )
    )
    return availableSuggestions.length > 0 ? availableSuggestions[0] : {}
  }

  try {
    const response = await ai!.models.generateContent({
      model: TEXT_MODEL,
      contents: prompt,
      config: { responseMimeType: "application/json" },
    })
    return parseJsonResponse<Partial<BudgetItem>>(response.text || "", {})
  } catch (error) {
    console.error("AI Error (ellaSuggestBudgetItem):", error)
    return {}
  }
}

export const ellaSuggestBudgetSolutions = async (
  problemItem: BudgetItem,
  allBudgetItems: BudgetItem[],
  weddingDetails: WeddingDetails,
  appMode: AppMode
): Promise<string> => {
  const overageAmount = problemItem.actualCost - problemItem.estimatedCost
  const totalBudget = allBudgetItems.reduce(
    (sum, item) => sum + item.estimatedCost,
    0
  )
  const totalActual = allBudgetItems.reduce(
    (sum, item) => sum + item.actualCost,
    0
  )

  const prompt = `
    As Ella, a helpful AI wedding planning assistant, provide budget solutions for a client.
    
    Problem: The "${
      problemItem.category
    }" category is over budget by $${overageAmount.toFixed(2)} (estimated: $${
    problemItem.estimatedCost
  }, actual: $${problemItem.actualCost}).
    
    Wedding context: ${weddingDetails.coupleNames}, ${
    weddingDetails.vibe
  } style, ${weddingDetails.guestCount} guests, ${weddingDetails.location}.
    Total budget overview: Estimated $${totalBudget}, Current actual $${totalActual}.
    
    Provide 2-3 practical, specific solutions to address this overage. Be encouraging and solution-focused.
    Consider options like: reallocating from other categories, finding cost savings, adjusting scope, or increasing budget if justified.
    
    Keep the tone friendly, professional, and supportive. Address the client directly.
  `

  const ai = getGeminiAI()
  if (shouldUseMockData(appMode)) {
    await mockDelay(2000 + Math.random() * 1000)
    return `Hi there! I see the ${
      problemItem.category
    } category is running about $${overageAmount.toFixed(
      2
    )} over budget. Here are some solutions I'd recommend:

1. **Reallocate from other categories**: Look at categories where you might have some wiggle room to shift funds toward ${
      problemItem.category
    }.

2. **Explore cost-saving alternatives**: For ${
      problemItem.category
    }, consider if there are ways to achieve the same impact with a more budget-friendly approach.

3. **Prioritize what matters most**: If this category is really important to your vision, we could adjust the scope of less critical items to accommodate this investment.

Remember, it's your special day, and we'll find a way to make it beautiful within your means! 💕`
  }

  try {
    const response = await ai!.models.generateContent({
      model: TEXT_MODEL,
      contents: prompt,
    })
    return (
      response.text ||
      "I'm having trouble generating budget solutions right now. Please try again!"
    )
  } catch (error) {
    console.error("AI Error (ellaSuggestBudgetSolutions):", error)
    return "I'm having trouble generating budget solutions right now. Please try again!"
  }
}
