// Core AI service (moved to main geminiService)
export {
  getGeminiAI,
  isAIAvailable,
  shouldUseMockData,
} from "@/src/services/geminiService"

// Onboarding services
export { fetchPlannerEllaOnboardingReply } from "@/src/services/ai/ellaOnboardingService"

// Vision and image services
export {
  ellaSuggestVisionBoardImage,
  generateLookIdeas,
  generateVisionSnapshot,
} from "@/src/services/ai/ellaVisionService"

// Wedding party services
export {
  generateInitialWeddingParty,
  generateSingleWeddingPartyMember,
  generateWeddingPartyAvatar,
  type WeddingPartyMember,
} from "@/src/services/ai/ellaWeddingPartyService"

// Avatar services
export {
  generateEllaAvatar,
  generateUserAvatar,
} from "@/src/services/ai/ellaAvatarService"

// Budget services
export {
  ellaSuggestBudgetItem,
  ellaSuggestBudgetSolutions,
  ellaSuggestInitialBudget,
} from "@/src/services/ai/ellaBudgetService"

// Timeline services
export {
  ellaBreakdownTask,
  ellaGenerateInitialTimeline,
  ellaSuggestTimelineTask,
} from "@/src/services/ai/ellaTimelineService"

// Re-export remaining functions from original service that haven't been moved yet
export {
  askEllaGeneral,
  ellaDraftVendorInquiry,
  ellaSuggestGuest,
  ellaSuggestVendor,
  getAIAgentActivity,
} from "@/src/services/geminiService"
