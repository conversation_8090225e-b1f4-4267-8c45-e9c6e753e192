import { IMAGE_MODEL, mockDelay } from "@/src/constants/aiConstants"
import { getGeminiAI, shouldUseMockData } from "@/src/services/geminiService"
import {
  AppMode,
  ChatMessage,
  GenerateImagesResponse as UIGenerateImagesResponse,
  VisionBoardItem,
  WeddingDetails,
} from "@/src/types/index"
import { generateClientSideId } from "@/src/utils/idUtils"

export const generateVisionSnapshot = async (
  details: WeddingDetails,
  appMode: AppMode = "dev"
): Promise<ChatMessage> => {
  const coupleNames = details.coupleNames || "the couple"
  const vibe = details.vibe || "beautiful"
  const ai = getGeminiAI()

  if (appMode === "prod" && ai) {
    try {
      const prompt = `Generate a beautiful, inspiring wedding image that captures a ${vibe} aesthetic. Focus on wedding decor, ambiance, ceremony setup, or reception styling that embodies this vibe. Create something that would inspire ${coupleNames} for their ${vibe} wedding.`

      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: prompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        sdkResponse.generatedImages &&
        sdkResponse.generatedImages.length > 0
      ) {
        const firstImage = sdkResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          return {
            id: `vision-${Date.now()}`,
            text: `Here's a sneak peek of the vision for ${coupleNames}! A little something to get the inspiration flowing for their ${vibe} wedding. ✨`,
            sender: "ella",
            timestamp: new Date(),
            imageUrl: `data:image/jpeg;base64,${firstImage.image.imageBytes}`,
          }
        }
      }
    } catch (error) {
      console.error("Error generating vision snapshot with AI:", error)
    }
  }

  // Fallback to a simple wedding-themed SVG
  await mockDelay(2000)
  const weddingIcon = `data:image/svg+xml;base64,${btoa(`
    <svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="bgGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#FEF7ED;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#FDF2F8;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#F3E8FF;stop-opacity:1" />
        </linearGradient>
        <linearGradient id="centerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:0.8" />
          <stop offset="100%" style="stop-color:#EC4899;stop-opacity:0.8" />
        </linearGradient>
      </defs>
      <rect width="600" height="400" fill="url(#bgGrad)"/>
      
      <!-- Decorative border -->
      <rect x="20" y="20" width="560" height="360" fill="none" stroke="#D97706" stroke-width="2" opacity="0.3" rx="10"/>
      
      <!-- Central wedding motif -->
      <g transform="translate(300, 200)">
        <!-- Wedding rings -->
        <circle cx="-30" cy="-10" r="25" fill="none" stroke="#D97706" stroke-width="4" opacity="0.9"/>
        <circle cx="30" cy="-10" r="25" fill="none" stroke="#D97706" stroke-width="4" opacity="0.9"/>
        <circle cx="-30" cy="-10" r="18" fill="none" stroke="#F59E0B" stroke-width="2"/>
        <circle cx="30" cy="-10" r="18" fill="none" stroke="#F59E0B" stroke-width="2"/>
        
        <!-- Decorative flowers around rings -->
        <g transform="translate(-60, -40)">
          <circle cx="0" cy="0" r="8" fill="#F472B6" opacity="0.7"/>
          <circle cx="-5" cy="-5" r="5" fill="#FBBF24" opacity="0.8"/>
          <circle cx="5" cy="-5" r="5" fill="#FBBF24" opacity="0.8"/>
          <circle cx="-5" cy="5" r="5" fill="#FBBF24" opacity="0.8"/>
          <circle cx="5" cy="5" r="5" fill="#FBBF24" opacity="0.8"/>
        </g>
        
        <g transform="translate(60, -40)">
          <circle cx="0" cy="0" r="8" fill="#F472B6" opacity="0.7"/>
          <circle cx="-5" cy="-5" r="5" fill="#FBBF24" opacity="0.8"/>
          <circle cx="5" cy="-5" r="5" fill="#FBBF24" opacity="0.8"/>
          <circle cx="-5" cy="5" r="5" fill="#FBBF24" opacity="0.8"/>
          <circle cx="5" cy="5" r="5" fill="#FBBF24" opacity="0.8"/>
        </g>
        
        <!-- Hearts -->
        <g transform="translate(-80, 20)">
          <path d="M0,8 C0,3 4,0 8,0 C12,0 16,3 16,8 C16,13 8,20 8,20 C8,20 0,13 0,8 Z" fill="#EC4899" opacity="0.6"/>
        </g>
        
        <g transform="translate(64, 20)">
          <path d="M0,8 C0,3 4,0 8,0 C12,0 16,3 16,8 C16,13 8,20 8,20 C8,20 0,13 0,8 Z" fill="#EC4899" opacity="0.6"/>
        </g>
      </g>
      
      <text x="300" y="320" text-anchor="middle" fill="#BE185D" font-family="serif" font-size="28" font-weight="600">${vibe} Wedding Vision</text>
      <text x="300" y="350" text-anchor="middle" fill="#A855F7" font-family="serif" font-size="18">Beautiful AI-Generated Inspiration Coming Soon</text>
    </svg>
  `)}`

  return {
    id: `vision-mock-${Date.now()}`,
    text: `Here's a sneak peek of the vision for ${coupleNames}! A little something to get the inspiration flowing for their ${vibe} wedding. ✨`,
    sender: "ella",
    timestamp: new Date(),
    imageUrl: weddingIcon,
  }
}

export const generateLookIdeas = async (
  preferences: string,
  appMode: AppMode = "dev",
  weddingDetails?: WeddingDetails | null
): Promise<UIGenerateImagesResponse> => {
  const ai = getGeminiAI()
  const forWhom = weddingDetails?.coupleNames
    ? `for ${weddingDetails.coupleNames}'s wedding`
    : "for a wedding look"

  if (appMode === "prod" && ai) {
    try {
      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: `Generate a wedding look visual ${forWhom}, based on these preferences: ${preferences}. Focus on bridal fashion, elegant, high quality. If multiple items are described, try to incorporate them into a cohesive look or a small set of related images.`,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })
      return {
        generatedImages: (sdkResponse.generatedImages || []).map(
          (sdkImg: any) => ({
            image: sdkImg.image
              ? { imageBytes: sdkImg.image.imageBytes }
              : undefined,
          })
        ),
      }
    } catch (error) {
      console.error("Error generating look ideas (prod):", error)
      return { generatedImages: [] }
    }
  }

  await mockDelay(2500 + Math.random() * 1000)
  const placeholderBase64 =
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
  return {
    generatedImages: [{ image: { imageBytes: placeholderBase64 } }],
  }
}

export const ellaSuggestVisionBoardImage = async (
  weddingVibe: string,
  appMode: AppMode
): Promise<VisionBoardItem | null> => {
  const ai = getGeminiAI()

  if (shouldUseMockData(appMode)) {
    await mockDelay(1500 + Math.random() * 1000)
    const inspirationIcon = `data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="inspirationBg" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#FEF7ED;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#F0F9FF;stop-opacity:1" />
          </linearGradient>
          <linearGradient id="flowerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#FB923C;stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:0.8" />
          </linearGradient>
        </defs>
        <rect width="400" height="300" fill="url(#inspirationBg)"/>
        
        <!-- Decorative border -->
        <rect x="10" y="10" width="380" height="280" fill="none" stroke="#FB923C" stroke-width="1.5" opacity="0.4" rx="8"/>
        
        <!-- Central floral motif -->
        <g transform="translate(200, 150)">
          <!-- Large central flower -->
          <circle cx="0" cy="0" r="20" fill="url(#flowerGradient)" opacity="0.7"/>
          <circle cx="-12" cy="-12" r="12" fill="#FED7AA" opacity="0.8"/>
          <circle cx="12" cy="-12" r="12" fill="#FED7AA" opacity="0.8"/>
          <circle cx="-12" cy="12" r="12" fill="#FED7AA" opacity="0.8"/>
          <circle cx="12" cy="12" r="12" fill="#FED7AA" opacity="0.8"/>
          <circle cx="0" cy="0" r="8" fill="#FBBF24"/>
          
          <!-- Smaller surrounding flowers -->
          <g transform="translate(-50, -30)">
            <circle cx="0" cy="0" r="8" fill="#DBEAFE" opacity="0.7"/>
            <circle cx="-5" cy="-5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="5" cy="-5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="-5" cy="5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="5" cy="5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="0" cy="0" r="3" fill="#FBBF24"/>
          </g>
          
          <g transform="translate(50, -30)">
            <circle cx="0" cy="0" r="8" fill="#DBEAFE" opacity="0.7"/>
            <circle cx="-5" cy="-5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="5" cy="-5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="-5" cy="5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="5" cy="5" r="5" fill="#93C5FD" opacity="0.8"/>
            <circle cx="0" cy="0" r="3" fill="#FBBF24"/>
          </g>
          
          <g transform="translate(-30, 40)">
            <circle cx="0" cy="0" r="6" fill="#FED7AA" opacity="0.7"/>
            <circle cx="-4" cy="-4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="4" cy="-4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="-4" cy="4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="4" cy="4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="0" cy="0" r="2" fill="#FBBF24"/>
          </g>
          
          <g transform="translate(30, 40)">
            <circle cx="0" cy="0" r="6" fill="#FED7AA" opacity="0.7"/>
            <circle cx="-4" cy="-4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="4" cy="-4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="-4" cy="4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="4" cy="4" r="4" fill="#FB923C" opacity="0.8"/>
            <circle cx="0" cy="0" r="2" fill="#FBBF24"/>
          </g>
        </g>
        
        <text x="200" y="240" text-anchor="middle" fill="#9A3412" font-family="serif" font-size="20" font-weight="600">${weddingVibe}</text>
        <text x="200" y="260" text-anchor="middle" fill="#1E40AF" font-family="serif" font-size="16">Wedding Inspiration</text>
      </svg>
    `)}`

    return {
      id: generateClientSideId(),
      imageUrl: inspirationIcon,
      caption: `Beautiful ${weddingVibe} wedding inspiration.`,
      tags: [weddingVibe.toLowerCase(), "inspiration", "wedding"],
    }
  }

  try {
    const prompt = `Generate a beautiful, inspiring wedding image that captures a ${weddingVibe} aesthetic. Focus on decor, ambiance, and overall wedding styling that embodies this vibe.`
    const sdkResponse = await ai!.models.generateImages({
      model: IMAGE_MODEL,
      prompt: prompt,
      config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
    })

    if (sdkResponse.generatedImages && sdkResponse.generatedImages.length > 0) {
      const firstImage = sdkResponse.generatedImages[0]
      if (firstImage.image && firstImage.image.imageBytes) {
        return {
          id: generateClientSideId(),
          imageUrl: `data:image/jpeg;base64,${firstImage.image.imageBytes}`,
          caption: `Beautiful ${weddingVibe} wedding inspiration.`,
          tags: [weddingVibe.toLowerCase(), "inspiration", "wedding"],
        }
      }
    }

    return null
  } catch (error) {
    console.error("Error generating vision board image:", error)
    return null
  }
}
