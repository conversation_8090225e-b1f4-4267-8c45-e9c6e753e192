import { IMAGE_MODEL, mockDelay } from "@/src/constants/aiConstants"
import { getGeminiAI, shouldUseMockData } from "@/src/services/geminiService"
import {
  AppMode,
  ChatMessage,
  GenerateImagesResponse as UIGenerateImagesResponse,
  VisionBoardItem,
  WeddingDetails,
} from "@/src/types/index"
import { generateClientSideId } from "@/src/utils/idUtils"

export const generateVisionSnapshot = async (
  details: WeddingDetails,
  appMode: AppMode = "dev"
): Promise<ChatMessage> => {
  const coupleNames = details.coupleNames || "the couple"
  const vibe = details.vibe || "beautiful"
  const ai = getGeminiAI()

  if (appMode === "prod" && ai) {
    try {
      const prompt = `Generate a beautiful, inspiring wedding image that captures a ${vibe} aesthetic. Focus on wedding decor, ambiance, ceremony setup, or reception styling that embodies this vibe. Create something that would inspire ${coupleNames} for their ${vibe} wedding.`

      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: prompt,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })

      if (
        sdkResponse.generatedImages &&
        sdkResponse.generatedImages.length > 0
      ) {
        const firstImage = sdkResponse.generatedImages[0]
        if (firstImage.image && firstImage.image.imageBytes) {
          return {
            id: `vision-${Date.now()}`,
            text: `Here's a sneak peek of the vision for ${coupleNames}! A little something to get the inspiration flowing for their ${vibe} wedding. ✨`,
            sender: "ella",
            timestamp: new Date(),
            imageUrl: `data:image/jpeg;base64,${firstImage.image.imageBytes}`,
          }
        }
      }
    } catch (error) {
      console.error("Error generating vision snapshot with AI:", error)
    }
  }

  // Fallback to a simple wedding-themed SVG
  await mockDelay(2000)
  const weddingIcon = `data:image/svg+xml;base64,${btoa(`
    <svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="600" height="400" fill="#F8FAFC"/>
      <circle cx="300" cy="200" r="80" fill="#FEF3C7" stroke="#F59E0B" stroke-width="2"/>
      <path d="M260 180 L280 200 L340 140" stroke="#F59E0B" stroke-width="3" fill="none"/>
      <text x="300" y="320" text-anchor="middle" fill="#374151" font-family="Arial" font-size="24">${vibe} Wedding Vision</text>
      <text x="300" y="350" text-anchor="middle" fill="#6B7280" font-family="Arial" font-size="16">AI-Generated Inspiration Coming Soon</text>
    </svg>
  `)}`

  return {
    id: `vision-mock-${Date.now()}`,
    text: `Here's a sneak peek of the vision for ${coupleNames}! A little something to get the inspiration flowing for their ${vibe} wedding. ✨`,
    sender: "ella",
    timestamp: new Date(),
    imageUrl: weddingIcon,
  }
}

export const generateLookIdeas = async (
  preferences: string,
  appMode: AppMode = "dev",
  weddingDetails?: WeddingDetails | null
): Promise<UIGenerateImagesResponse> => {
  const ai = getGeminiAI()
  const forWhom = weddingDetails?.coupleNames
    ? `for ${weddingDetails.coupleNames}'s wedding`
    : "for a wedding look"

  if (appMode === "prod" && ai) {
    try {
      const sdkResponse = await ai.models.generateImages({
        model: IMAGE_MODEL,
        prompt: `Generate a wedding look visual ${forWhom}, based on these preferences: ${preferences}. Focus on bridal fashion, elegant, high quality. If multiple items are described, try to incorporate them into a cohesive look or a small set of related images.`,
        config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
      })
      return {
        generatedImages: (sdkResponse.generatedImages || []).map(
          (sdkImg: any) => ({
            image: sdkImg.image
              ? { imageBytes: sdkImg.image.imageBytes }
              : undefined,
          })
        ),
      }
    } catch (error) {
      console.error("Error generating look ideas (prod):", error)
      return { generatedImages: [] }
    }
  }

  await mockDelay(2500 + Math.random() * 1000)
  const placeholderBase64 =
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
  return {
    generatedImages: [{ image: { imageBytes: placeholderBase64 } }],
  }
}

export const ellaSuggestVisionBoardImage = async (
  weddingVibe: string,
  appMode: AppMode
): Promise<VisionBoardItem | null> => {
  const ai = getGeminiAI()

  if (shouldUseMockData(appMode)) {
    await mockDelay(1500 + Math.random() * 1000)
    const inspirationIcon = `data:image/svg+xml;base64,${btoa(`
      <svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect width="400" height="300" fill="#FEF7ED"/>
        <circle cx="200" cy="150" r="60" fill="#FED7AA" stroke="#FB923C" stroke-width="2"/>
        <path d="M170 130 L190 150 L230 110" stroke="#FB923C" stroke-width="2" fill="none"/>
        <text x="200" y="240" text-anchor="middle" fill="#9A3412" font-family="Arial" font-size="18">${weddingVibe}</text>
        <text x="200" y="260" text-anchor="middle" fill="#C2410C" font-family="Arial" font-size="14">Wedding Inspiration</text>
      </svg>
    `)}`

    return {
      id: generateClientSideId(),
      imageUrl: inspirationIcon,
      caption: `Beautiful ${weddingVibe} wedding inspiration.`,
      tags: [weddingVibe.toLowerCase(), "inspiration", "wedding"],
    }
  }

  try {
    const prompt = `Generate a beautiful, inspiring wedding image that captures a ${weddingVibe} aesthetic. Focus on decor, ambiance, and overall wedding styling that embodies this vibe.`
    const sdkResponse = await ai!.models.generateImages({
      model: IMAGE_MODEL,
      prompt: prompt,
      config: { numberOfImages: 1, outputMimeType: "image/jpeg" },
    })

    if (sdkResponse.generatedImages && sdkResponse.generatedImages.length > 0) {
      const firstImage = sdkResponse.generatedImages[0]
      if (firstImage.image && firstImage.image.imageBytes) {
        return {
          id: generateClientSideId(),
          imageUrl: `data:image/jpeg;base64,${firstImage.image.imageBytes}`,
          caption: `Beautiful ${weddingVibe} wedding inspiration.`,
          tags: [weddingVibe.toLowerCase(), "inspiration", "wedding"],
        }
      }
    }

    return null
  } catch (error) {
    console.error("Error generating vision board image:", error)
    return null
  }
}
