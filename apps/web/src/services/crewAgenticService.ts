import { crew } from "@/src/agents/crewWorkflow"

interface AgenticActionPayload {
  action: string
  domain: string
  payload: any
}

export class CrewAgenticService {
  static async executeAgenticAction({
    action,
    domain,
    payload,
  }: AgenticActionPayload) {
    try {
      // Find the appropriate manager for the domain
      const manager = crew.managers.find((m: any) => m.getDomain === domain)

      if (!manager) {
        throw new Error(`Manager not found for domain: ${domain}`)
      }

      // Execute the task through the manager
      const task = { type: action, ...payload }
      const result = await manager.assignTask(task)

      return {
        success: true,
        result,
        message: `Successfully executed ${action} in ${domain} domain`,
      }
    } catch (error: any) {
      console.error("Error in CrewAgenticService:", error)
      throw new Error(error.message || "Agentic action failed")
    }
  }

  static async getCrewStatus() {
    try {
      const orchestratorStatus = crew.orchestrator
        .getManagers()
        .map((manager) => ({
          domain: manager.getDomain,
          status: manager.reportStatus(),
        }))

      return {
        success: true,
        crew: {
          orchestrator: "Ella - Active",
          managers: orchestratorStatus,
        },
      }
    } catch (error: any) {
      console.error("Error getting crew status:", error)
      throw new Error("Failed to get crew status")
    }
  }

  static async getDomainCapabilities(domain: string) {
    try {
      const manager = crew.managers.find((m: any) => m.getDomain === domain)

      if (!manager) {
        throw new Error(`Manager not found for domain: ${domain}`)
      }

      const status = manager.reportStatus()

      return {
        success: true,
        domain,
        capabilities: status.associates,
        status: "active",
      }
    } catch (error: any) {
      console.error("Error getting domain capabilities:", error)
      throw new Error(`Failed to get capabilities for domain: ${domain}`)
    }
  }
}
