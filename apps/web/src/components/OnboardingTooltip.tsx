// src/components/OnboardingTooltip.tsx
import * as Tooltip from '@radix-ui/react-tooltip';

export default function OnboardingTooltip({ children, content }: { children: React.ReactNode, content: string }) {
  return (
    <Tooltip.Provider>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>{children}</Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content className="bg-primary text-white px-3 py-2 rounded shadow-lg text-sm max-w-xs z-50" sideOffset={8}>
            {content}
            <Tooltip.Arrow className="fill-primary" />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    </Tooltip.Provider>
  );
}
