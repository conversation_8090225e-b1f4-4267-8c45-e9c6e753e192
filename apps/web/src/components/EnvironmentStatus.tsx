import { getEnvironment, validateEnvironment } from '@/src/constants/environment';
import { getApiKeySource } from '@/src/services/geminiService';
import Card from './common/Card';
import { CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from './icons/HeroIcons';

const EnvironmentStatus: React.FC = () => {
    const validation = validateEnvironment();
    const env = getEnvironment();
    const apiKeySource = getApiKeySource();

    return (
        <Card title="Environment Status" className="mb-4">
            <div className="space-y-3">
                <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${env === 'production' ? 'bg-green-500' : 'bg-blue-500'}`}></div>
                    <span className="text-sm font-medium">
                        Running in <span className="capitalize">{env}</span> mode
                    </span>
                </div>

                {validation.isValid ? (
                    <div className="flex items-center space-x-2 text-green-600">
                        <CheckCircleIcon className="w-4 h-4" />
                        <span className="text-sm">Environment properly configured</span>
                    </div>
                ) : (
                    <div className="space-y-2">
                        <div className="flex items-center space-x-2 text-amber-600">
                            <ExclamationTriangleIcon className="w-4 h-4" />
                            <span className="text-sm">Configuration issues detected</span>
                        </div>
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                            <p className="text-sm text-amber-800 font-medium mb-1">Missing environment variables:</p>
                            <ul className="text-xs text-amber-700 space-y-1">
                                {validation.missingVars.map(varName => (
                                    <li key={varName} className="font-mono">• {varName}</li>
                                ))}
                            </ul>
                        </div>
                    </div>
                )}

                <div className="border-t pt-3">
                    <div className="flex items-start space-x-2 text-blue-600">
                        <InformationCircleIcon className="w-4 h-4 mt-0.5" />
                        <div className="text-xs">
                            <p className="font-medium">Configuration:</p>
                            <ul className="mt-1 space-y-1 text-muted-foreground">
                                <li>• App Mode: {validation.config.defaultAppMode}</li>
                                <li>• Supabase Project: {validation.config.supabaseProject}</li>
                                <li>• Debug Logs: {validation.config.enableDebugLogs ? 'Enabled' : 'Disabled'}</li>
                                <li>• Mock Data: {validation.config.mockDataEnabled ? 'Enabled' : 'Disabled'}</li>
                                <li>• API Key Source: {
                                    apiKeySource === 'user' ? 'Personal Key (User Configured)' :
                                        apiKeySource === 'environment' ? 'Environment Variables' :
                                            'None Configured'
                                }</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </Card>
    );
};

export default EnvironmentStatus; 