'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useWedding } from '@/src/contexts/WeddingContext';
import { useAgentSystem } from '@/src/hooks/useAgentSystem';
import { useToast } from '@/src/contexts/ToastContext';

interface ChatMessage {
  id: string;
  type: 'user' | 'agent';
  content: string;
  timestamp: Date;
  agent?: string;
  runId?: string;
}

interface AgentChatProps {
  className?: string;
  onAgentAction?: (action: string, data: any) => void;
}

export function AgentChat({ className = '', onAgentAction }: AgentChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [showFeedback, setShowFeedback] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  
  const { wedding, user } = useWedding();
  const { sendMessage, submitFeedback, isLoading, currentRunId, cancelRequest } = useAgentSystem();
  const { addToast } = useToast();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message
    if (messages.length === 0) {
      setMessages([{
        id: 'welcome',
        type: 'agent',
        content: "Hi! I'm Ella, your AI wedding planning assistant. I can help you with budget planning, vendor coordination, guest management, timeline creation, and much more. What would you like to work on today?",
        timestamp: new Date(),
        agent: 'orchestrator'
      }]);
    }
  }, [messages.length]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;
    if (!wedding?.id || !user?.id) {
      addToast('Please ensure you are logged in and have a wedding selected.', 'error');
      return;
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    try {
      const response = await sendMessage(
        inputMessage.trim(),
        wedding.id,
        user.id,
        (agentResponse) => {
          const agentMessage: ChatMessage = {
            id: `agent-${Date.now()}`,
            type: 'agent',
            content: agentResponse.content,
            timestamp: new Date(),
            agent: agentResponse.agent,
            runId: agentResponse.runId
          };
          setMessages(prev => [...prev, agentMessage]);
          
          // Check if the agent performed any actions that need to update the UI
          if (onAgentAction) {
            // Parse agent response for actions
            if (agentResponse.content.includes('budget') || agentResponse.content.includes('Budget')) {
              onAgentAction('budget_update', { content: agentResponse.content });
            }
            if (agentResponse.content.includes('vendor') || agentResponse.content.includes('Vendor')) {
              onAgentAction('vendor_update', { content: agentResponse.content });
            }
            if (agentResponse.content.includes('guest') || agentResponse.content.includes('Guest')) {
              onAgentAction('guest_update', { content: agentResponse.content });
            }
            if (agentResponse.content.includes('timeline') || agentResponse.content.includes('Timeline')) {
              onAgentAction('timeline_update', { content: agentResponse.content });
            }
          }
        },
        (error) => {
          const errorMessage: ChatMessage = {
            id: `error-${Date.now()}`,
            type: 'agent',
            content: `I apologize, but I encountered an error: ${error}. Please try again.`,
            timestamp: new Date(),
            agent: 'error'
          };
          setMessages(prev => [...prev, errorMessage]);
        }
      );
    } catch (error) {
      console.error('Error in chat:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFeedback = async (messageId: string, feedbackType: 'thumbs', thumbsDirection: 'up' | 'down') => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.runId || !wedding?.id || !user?.id) return;

    const success = await submitFeedback({
      runId: message.runId,
      userId: user.id,
      weddingId: wedding.id,
      feedbackType,
      thumbsDirection
    });

    if (success) {
      setShowFeedback(messageId);
      setTimeout(() => setShowFeedback(null), 2000);
    }
  };

  const handleRatingFeedback = async (messageId: string, rating: number) => {
    const message = messages.find(m => m.id === messageId);
    if (!message || !message.runId || !wedding?.id || !user?.id) return;

    await submitFeedback({
      runId: message.runId,
      userId: user.id,
      weddingId: wedding.id,
      feedbackType: 'rating',
      rating
    });
  };

  return (
    <div className={`flex flex-col h-full bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
            <span className="text-purple-600 font-semibold text-sm">E</span>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Ella</h3>
            <p className="text-xs text-gray-500">AI Wedding Assistant</p>
          </div>
        </div>
        {isLoading && (
          <button
            onClick={cancelRequest}
            className="px-3 py-1 text-xs bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors"
          >
            Cancel
          </button>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.type === 'user'
                  ? 'bg-purple-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              <p className="text-sm">{message.content}</p>
              <div className="flex items-center justify-between mt-2">
                <p className="text-xs opacity-70">
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  {message.agent && message.agent !== 'orchestrator' && (
                    <span className="ml-1">• {message.agent}</span>
                  )}
                </p>
                
                {/* Feedback buttons for agent messages */}
                {message.type === 'agent' && message.runId && (
                  <div className="flex items-center space-x-1 ml-2">
                    {showFeedback === message.id ? (
                      <span className="text-xs text-green-600">Thanks!</span>
                    ) : (
                      <>
                        <button
                          onClick={() => handleFeedback(message.id, 'thumbs', 'up')}
                          className="p-1 hover:bg-gray-200 rounded transition-colors"
                          title="Helpful"
                        >
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleFeedback(message.id, 'thumbs', 'down')}
                          className="p-1 hover:bg-gray-200 rounded transition-colors"
                          title="Not helpful"
                        >
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M18 9.5a1.5 1.5 0 11-3 0v-6a1.5 1.5 0 013 0v6zM14 9.667v-5.43a2 2 0 00-1.106-1.79l-.05-.025A4 4 0 0011.057 2H5.641a2 2 0 00-1.962 1.608l-1.2 6A2 2 0 004.44 12H8v4a2 2 0 002 2 1 1 0 001-1v-.667a4 4 0 01.8-2.4l1.4-1.866a4 4 0 00.8-2.4z" />
                          </svg>
                        </button>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-100 rounded-lg px-4 py-2">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask Ella about your wedding planning..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
          </button>
        </div>
        
        {/* Quick actions */}
        <div className="flex flex-wrap gap-2 mt-2">
          <button
            onClick={() => setInputMessage('What\'s my current budget status?')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
            disabled={isLoading}
          >
            Budget Status
          </button>
          <button
            onClick={() => setInputMessage('Help me find vendors in my area')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
            disabled={isLoading}
          >
            Find Vendors
          </button>
          <button
            onClick={() => setInputMessage('Create a wedding timeline')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
            disabled={isLoading}
          >
            Create Timeline
          </button>
          <button
            onClick={() => setInputMessage('Help me manage my guest list')}
            className="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 transition-colors"
            disabled={isLoading}
          >
            Guest Management
          </button>
        </div>
      </div>
    </div>
  );
}
