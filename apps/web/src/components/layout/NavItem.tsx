"use client";

import { NavItemType } from "@/src/types/index";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

interface NavItemProps {
  item: NavItemType;
  isWeddingContext: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ item, isWeddingContext }) => {
  const baseClasses = "flex items-center space-x-3 px-3 py-2.5 rounded-md transition-colors duration-200 ease-in-out group";
  const textClasses = "text-sm font-medium";
  const pathname = usePathname(); // Next.js hook to get the current path

  let href = item.path;
  if (isWeddingContext) {
    const weddingIdMatch = pathname.match(/\/wedding\/([^\/]+)/);
    const weddingId = weddingIdMatch?.[1];
    if (weddingId) {
      // Construct the full path for wedding-specific links
      href = `/wedding/${weddingId}/${item.path}`;
    }
  }

  // Simplified active check for Next.js
  const isActive = pathname === href || (item.path === 'overview' && pathname === `/wedding/${href.split('/')[2]}`);

  return (
    <li className="mb-1">
      <Link
        href={href}
        className={`${baseClasses} ${textClasses} ${isActive
          ? "bg-sidebar-primary text-sidebar-primary-foreground shadow-sm"
          : "text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent"
          }`}
      >
        <item.icon className="w-5 h-5 flex-shrink-0 text-inherit" />
        <span>{item.label}</span>
      </Link>
    </li>
  );
};

export default NavItem;