import Sidebar from '@/src/components/layout/Sidebar';
import { AgentChat } from '@/src/components/AgentChat';
import { NavItemType } from '@/src/types/index';
import { useState } from 'react';

interface AppLayoutProps {
    navItems: NavItemType[];
    isWeddingSelected: boolean;
    children: React.ReactNode;
    className?: string;
}

const AppLayout: React.FC<AppLayoutProps> = ({
    navItems,
    isWeddingSelected,
    children,
    className = "p-6 sm:p-8 md:p-10"
}) => {
    const [isChatOpen, setIsChatOpen] = useState(false);

    const handleAgentAction = (action: string, data: any) => {
        console.log('Agent action:', action, data);
        // Handle agent actions that might need to update the UI
        // This could trigger state updates, navigation, or other UI changes
    };

    return (
        <div className="flex h-screen bg-background text-foreground">
            <Sidebar navItems={navItems} isWeddingSelected={isWeddingSelected} />
            <main className={`flex-1 overflow-y-auto custom-scrollbar ${className}`}>
                {children}
            </main>

            {/* Agent Chat Toggle Button */}
            <button
                onClick={() => setIsChatOpen(!isChatOpen)}
                className={`fixed bottom-6 right-6 z-40 w-14 h-14 bg-purple-600 hover:bg-purple-700 text-white rounded-full shadow-lg transition-all duration-300 flex items-center justify-center ${
                    isChatOpen ? 'rotate-45' : ''
                }`}
                title={isChatOpen ? 'Close Ella' : 'Chat with Ella'}
            >
                {isChatOpen ? (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                ) : (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                    </svg>
                )}
            </button>

            {/* Agent Chat Panel */}
            <div
                className={`fixed bottom-6 right-24 z-30 w-96 h-[600px] transition-all duration-300 transform ${
                    isChatOpen ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0 pointer-events-none'
                }`}
            >
                <AgentChat
                    className="h-full"
                    onAgentAction={handleAgentAction}
                />
            </div>

            {/* Backdrop */}
            {isChatOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-20 z-20 lg:hidden"
                    onClick={() => setIsChatOpen(false)}
                />
            )}
        </div>
    );
};

export default AppLayout; 