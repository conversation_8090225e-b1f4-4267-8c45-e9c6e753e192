import SayYesLogo from '@/src/components/icons/SayYesLogo';
import NavItem from '@/src/components/layout/NavItem';
import { NavItemType } from '@/src/types/index';
import Link from 'next/link';

interface SidebarProps {
  navItems: NavItemType[];
  isWeddingSelected: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ navItems, isWeddingSelected }) => {
  return (
    <aside className="w-64 bg-sidebar text-sidebar-foreground p-4 sm:p-5 flex flex-col shadow-lg border-r border-sidebar-border">
      <div className="mb-8 flex items-center space-x-3 px-2">
        <SayYesLogo className="w-10 h-10" />
        <span className="text-xl font-semibold">SayYes Planner</span> {/* Removed explicit text color */}
      </div>
      <nav className="flex-grow">
        <ul>
          {isWeddingSelected && (
            <li className="mb-2.5">
              <Link
                href="/dashboard"
                className="flex items-center space-x-3 px-3 py-2.5 rounded-md transition-all duration-200 ease-in-out group text-sidebar-foreground/80 hover:text-sidebar-foreground hover:bg-sidebar-accent"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 15L3 9m0 0l6-6M3 9h12a6 6 0 010 12h-3" />
                </svg>
                <span className="text-sm">Back to Dashboard</span>
              </Link>
            </li>
          )}
          {navItems.map((item) => (
            <NavItem
              key={item.id}
              item={item}
              isWeddingContext={isWeddingSelected}
            />
          ))}
        </ul>
      </nav>
      <div className="mt-auto pt-4 border-t border-sidebar-border text-center text-xs text-muted-foreground/60">
        <p>&copy; {new Date().getFullYear()} SayYes AI</p>
        <p>Intelligent Planning Partner</p>
      </div>
    </aside>
  );
};

export default Sidebar;