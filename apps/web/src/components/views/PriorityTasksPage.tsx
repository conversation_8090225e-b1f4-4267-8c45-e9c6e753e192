import Card from '@/src/components/common/Card';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import { CheckBadgeIcon, CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { Task } from '@/src/types/index';
import { parseAndFormatDateStringForDisplay, parseDateStringToLocalMidnight } from '@/src/utils/dateUtils';
import React, { useContext } from 'react';

const TasksPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return <div className="flex items-center justify-center h-screen"><LoadingSpinner text="Loading tasks..." /></div>;
  }

  const tasks = plannerCtx.currentWedding.tasks || [];

  const toggleTaskCompletion = (taskId: string) => {
    if (!plannerCtx || !plannerCtx.currentWeddingId) return;

    const updatedTasks = tasks.map(taskItem =>
      taskItem.id === taskId ? { ...taskItem, isCompleted: !taskItem.isCompleted } : taskItem
    );

    plannerCtx.updateDataForCurrentWedding('tasks', updatedTasks);

    const task = updatedTasks.find(t => t.id === taskId);
    if (task) {
      addToast(`${task.isCompleted ? 'Marked as complete:' : 'Marked as incomplete:'} "${task.title}"`, "success");
    }
  };

  const todayForComparison = parseDateStringToLocalMidnight(new Date().toISOString().split('T')[0]);

  const upcomingTasks = tasks.filter(task => {
    const taskDueDateObj = parseDateStringToLocalMidnight(task.dueDate);
    return !task.isCompleted && taskDueDateObj && todayForComparison && taskDueDateObj >= todayForComparison;
  }).sort((a, b) => {
    const dateA = parseDateStringToLocalMidnight(a.dueDate);
    const dateB = parseDateStringToLocalMidnight(b.dueDate);
    return dateA!.getTime() - dateB!.getTime();
  });

  const overdueTasks = tasks.filter(task => {
    const taskDueDateObj = parseDateStringToLocalMidnight(task.dueDate);
    return !task.isCompleted && taskDueDateObj && todayForComparison && taskDueDateObj < todayForComparison;
  }).sort((a, b) => {
    const dateA = parseDateStringToLocalMidnight(a.dueDate);
    const dateB = parseDateStringToLocalMidnight(b.dueDate);
    return dateA!.getTime() - dateB!.getTime();
  });

  const completedTasks = tasks.filter(task => task.isCompleted).sort((a, b) => {
    const dateA = parseDateStringToLocalMidnight(a.dueDate);
    const dateB = parseDateStringToLocalMidnight(b.dueDate);
    if (dateA && dateB) return dateB.getTime() - dateA.getTime();
    if (dateB) return 1; // Sort completed tasks with dates after those without
    if (dateA) return -1;
    return 0;
  });

  const renderTaskList = (taskList: Task[], listTitle: string, listIcon?: React.ReactNode, isOverdueList?: boolean) => (
    <Card title={listTitle} className={`min-h-[200px] ${isOverdueList ? "!border-destructive/50 shadow-destructive/20" : ""}`} titleClassName="flex items-center">
      {listIcon && React.isValidElement(listIcon) && <span className={`mr-2 ${isOverdueList ? "text-destructive" : "text-primary"}`}>{React.cloneElement(listIcon as React.ReactElement<{ className?: string }>, { className: "w-5 h-5" })}</span>}
      {taskList.length > 0 ? (
        <ul className="space-y-3">
          {taskList.map(taskItem => {
            const taskDueDateObj = parseDateStringToLocalMidnight(taskItem.dueDate);
            // isOverdue within this function's scope for clarity, distinct from isOverdueList prop
            const isCurrentlyOverdue = !taskItem.isCompleted && taskDueDateObj && todayForComparison && taskDueDateObj < todayForComparison;
            return (
              <li key={taskItem.id} className={`p-3 rounded-md transition-all duration-200 ${taskItem.isCompleted ? 'bg-positive/10' : 'bg-secondary hover:bg-accent'}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <button onClick={() => toggleTaskCompletion(taskItem.id)} className="mr-3 focus:outline-none">
                      {taskItem.isCompleted ? <CheckCircleIcon className="w-6 h-6 text-positive" /> : <div className="w-6 h-6 border-2 border-muted-foreground/50 rounded-full hover:border-primary"></div>}
                    </button>
                    <div>
                      <span className={`text-sm font-medium ${taskItem.isCompleted ? 'line-through text-muted-foreground' : 'text-foreground'}`}>{taskItem.title}</span>
                      {taskItem.description && <p className="text-xs text-muted-foreground mt-0.5">{taskItem.description}</p>}
                    </div>
                  </div>
                  {taskItem.dueDate && <span className={`text-xs ${taskItem.isCompleted ? 'text-muted-foreground/70' : isCurrentlyOverdue ? 'text-destructive font-semibold' : 'text-primary'}`}>Due: {parseAndFormatDateStringForDisplay(taskItem.dueDate)}</span>}
                </div>
              </li>
            );
          })}
        </ul>
      ) : (
        <p className="text-sm text-muted-foreground text-center py-4">No tasks in this category.</p>
      )}
    </Card>
  );

  const allTasksCompleted = tasks.length > 0 && tasks.every(t => t.isCompleted);

  return (
    <div className="space-y-6">
      <PageTitle title={`${plannerCtx.currentWedding.weddingDetails.coupleNames}'s Tasks`} subtitle="Focus on what needs attention most. Ella helps identify and track these critical items." icon={<CheckBadgeIcon />} />

      {overdueTasks.length > 0 && renderTaskList(overdueTasks, "Overdue Tasks", <ExclamationTriangleIcon />, true)}
      {upcomingTasks.length > 0 && renderTaskList(upcomingTasks, "Upcoming Tasks", <InformationCircleIcon />)}

      {allTasksCompleted && tasks.length > 0 && (
        <Card>
          <p className="text-muted-foreground text-center py-8">All tasks completed! Well done!</p>
        </Card>
      )}
      {completedTasks.length > 0 && !allTasksCompleted && renderTaskList(completedTasks, "Completed Tasks", <CheckCircleIcon />)}


      {(tasks.length === 0) && (
        <Card>
          <p className="text-muted-foreground text-center py-8">No tasks identified yet. Ella will highlight them here or you can add them in the Timeline section!</p>
        </Card>
      )}
    </div>
  );
};

export default TasksPage;