import AgentChatBubble from '@/src/components/AgentChatBubble';
import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import AgentChatInterface from '@/src/components/feature/agent/AgentChatInterface';
import WorkforceConfigPanel from '@/src/components/feature/workforce/WorkforceConfigPanel';
import { CogIcon, CpuChipIcon, PaperAirplaneIcon, SparklesIcon } from '@/src/components/icons/HeroIcons';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import { getAIAgentActivity } from '@/src/services/ai/index';
import { AIAgentLogEntry } from '@/src/types/index';
import React, { useCallback, useContext, useEffect, useState } from 'react';

const AIAgentSystemPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const [isLoadingLog, setIsLoadingLog] = useState(false);
  const [showWorkforceConfig, setShowWorkforceConfig] = useState(false);
  const [showWorkforcePanel, setShowWorkforcePanel] = useState(false);
  const [crewStatus, setCrewStatus] = useState<any>(null);

  const { runAgenticAction, getCrewStatus, getDomainCapabilities, loading, error, result } = useAgenticAction();

  // Agent logs are now part of the currentWedding object in PlannerContext
  const agentLogs = plannerCtx?.currentWedding?.agentLogs || [];

  const domains = [
    { id: 'vendor', label: 'Vendor Management', action: 'add-vendor' },
    { id: 'budget', label: 'Budget Planning', action: 'add-expense' },
    { id: 'timeline', label: 'Timeline Management', action: 'add-milestone' },
    { id: 'guest', label: 'Guest Management', action: 'add-guest' },
    { id: 'vision', label: 'Vision Board', action: 'add-image' },
    { id: 'styling', label: 'Styling Assistant', action: 'add-outfit' }
  ];

  const fetchNewLogEntry = useCallback(async () => {
    if (!plannerCtx || !plannerCtx.currentWedding || isLoadingLog) return;

    setIsLoadingLog(true);
    const activityHint = "Monitoring and assisting with the current client's wedding planning.";

    try {
      const newEntry: AIAgentLogEntry = await getAIAgentActivity(plannerCtx.currentWedding, activityHint, plannerCtx.appMode || 'dev');
      // Add log entry to the current wedding's log
      plannerCtx.addAIAgentLogToCurrentWedding(newEntry);
    } catch (error) {
      console.error("Failed to fetch or process AI agent log entry:", error);
      // Optionally add a system error log to the UI if desired
      const errorEntry: AIAgentLogEntry = {
        id: `log-error-${Date.now()}`,
        timestamp: new Date(),
        agent: 'System',
        action: 'Failed to retrieve AI agent activity.',
        details: (error as Error).message || 'Unknown error'
      };
      plannerCtx.addAIAgentLogToCurrentWedding(errorEntry);
    } finally {
      setIsLoadingLog(false);
    }
  }, [plannerCtx, isLoadingLog]);

  const handleGetCrewStatus = async () => {
    const status = await getCrewStatus();
    setCrewStatus(status);
  };

  const handleTestDomain = async (domain: any) => {
    if (!plannerCtx?.currentWedding) return;

    const testPayload = {
      clientId: plannerCtx.currentWedding.id,
      coupleNames: plannerCtx.currentWedding.weddingDetails.coupleNames,
      domain: domain.id
    };

    await runAgenticAction(domain.action, domain.id, testPayload);
  };

  useEffect(() => {
    if (plannerCtx?.currentWedding) {
      // Fetch initial log if logs are empty for this wedding
      if (agentLogs.length === 0) fetchNewLogEntry();

      const intervalId = setInterval(() => {
        fetchNewLogEntry();
      }, 10000 + Math.random() * 7000);

      return () => clearInterval(intervalId);
    }
  }, [plannerCtx?.currentWedding, plannerCtx?.appMode, agentLogs.length, fetchNewLogEntry]);

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return <div className="flex items-center justify-center h-screen"><LoadingSpinner text="Loading AI Agent System..." /></div>;
  }

  const { currentWedding } = plannerCtx;
  const clientCoupleNames = currentWedding.weddingDetails.coupleNames;

  return (
    <div className="space-y-6">
      <PageTitle
        title={`AI Workforce for ${clientCoupleNames}`}
        subtitle="Monitor and configure Ella's team working on this specific wedding."
        icon={<CpuChipIcon />}
      />

      {/* Ella Status Card */}
      <Card title={`Ella - AI Orchestrator Status for ${clientCoupleNames}`}>
        <div className="space-y-2">
          <p className="text-muted-foreground"><strong className="text-primary">Current Focus:</strong> {currentWedding.weddingDetails?.vibe ? `Refining plans for their ${currentWedding.weddingDetails.vibe} wedding.` : 'Awaiting initial wedding details.'}</p>
          <p className="text-muted-foreground"><strong className="text-primary">Autonomy Level:</strong> Balanced (Planner review for critical decisions).</p>
          <p className="text-muted-foreground"><strong className="text-primary">System Mode:</strong> <span className={plannerCtx.appMode === 'prod' ? 'text-positive font-semibold' : 'text-warning-foreground font-semibold'}>{plannerCtx.appMode?.toUpperCase()}</span></p>
        </div>

        <div className="flex gap-4 mt-4">
          <Button
            onClick={handleGetCrewStatus}
            variant="secondary"
            size="sm"
            leftIcon={<SparklesIcon className="w-4 h-4" />}
          >
            Get Crew Status
          </Button>
          <Button
            onClick={() => setShowWorkforcePanel(true)}
            variant="primary"
            size="sm"
            leftIcon={<CogIcon className="w-4 h-4" />}
          >
            Configure Workforce
          </Button>
          <Button
            onClick={() => setShowWorkforceConfig(!showWorkforceConfig)}
            variant="outline"
            size="sm"
            leftIcon={<PaperAirplaneIcon className="w-4 h-4" />}
          >
            Quick Domain Tests
          </Button>
        </div>

        {crewStatus && (
          <div className="mt-4 p-3 bg-secondary/50 rounded-lg">
            <h4 className="font-medium text-foreground mb-2">Current Crew Status</h4>
            <div className="text-sm text-muted-foreground space-y-1">
              <p><strong>Orchestrator:</strong> {crewStatus.crew?.orchestrator}</p>
              <p><strong>Active Managers:</strong> {crewStatus.crew?.managers?.length || 0}</p>
              {crewStatus.crew?.managers?.map((manager: any, idx: number) => (
                <p key={idx} className="ml-4">• {manager.domain}: {manager.status?.status || 'active'}</p>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Workforce Configuration Preview */}
      {showWorkforceConfig && (
        <Card title="Quick Domain Testing" className="border-primary/20">
          <p className="text-muted-foreground mb-4">Test individual domains quickly. For full workforce configuration, use the &quot;Configure Workforce&quot; button above.</p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {domains.map(domain => (
              <div
                key={domain.id}
                className="p-4 border rounded-lg bg-secondary/30 hover:bg-secondary/50 transition-colors"
              >
                <h3 className="font-medium text-foreground">{domain.label}</h3>
                <p className="text-sm text-muted-foreground">Action: {domain.action}</p>
                <div className="mt-3 space-y-2">
                  <Button
                    onClick={() => handleTestDomain(domain)}
                    disabled={loading}
                    variant="ghost"
                    size="sm"
                    className="w-full"
                    leftIcon={<PaperAirplaneIcon className="w-4 h-4" />}
                  >
                    Test Domain
                  </Button>
                  <Button
                    onClick={() => getDomainCapabilities(domain.id)}
                    variant="outline"
                    size="sm"
                    className="w-full text-xs"
                  >
                    View Capabilities
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* CrewAI Execution Results */}
      {loading && (
        <AgentChatBubble
          message={`Working on ${clientCoupleNames}'s wedding planning request...`}
          name="Ella"
        />
      )}

      {error && (
        <Card title="Execution Error" className="border-destructive">
          <p className="text-destructive">{error}</p>
        </Card>
      )}

      {result && (
        <Card title="Action Completed" className="border-positive">
          <p className="text-positive mb-3">Ella completed the action for {clientCoupleNames}</p>
          <pre className="text-sm bg-secondary/50 p-4 rounded overflow-x-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </Card>
      )}

      {/* Real-time Agent Activity Log */}
      <Card title="Real-time Agent Activity Log">
        <div className="mb-4">
          <Button
            onClick={fetchNewLogEntry}
            isLoading={isLoadingLog}
            disabled={isLoadingLog || !plannerCtx?.currentWedding}
            leftIcon={<SparklesIcon className="w-4 h-4" />}
            variant="secondary"
            size="sm"
          >
            Fetch Latest Activity
          </Button>
        </div>
        {isLoadingLog && agentLogs.length === 0 && <p className="text-muted-foreground">Initializing activity log for {clientCoupleNames}...</p>}
        {agentLogs.length === 0 && !isLoadingLog && <p className="text-muted-foreground">No agent activity logged yet for this wedding. Ella might be waiting for your input!</p>}

        <div className="max-h-[400px] overflow-y-auto space-y-3 pr-2 custom-scrollbar">
          {agentLogs.map(log => (
            <div key={log.id} className="p-3 bg-secondary/80 rounded-lg shadow-sm border-l-4 border-primary">
              <div className="flex justify-between items-center mb-1">
                <span className="font-semibold text-primary text-sm">{log.agent}</span>
                <span className="text-xs text-muted-foreground/80">{log.timestamp.toLocaleTimeString()} - {log.timestamp.toLocaleDateString()}</span>
              </div>
              <p className="text-sm text-foreground">{log.action}</p>
              {log.details && <p className="text-xs text-muted-foreground mt-1 italic">{log.details}</p>}
            </div>
          ))}
          {isLoadingLog && agentLogs.length > 0 && <p className="text-muted-foreground text-sm italic mt-2">Ella's team is working on {clientCoupleNames}'s wedding...</p>}
        </div>
      </Card>

      {/* System Architecture */}
      <Card title="LangGraph Agent System Architecture">
        <div className="space-y-4">
          <p className="text-muted-foreground mb-4">SayYes utilizes a LangGraph-powered hierarchical agent system for each wedding:</p>

          <div className="pl-4 border-l-2 border-primary space-y-3">
            <div>
              <strong className="text-primary">Tier 1: Ella (Orchestrator)</strong>
              <p className="text-sm text-muted-foreground">Top-level AI coordinator managing all wedding planning activities for {clientCoupleNames}</p>
            </div>

            <div className="pl-4 border-l-2 border-secondary space-y-2">
              <div>
                <strong className="text-secondary">Tier 2: Manager Agents</strong>
                <p className="text-xs text-muted-foreground">Domain specialists handling specific areas for this wedding</p>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2 text-xs">
                  {domains.map(domain => (
                    <div key={domain.id} className="px-2 py-1 bg-secondary/20 rounded text-muted-foreground">
                      {domain.label}
                    </div>
                  ))}
                </div>
              </div>

              <div className="pl-4 border-l-2 border-accent space-y-1">
                <div>
                  <strong className="text-accent">Tier 3: Associate Agents</strong>
                  <p className="text-xs text-muted-foreground">Task executors performing specific operations and research for {clientCoupleNames}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 p-3 bg-primary/10 rounded-lg">
            <p className="text-xs text-muted-foreground">
              <strong>LangGraph Integration:</strong> This system uses LangGraph for orchestration, providing stateful agent workflows, memory persistence, and human-in-the-loop capabilities tailored specifically for each client's needs.
            </p>
          </div>
        </div>
      </Card>

      {/* Workforce Configuration Panel */}
      <WorkforceConfigPanel
        isVisible={showWorkforcePanel}
        onClose={() => setShowWorkforcePanel(false)}
      />
    </div>
  );
};

export default AIAgentSystemPage;
