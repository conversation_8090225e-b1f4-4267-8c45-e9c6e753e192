// src/pages/budget.tsx
import OnboardingTooltip from '@/src/components/OnboardingTooltip';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import React, { useState } from 'react';

import AgentChatBubble from '@/src/components/AgentChatBubble';

import Spinner from '@/src/components/Spinner';

export default function BudgetPage() {
  const [expense, setExpense] = useState({ category: '', amount: '' });
  const { runAgenticAction, loading, error, result } = useAgenticAction();

  const handleAddExpense = async (e: React.FormEvent) => {
    e.preventDefault();
    await runAgenticAction('add-expense', 'budget', { clientId: 'CURRENT_CLIENT_ID', expense });
    setExpense({ category: '', amount: '' });
  };

  return (
    <div>
      <OnboardingTooltip content="This action is handled by <PERSON>, your AI wedding planner agent. She coordinates specialized agents to update your live data in real time!">
        <h2>Add Expense</h2>
      </OnboardingTooltip>
      <form onSubmit={handleAddExpense}>
        <input type="text" placeholder="Category" value={expense.category} onChange={e => setExpense({ ...expense, category: e.target.value })} required />
        <input type="number" placeholder="Amount" value={expense.amount} onChange={e => setExpense({ ...expense, amount: e.target.value })} required />
        <button type="submit" disabled={loading}>Add Expense</button>
      </form>
      {loading && <AgentChatBubble message="Ella is working on your request..." />}

      {loading && <Spinner />}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {result && <p style={{ color: 'green' }}>Expense added!</p>}
    </div>
  );
}
