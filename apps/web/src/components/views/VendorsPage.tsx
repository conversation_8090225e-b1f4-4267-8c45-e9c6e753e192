import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import Modal from '@/src/components/common/Modal';
import PageTitle from '@/src/components/common/PageTitle';
import Textarea from '@/src/components/common/Textarea';
import { BuildingStorefrontIcon, ClipboardDocumentIcon, EnvelopeIcon, MagnifyingGlassIcon, PencilIcon, PlusIcon, SparklesIcon, TrashIcon, XCircleIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { CrudItem, useCrudManager } from '@/src/hooks/useCrudManager';
import { ellaDraftVendorInquiry, ellaSuggestVendor } from '@/src/services/ai/index';
import { Vendor, VendorCategory, VendorStatus } from '@/src/types/index';
import { generateClientSideId } from '@/src/utils/idUtils';
import React, { useContext, useEffect, useState } from 'react';
import VendorDirectoryPage from './VendorDirectoryPage';

interface VendorItem extends Vendor, CrudItem { }
type VendorFormState = Omit<Vendor, 'id'>;

const initialVendorFormState: VendorFormState = {
  name: '', category: 'Venue', contactPerson: '', email: '', phone: '',
  status: 'Researching', quotedPrice: undefined, actualCost: undefined, notes: '',
};

const VENDOR_CATEGORIES: VendorCategory[] = ['Venue', 'Photographer', 'Caterer', 'Florist', 'DJ/Band', 'Attire', 'Stationery', 'Transportation', 'Cake', 'Officiant', 'Videographer', 'Wedding Planner', 'Hair & Makeup', 'Favors', 'Other'];
const VENDOR_STATUSES: VendorStatus[] = ['Researching', 'Contacted', 'Awaiting Quote', 'Quote Received', 'Shortlisted', 'Booked', 'Declined'];

const VendorsPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();
  const [isAISuggesting, setIsAISuggesting] = useState(false);
  const [showInquiryModal, setShowInquiryModal] = useState(false);
  const [selectedVendorForInquiry, setSelectedVendorForInquiry] = useState<Vendor | null>(null);
  const [draftedInquiryEmail, setDraftedInquiryEmail] = useState<string | null>(null);
  const [isDraftingEmail, setIsDraftingEmail] = useState(false);
  const [copySuccess, setCopySuccess] = useState('');
  const [activeTab, setActiveTab] = useState<'my-vendors' | 'vendor-directory'>('my-vendors');

  const vendorValidator = (formState: VendorFormState) => {
    const errors: Record<string, string> = {};
    if (!formState.name.trim()) errors.name = "Vendor name is required.";
    if (!formState.category) errors.category = "Category is required.";
    if (formState.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formState.email)) errors.email = "Invalid email format.";
    if (formState.quotedPrice !== undefined && formState.quotedPrice < 0) errors.quotedPrice = "Quoted price cannot be negative.";
    if (formState.actualCost !== undefined && formState.actualCost < 0) errors.actualCost = "Actual cost cannot be negative.";
    return { isValid: Object.keys(errors).length === 0, errors };
  };

  const contextVendors = plannerCtx?.currentWedding?.vendors || [];
  const setContextVendors = (newItems: VendorItem[] | ((prevItems: VendorItem[]) => VendorItem[])) => {
    if (plannerCtx && plannerCtx.currentWeddingId) {
      const finalItems = typeof newItems === 'function' ? newItems(plannerCtx.currentWedding?.vendors || []) : newItems;
      plannerCtx.updateDataForCurrentWedding('vendors', finalItems);
    }
  };

  const {
    items: _vendors,
    showForm, editingItem, formState, formErrors,
    handleInputChange, handleSubmit, handleEdit, handleDelete, handleAddNew, handleCancel,
  } = useCrudManager<VendorItem, VendorFormState>({
    initialItems: contextVendors,
    initialFormState: initialVendorFormState,
    itemValidator: vendorValidator,
    crudSetItems: setContextVendors,
    itemTypeForToast: "Vendor",
  });

  useEffect(() => {
    if (plannerCtx?.currentWedding && plannerCtx.appMode === 'dev' && plannerCtx.currentWedding.vendors.length === 0 && !plannerCtx.currentWedding.isAIPopulated) {
      const devMockVendors: VendorItem[] = [
        { id: 'v-dev1', name: "Client's Venue", category: 'Venue', status: 'Booked', quotedPrice: 5000, actualCost: 4800, contactPerson: '', email: '', phone: '', notes: '' },
        { id: 'v-dev2', name: "Client's Photographer", category: 'Photographer', status: 'Researching', contactPerson: '', email: '', phone: '', notes: '' },
      ];
      if (plannerCtx.currentWeddingId) {
        plannerCtx.updateDataForCurrentWedding('vendors', devMockVendors);
        addToast("Loaded placeholder vendors for client.", "info", 3000);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plannerCtx?.currentWedding?.id]); // Rerun if current wedding changes


  const formatCurrency = (amount?: number) => (amount === undefined || amount === null) ? '-' : new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const getStatusBadgeColor = (status: VendorStatus) => {
    switch (status) {
      case 'Booked': return 'bg-positive/20 text-positive';
      case 'Shortlisted': return 'bg-primary/20 text-primary';
      case 'Quote Received': return 'bg-primary/30 text-primary';
      case 'Contacted': case 'Awaiting Quote': return 'bg-warning/20 text-warning-foreground';
      case 'Declined': return 'bg-destructive/20 text-destructive';
      case 'Researching': default: return 'bg-muted/20 text-muted-foreground';
    }
  };

  const suggestVendorByAI = async () => {
    if (!plannerCtx || !plannerCtx.currentWedding || !plannerCtx.currentWedding.weddingDetails) {
      addToast("Client's wedding details are not available for AI suggestion.", "error"); return;
    }
    setIsAISuggesting(true);
    addToast("Ella is searching for vendor suggestions...", "info");
    try {
      const suggestion = await ellaSuggestVendor(plannerCtx.currentWedding.vendors, plannerCtx.currentWedding.weddingDetails, plannerCtx.appMode);
      if (suggestion.name && suggestion.category && suggestion.status) {
        const newVendor: VendorItem = {
          ...initialVendorFormState, id: generateClientSideId(), name: suggestion.name,
          category: suggestion.category as VendorCategory, status: suggestion.status,
        };
        setContextVendors(prev => [...prev, newVendor]);
        addToast(`Ella suggested adding vendor for client: ${suggestion.name} (Category: ${suggestion.category})`, "success");
      } else {
        addToast("Ella couldn't find a new vendor suggestion for this client right now.", "info");
      }
    } catch (error) {
      console.error("Error suggesting vendor:", error);
      addToast("Error suggesting vendor by AI.", "error");
    } finally {
      setIsAISuggesting(false);
    }
  };

  const handleDraftInquiry = async (vendor: Vendor) => {
    if (!plannerCtx || !plannerCtx.currentWedding || !plannerCtx.currentWedding.weddingDetails) {
      addToast("Client's wedding details are not available to draft an inquiry.", "error"); return;
    }
    setSelectedVendorForInquiry(vendor);
    setIsDraftingEmail(true);
    setDraftedInquiryEmail(null);
    setCopySuccess('');
    setShowInquiryModal(true);
    addToast(`Ella is drafting an inquiry for ${vendor.name}...`, "info");
    try {
      const emailText = await ellaDraftVendorInquiry(vendor, plannerCtx.currentWedding.weddingDetails, plannerCtx.appMode, plannerCtx.plannerProfile?.name);
      setDraftedInquiryEmail(emailText);
      addToast(`Inquiry for ${vendor.name} drafted successfully!`, "success");
    } catch (error) {
      console.error("Error drafting inquiry:", error);
      setDraftedInquiryEmail("Ella had trouble drafting this. Try again.");
      addToast(`Error drafting inquiry for ${vendor.name}.`, "error");
    } finally {
      setIsDraftingEmail(false);
    }
  };

  const copyToClipboard = () => {
    if (draftedInquiryEmail) {
      navigator.clipboard.writeText(draftedInquiryEmail).then(() => {
        setCopySuccess('Email copied to clipboard!');
        addToast('Email copied to clipboard!', 'success');
        setTimeout(() => setCopySuccess(''), 2000);
      }, (_err) => {
        setCopySuccess('Failed to copy. Please copy manually.');
        addToast('Failed to copy email.', 'error');
      });
    }
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return <div className="flex items-center justify-center h-screen"><LoadingSpinner text="Loading vendor details..." /></div>;
  }
  const clientCoupleNames = plannerCtx.currentWedding.weddingDetails.coupleNames;

  return (
    <div className="space-y-6">
      <PageTitle title={`${clientCoupleNames}'s Vendor Management`} subtitle="Manage client vendors and discover new ones" icon={<BuildingStorefrontIcon />} />

      {/* Tab Navigation */}
      <div className="border-b border-border">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('my-vendors')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'my-vendors'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
              }`}
          >
            My Vendors
          </button>
          <button
            onClick={() => setActiveTab('vendor-directory')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'vendor-directory'
              ? 'border-primary text-primary'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
              }`}
          >
            <MagnifyingGlassIcon className="w-4 h-4 inline mr-1" />
            Vendor Directory
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'my-vendors' ? (
        <>
          <div className="flex space-x-3">
            {!showForm && (<Button onClick={handleAddNew} leftIcon={<PlusIcon className="w-5 h-5" />} variant="primary">Add Vendor for Client</Button>)}
            <Button onClick={suggestVendorByAI} leftIcon={<SparklesIcon className="w-5 h-5" />} variant="secondary" isLoading={isAISuggesting} disabled={isAISuggesting || !plannerCtx?.currentWedding?.weddingDetails}>
              {isAISuggesting ? "Ella is Searching..." : "Ella Suggests Vendor for Client"}
            </Button>
          </div>

          {showForm && (
            <Card title={editingItem ? "Edit Client's Vendor" : "Add New Vendor for Client"}>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input label="Vendor Name" name="name" value={formState.name} onChange={handleInputChange} error={formErrors?.name} required />
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-muted-foreground mb-1.5">Category</label>
                    <select id="category" name="category" value={formState.category} onChange={handleInputChange} className={`w-full px-4 py-2.5 rounded-md bg-input border ${formErrors?.category ? 'border-destructive' : 'border-border'} text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary`}>
                      {VENDOR_CATEGORIES.map(cat => <option key={cat} value={cat}>{cat}</option>)}
                    </select>
                    {formErrors?.category && <p className="mt-1.5 text-xs text-destructive">{formErrors.category}</p>}
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input label="Contact Person" name="contactPerson" value={formState.contactPerson || ''} onChange={handleInputChange} />
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-muted-foreground mb-1.5">Status</label>
                    <select id="status" name="status" value={formState.status} onChange={handleInputChange} className="w-full px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary sm:text-sm">
                      {VENDOR_STATUSES.map(stat => <option key={stat} value={stat}>{stat}</option>)}
                    </select>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input label="Email" name="email" type="email" value={formState.email || ''} onChange={handleInputChange} error={formErrors?.email} />
                  <Input label="Phone" name="phone" type="tel" value={formState.phone || ''} onChange={handleInputChange} />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input label="Quoted Price ($)" name="quotedPrice" type="number" value={formState.quotedPrice === undefined ? '' : formState.quotedPrice} onChange={handleInputChange} min="0" step="0.01" error={formErrors?.quotedPrice} />
                  <Input label="Actual Cost ($)" name="actualCost" type="number" value={formState.actualCost === undefined ? '' : formState.actualCost} onChange={handleInputChange} min="0" step="0.01" error={formErrors?.actualCost} />
                </div>
                <Textarea label="Notes" name="notes" value={formState.notes || ''} onChange={handleInputChange} rows={3} />
                <div className="flex space-x-3 pt-2">
                  <Button type="submit" variant="primary">{editingItem ? 'Update Vendor' : 'Save Vendor'}</Button>
                  <Button type="button" onClick={handleCancel} variant="ghost" leftIcon={<XCircleIcon className="w-5 h-5" />}>Cancel</Button>
                </div>
              </form>
            </Card>
          )}

          <Card title="Client&apos;s Vendors">
            {(plannerCtx.currentWedding.vendors || []).length === 0 && !isAISuggesting ? (
              <p className="text-muted-foreground text-center py-8">Client&apos;s vendor list is empty.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-border">
                  <thead className="bg-secondary/50"><tr>{['Name', 'Category', 'Status', 'Contact', 'Quoted', 'Actual', 'Actions'].map(h => <th key={h} scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider whitespace-nowrap">{h}</th>)}</tr></thead>
                  <tbody className="bg-card divide-y divide-border">
                    {(plannerCtx.currentWedding.vendors || []).map((vendor) => (
                      <tr key={vendor.id} className="hover:bg-accent/50 transition-colors">
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-foreground">{vendor.name}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{vendor.category}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm"><span className={`px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(vendor.status)}`}>{vendor.status}</span></td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">
                          {vendor.contactPerson || ''}
                          {vendor.email && <div className="text-xs text-muted-foreground/80">{vendor.email}</div>}
                          {vendor.phone && <div className="text-xs text-muted-foreground/80">{vendor.phone}</div>}
                          {(!vendor.contactPerson && !vendor.email && !vendor.phone) && '-'}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{formatCurrency(vendor.quotedPrice)}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{formatCurrency(vendor.actualCost)}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium space-x-2">
                          <Button size="sm" variant="ghost" onClick={() => handleDraftInquiry(vendor)} className="!p-1 text-accent-foreground hover:text-primary" title="Draft Inquiry"><EnvelopeIcon className="w-4 h-4" /></Button>
                          <Button size="sm" variant="ghost" onClick={() => handleEdit(vendor as VendorItem)} className="!p-1 text-primary hover:text-primary/80" title="Edit Vendor"><PencilIcon className="w-4 h-4" /></Button>
                          <Button size="sm" variant="ghost" onClick={() => handleDelete(vendor.id)} className="!p-1 text-destructive hover:text-destructive/80" title="Delete Vendor"><TrashIcon className="w-4 h-4" /></Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </Card>

          {selectedVendorForInquiry && (
            <Modal isOpen={showInquiryModal} onClose={() => { setShowInquiryModal(false); setSelectedVendorForInquiry(null); setDraftedInquiryEmail(null); setCopySuccess(''); }} title={`Draft Inquiry for ${selectedVendorForInquiry.name}`} size="lg">
              {isDraftingEmail ? (<div className="flex flex-col items-center justify-center min-h-[200px]"><LoadingSpinner text="Ella is drafting your email..." /></div>) : (
                <div className="space-y-4">
                  {draftedInquiryEmail ? (
                    <>
                      <Textarea value={draftedInquiryEmail} readOnly rows={15} className="!bg-background !border-border text-sm custom-scrollbar" />
                      <div className="flex justify-between items-center">
                        <Button onClick={copyToClipboard} leftIcon={<ClipboardDocumentIcon className="w-4 h-4" />} variant="secondary" size="sm">Copy to Clipboard</Button>
                        {copySuccess && <p className="text-xs text-positive">{copySuccess}</p>}
                      </div>
                    </>
                  ) : (<p className="text-muted-foreground">Could not load draft.</p>)}
                  <div className="mt-6 pt-4 border-t border-border flex justify-end">
                    <Button onClick={() => { setShowInquiryModal(false); setSelectedVendorForInquiry(null); setDraftedInquiryEmail(null); setCopySuccess(''); }} variant="primary">Close</Button>
                  </div>
                </div>
              )}
            </Modal>
          )}
        </>
      ) : (
        <VendorDirectoryPage />
      )}
    </div>
  );
};

export default VendorsPage;
