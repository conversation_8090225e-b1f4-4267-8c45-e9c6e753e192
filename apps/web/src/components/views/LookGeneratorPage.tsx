import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import { SparklesIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { generateLookIdeas } from '@/src/services/ai/index';
import { ChatMessage } from '@/src/types/index';
import Image from 'next/image';
import React, { useContext, useState } from 'react';

const LookGeneratorPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();
  const [preferences, setPreferences] = useState<string>('');
  const [generatedLooks, setGeneratedLooks] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleGenerateLook = async () => {
    if (!preferences.trim()) {
      addToast("Please describe preferences for the client&apos;s look!", "warning");
      return;
    }
    if (!plannerCtx || !plannerCtx.currentWedding) {
      addToast("Please select a client wedding first.", "error");
      return;
    }
    setIsLoading(true);
    addToast("Ella is designing some look ideas...", "info");

    const userPromptMessage: ChatMessage = {
      id: `user-look-${Date.now()}`,
      text: `Generate a look for ${plannerCtx.currentWedding.weddingDetails.coupleNames} based on: ${preferences}`,
      sender: 'user',
      timestamp: new Date(),
    };
    setGeneratedLooks([userPromptMessage]);

    try {
      if (plannerCtx.appMode === 'dev') {
        await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate AI delay

        const lookIcon = `data:image/svg+xml;base64,${btoa(`
          <svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="600" height="400" fill="#FDF2F8"/>
            <circle cx="300" cy="200" r="100" fill="#FCE7F3" stroke="#EC4899" stroke-width="3"/>
            <path d="M250 170 L280 200 L350 130" stroke="#EC4899" stroke-width="4" fill="none"/>
            <text x="300" y="330" text-anchor="middle" fill="#BE185D" font-family="Arial" font-size="20">Wedding Look</text>
            <text x="300" y="360" text-anchor="middle" fill="#DB2777" font-family="Arial" font-size="16">AI-Generated Style Coming Soon</text>
          </svg>
        `)}`;
        let devCaption = `A modern wedding dress style look for your client.`;
        if (preferences) {
          devCaption = `A modern wedding dress style look for your client, inspired by '${preferences.substring(0, 50)}...'`;
        }


        const mockEllaMessage: ChatMessage = {
          id: `ella-dev-look-${Date.now()}`,
          text: devCaption,
          sender: 'ella',
          timestamp: new Date(),
          imageUrl: lookIcon,
        };
        setGeneratedLooks(prev => [...prev, mockEllaMessage]);
        addToast("Ella prepared a look idea for your client!", "success");

      } else {
        // Production mode: call the actual AI service
        const response = await generateLookIdeas(preferences, plannerCtx.appMode, plannerCtx.currentWedding.weddingDetails);

        if (response.generatedImages && response.generatedImages.length > 0) {
          const newLookMessages: ChatMessage[] = response.generatedImages
            .map((img, index): ChatMessage | null => {
              if (img.image && img.image.imageBytes) {
                return {
                  id: `ella-look-${Date.now()}-${index}`,
                  text: `Here's an idea for your client's "${preferences.substring(0, 30)}..." look:`,
                  sender: 'ella',
                  timestamp: new Date(),
                  imageUrl: `data:image/jpeg;base64,${img.image.imageBytes}`,
                };
              }
              return null;
            })
            .filter((message): message is ChatMessage => message !== null);

          if (newLookMessages.length > 0) {
            setGeneratedLooks(prev => [...prev, ...newLookMessages]);
            addToast(`Ella found ${newLookMessages.length} look idea(s) for your client!`, "success");
          } else {
            const issueMessage: ChatMessage = {
              id: `ella-look-issue-${Date.now()}`,
              text: "Ella found some ideas, but there was a problem displaying the images. Please try again or rephrase your request.",
              sender: 'ella', timestamp: new Date(),
            };
            setGeneratedLooks(prev => [...prev, issueMessage]);
            addToast("Ella had trouble displaying images for the look.", "warning");
          }
        } else {
          const noResultMessage: ChatMessage = {
            id: `ella-look-empty-${Date.now()}`,
            text: "Sorry, I couldn't generate a look based on those preferences for the client right now. Try being more specific or a bit broader!",
            sender: 'ella', timestamp: new Date(),
          };
          setGeneratedLooks(prev => [...prev, noResultMessage]);
          addToast("Ella couldn't generate a look with those preferences.", "info");
        }
      }
    } catch (error) {
      console.error("Error generating look:", error);
      addToast("Oops! Something went wrong generating the look.", "error");
      const errorMessage: ChatMessage = {
        id: `error-look-${Date.now()}`,
        text: "Oops! Something went wrong while generating the look. Please try again.",
        sender: 'system', timestamp: new Date(),
      };
      setGeneratedLooks(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return <div className="flex items-center justify-center h-screen"><LoadingSpinner text="Loading look generator..." /></div>;
  }
  const clientWeddingDetails = plannerCtx.currentWedding.weddingDetails;
  const weddingVibe = clientWeddingDetails?.vibe || "their unique";

  return (
    <div className="space-y-6">
      <PageTitle
        title={`Personalized Look Generator for ${clientWeddingDetails.coupleNames}`}
        subtitle={`Let Ella help you visualize your client's ${weddingVibe} wedding style!`}
        icon={<SparklesIcon />}
      />
      <Card title="Describe Client&apos;s Dream Look">
        <div className="space-y-4">
          <Input
            label="Preferences (e.g., 'romantic lace gown with long sleeves', 'modern minimalist suit', 'boho chic accessories')"
            id="lookPreferences"
            value={preferences}
            onChange={(e) => setPreferences(e.target.value)}
            placeholder={`e.g., classic ballroom dress for client, ${weddingVibe} floral details, vintage jewelry...`}
            className="!bg-input focus:!ring-primary focus:!border-primary"
          />
          <Button onClick={handleGenerateLook} isLoading={isLoading} disabled={isLoading || !preferences.trim()} leftIcon={<SparklesIcon className="w-5 h-5" />}>
            {isLoading ? 'Ella is Designing...' : 'Generate Look Ideas for Client'}
          </Button>
        </div>
      </Card>

      {isLoading && generatedLooks.length <= 1 && (
        <Card><LoadingSpinner text="Ella is conjuring up some beautiful ideas for your client..." color="text-primary" /></Card>
      )}

      {generatedLooks.length > 0 && (
        <Card title="Generated Look Ideas for Client">
          <div className="space-y-6">
            {generatedLooks.map((msg) => (
              <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`p-4 rounded-xl max-w-lg shadow-md ${msg.sender === 'user' ? 'bg-primary text-primary-foreground'
                  : msg.sender === 'ella' ? 'bg-secondary text-secondary-foreground'
                    : 'bg-warning text-black'
                  }`}>
                  <p className="text-sm whitespace-pre-wrap mb-2">{msg.text}</p>
                  {msg.imageUrl && (<Image src={msg.imageUrl} alt="Generated look" width={500} height={400} className="rounded-lg w-full h-auto max-h-[500px] object-contain border border-border" />)}
                  <p className="text-xs opacity-70 mt-2 text-right">{new Date(msg.timestamp).toLocaleTimeString()}</p>
                </div>
              </div>
            ))}
            {isLoading && generatedLooks.length > 0 && generatedLooks[generatedLooks.length - 1].sender === 'user' && (
              <div className="flex justify-start">
                <div className="p-4 rounded-xl max-w-lg bg-secondary text-secondary-foreground flex items-center shadow-md">
                  <SparklesIcon className="w-5 h-5 text-primary animate-pulse mr-2" />
                  <span className="italic">Ella is thinking...</span>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {generatedLooks.length === 0 && !isLoading && (
        <Card><p className="text-muted-foreground text-center py-8">Describe your client&apos;s desired style above.</p></Card>
      )}
    </div>
  );
};

export default LookGeneratorPage;
