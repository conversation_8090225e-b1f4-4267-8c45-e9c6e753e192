import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import { ClipboardIcon, PlusIcon, SparklesIcon, TrashIcon, XCircleIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { ellaSuggestVisionBoardImage } from '@/src/services/ai/index';
import { VisionBoardItem } from '@/src/types/index';
import { generateClientSideId } from '@/src/utils/idUtils';
import Image from 'next/image';
import React, { ChangeEvent, useContext, useState } from 'react';

const VisionBoardPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();

  const visionBoardItems = plannerCtx?.currentWedding?.visionBoardItems || [];
  const setVisionBoardItems = (newItems: VisionBoardItem[] | ((prevItems: VisionBoardItem[]) => VisionBoardItem[])) => {
    if (plannerCtx && plannerCtx.currentWeddingId) {
      const finalItems = typeof newItems === 'function' ? newItems(plannerCtx.currentWedding?.visionBoardItems || []) : newItems;
      plannerCtx.updateDataForCurrentWedding('visionBoardItems', finalItems);
    }
  };

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [newItemCaption, setNewItemCaption] = useState('');
  const [fileError, setFileError] = useState<string | null>(null);
  const [isAISuggestingVisionItem, setIsAISuggestingVisionItem] = useState(false);

  // Removed useEffect for dev mock data loading; now handled in WeddingLayout.tsx

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    setFileError(null);
    setPreviewUrl(null);
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        setFileError("File is too large. Max 5MB.");
        addToast("Image too large (max 5MB).", "error");
        setSelectedFile(null);
        return;
      }
      if (!file.type.startsWith('image/')) {
        setFileError("Invalid file type. Please select an image.");
        addToast("Invalid file type. Please select an image.", "error");
        setSelectedFile(null);
        return;
      }
      setSelectedFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setSelectedFile(null);
    }
  };

  const handleAddItem = () => {
    if (!selectedFile) {
      setFileError("Please select an image file to upload.");
      addToast("Please select an image file.", "error");
      return;
    }
    if (fileError) return;

    const reader = new FileReader();
    reader.onloadend = () => {
      const newItem: VisionBoardItem = {
        id: generateClientSideId(),
        imageUrl: reader.result as string,
        caption: newItemCaption,
        tags: newItemCaption.toLowerCase().split(' ').filter(tag => tag.length > 2 && tag.length < 15),
      };
      setVisionBoardItems(prev => [...prev, newItem]);
      addToast("Inspiration added to client's board!", "success");
      setSelectedFile(null);
      setNewItemCaption('');
      setPreviewUrl(null);
      const fileInput = document.getElementById('visionBoardFileInput') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = "";
      }
    };
    reader.onerror = () => {
      setFileError("Failed to read file. Please try again.");
      addToast("Failed to read file.", "error");
    };
    reader.readAsDataURL(selectedFile);
  };

  const handleDeleteItem = (id: string) => {
    setVisionBoardItems(prev => prev.filter(item => item.id !== id));
    addToast("Inspiration removed from client's board.", "success");
  };

  const handleAISuggestion = async () => {
    if (!plannerCtx || !plannerCtx.currentWedding || !plannerCtx.currentWedding.weddingDetails) {
      addToast("Please select a client wedding first.", "error");
      return;
    }
    setIsAISuggestingVisionItem(true);
    addToast("Ella is searching for inspiration...", "info");
    try {
      const vibe = plannerCtx.currentWedding.weddingDetails.vibe || "inspiration";
      const suggestedItem = await ellaSuggestVisionBoardImage(vibe, plannerCtx.appMode);

      if (suggestedItem) {
        setVisionBoardItems(prev => [...prev, suggestedItem]);
        addToast("Ella added an inspiration to the client's board!", "success");
      } else {
        addToast("Ella couldn't generate a suggestion at this time.", "info");
      }
    } catch (error) {
      console.error("Error getting AI vision board suggestion:", error);
      addToast("An error occurred while Ella was generating suggestions.", "error");
    } finally {
      setIsAISuggestingVisionItem(false);
    }
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return <div className="flex items-center justify-center h-screen"><LoadingSpinner text="Loading vision board..." /></div>;
  }
  const clientCoupleNames = plannerCtx.currentWedding.weddingDetails.coupleNames;


  return (
    <div className="space-y-6">
      <PageTitle title={`${clientCoupleNames}'s Vision Board`} subtitle="Collect and organize all visual inspirations for your client. Ella can help refine their vision!" icon={<ClipboardIcon />} />

      <Card title="Add New Inspiration for Client">
        <div className="space-y-4">
          <div>
            <label htmlFor="visionBoardFileInput" className="block text-sm font-medium text-muted-foreground mb-1.5">Upload Image</label>
            <input
              type="file"
              id="visionBoardFileInput"
              accept="image/*"
              onChange={handleFileChange}
              className="block w-full text-sm text-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 file:cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
            />
            {fileError && <p className="mt-1.5 text-xs text-destructive">{fileError}</p>}
            {previewUrl && !fileError && (
              <div className="mt-3">
                <Image src={previewUrl} alt="Selected preview" width={300} height={160} className="max-h-40 rounded-md border border-border" />
              </div>
            )}
          </div>
          <Input label="Caption (optional)" placeholder="Describe this inspiration..." value={newItemCaption} onChange={(e) => setNewItemCaption(e.target.value)} />
          <div className="flex space-x-3">
            <Button onClick={handleAddItem} leftIcon={<PlusIcon className="w-5 h-5" />} disabled={!selectedFile || !!fileError}>Add to Board</Button>
            <Button
              onClick={handleAISuggestion}
              variant="secondary"
              leftIcon={<SparklesIcon className="w-5 h-5" />}
              isLoading={isAISuggestingVisionItem}
              disabled={isAISuggestingVisionItem}
            >
              {isAISuggestingVisionItem ? "Ella is Designing..." : "Get AI Suggestion"}
            </Button>
            {(selectedFile || newItemCaption) && (
              <Button
                onClick={() => {
                  setSelectedFile(null);
                  setNewItemCaption('');
                  setPreviewUrl(null);
                  setFileError(null);
                  const fileInput = document.getElementById('visionBoardFileInput') as HTMLInputElement;
                  if (fileInput) fileInput.value = "";
                  addToast("Cleared vision board input fields.", "info");
                }}
                variant="ghost"
                size="sm"
                leftIcon={<XCircleIcon className="w-4 h-4" />}
                className="!text-muted-foreground"
              >
                Clear
              </Button>
            )}
          </div>
        </div>
      </Card>

      {visionBoardItems.length === 0 && !isAISuggestingVisionItem && (
        <Card><p className="text-muted-foreground text-center py-8">Client's vision board is empty. Upload an image or ask Ella for suggestions!</p></Card>
      )}
      {isAISuggestingVisionItem && visionBoardItems.length === 0 && (
        <Card><LoadingSpinner text="Ella is searching for inspiration..." /></Card>
      )}


      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {visionBoardItems.map(item => (
          <Card key={item.id} className="group relative overflow-hidden !p-0 !border-transparent hover:!border-primary/50">
            <Image src={item.imageUrl} alt={item.caption || 'Vision board item'} width={300} height={256} className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 p-4 flex flex-col justify-end">
              {item.caption && <p className="text-white text-sm font-semibold mb-1 drop-shadow-md">{item.caption}</p>}
              {item.tags && item.tags.length > 0 && (<div className="flex flex-wrap gap-1 mb-2">{item.tags.slice(0, 3).map(tag => <span key={tag} className="text-xs bg-primary/70 text-primary-foreground px-2 py-0.5 rounded-full backdrop-blur-sm">{tag}</span>)}</div>)}
              <Button size="sm" variant="danger" onClick={() => handleDeleteItem(item.id)} className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 self-start !py-1 !px-2">
                <TrashIcon className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default VisionBoardPage;
