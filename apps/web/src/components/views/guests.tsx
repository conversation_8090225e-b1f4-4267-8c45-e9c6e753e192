// src/pages/guests.tsx
import OnboardingTooltip from '@/src/components/OnboardingTooltip';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import React, { useState } from 'react';

import AgentChatBubble from '@/src/components/AgentChatBubble';

import Spinner from '@/src/components/Spinner';

export default function GuestsPage() {
  const [guest, setGuest] = useState({ name: '', email: '' });
  const { runAgenticAction, loading, error, result } = useAgenticAction();

  const handleAddGuest = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await runAgenticAction('add-guest', 'guest', { clientId: 'CURRENT_CLIENT_ID', guest });
    if (success) {
      setGuest({ name: '', email: '' });
    }
  };

  return (
    <div>
      <OnboardingTooltip content="This action is handled by <PERSON>, your AI wedding planner agent. She coordinates specialized agents to update your live data in real time!">
        <h2>Add Guest</h2>
      </OnboardingTooltip>
      <form onSubmit={handleAddGuest}>
        <input type="text" placeholder="Guest Name" value={guest.name} onChange={e => setGuest({ ...guest, name: e.target.value })} required />
        <input type="email" placeholder="Email" value={guest.email} onChange={e => setGuest({ ...guest, email: e.target.value })} required />
        <button type="submit" disabled={loading}>Add Guest</button>
      </form>
      {loading && <AgentChatBubble message="Ella is working on your request..." />}

      {loading && <Spinner />}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {result && <p style={{ color: 'green' }}>Guest added!</p>}
    </div>
  );
}
