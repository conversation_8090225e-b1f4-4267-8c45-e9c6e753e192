// src/pages/vision.tsx
import OnboardingTooltip from '@/src/components/OnboardingTooltip';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import React, { useState } from 'react';

import AgentChatBubble from '@/src/components/AgentChatBubble';

import Spinner from '@/src/components/Spinner';

export default function VisionPage() {
  const [image, setImage] = useState({ url: '', description: '' });
  const { runAgenticAction, loading, error, result } = useAgenticAction();

  const handleAddImage = async (e: React.FormEvent) => {
    e.preventDefault();
    const success = await runAgenticAction('add-image', 'vision', { clientId: 'CURRENT_CLIENT_ID', image });
    if (success) {
      setImage({ url: '', description: '' });
    }
  };

  return (
    <div>
      <OnboardingTooltip content="This action is handled by <PERSON>, your AI wedding planner agent. She coordinates specialized agents to update your live data in real time!">
        <h2>Add Vision Image</h2>
      </OnboardingTooltip>
      <form onSubmit={handleAddImage}>
        <label htmlFor="image-url">Image URL</label>
        <input id="image-url" type="text" placeholder="Image URL" value={image.url} onChange={e => setImage({ ...image, url: e.target.value })} required />
        <label htmlFor="image-description">Description</label>
        <input id="image-description" type="text" placeholder="Description" value={image.description} onChange={e => setImage({ ...image, description: e.target.value })} required />
        <button type="submit" disabled={loading}>Add Image</button>
      </form>
      {loading && <AgentChatBubble message="Ella is working on your request..." />}

      {loading && <Spinner />}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {result && <p style={{ color: 'green' }}>Image added!</p>}
    </div>
  );
}
