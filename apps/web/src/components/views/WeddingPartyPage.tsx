import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import { SparklesIcon, UserGroupIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { generateInitialWeddingParty, type WeddingPartyMember } from '@/src/services/ai/index';
import Image from 'next/image';
import React, { useContext, useState } from 'react';

const WeddingPartyPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();
  const [partyMembers, setPartyMembers] = useState<WeddingPartyMember[]>([]);
  const [isGeneratingParty, setIsGeneratingParty] = useState(false);
  const [hasGenerated, setHasGenerated] = useState(false);

  const handleGenerateAIWeddingParty = async () => {
    if (!plannerCtx || !plannerCtx.currentWedding) {
      addToast("Please select a client wedding first.", "error");
      return;
    }

    setIsGeneratingParty(true);
    addToast("Ella is creating AI-generated wedding party members...", "info");

    try {
      const generatedParty = await generateInitialWeddingParty(
        plannerCtx.currentWedding.weddingDetails,
        plannerCtx.appMode
      );

      setPartyMembers(generatedParty);
      setHasGenerated(true);
      addToast(`Ella generated ${generatedParty.length} wedding party members with AI avatars!`, "success");
    } catch (error) {
      console.error("Error generating wedding party:", error);
      addToast("Error generating wedding party with AI.", "error");
    } finally {
      setIsGeneratingParty(false);
    }
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner text="Loading wedding details..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PageTitle
        title="Wedding Party"
        subtitle="Coordinate with your bridesmaids, groomsmen, and other key people with AI-generated profiles."
        icon={<UserGroupIcon />}
      />

      <Card title="Your Client's Wedding Crew">
        {!hasGenerated && partyMembers.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              No wedding party members yet. Let Ella generate some AI-powered wedding party members based on your client&apos;s details!
            </p>
            <Button
              onClick={handleGenerateAIWeddingParty}
              leftIcon={<SparklesIcon className="w-5 h-5" />}
              isLoading={isGeneratingParty}
              disabled={isGeneratingParty}
              className="mx-auto"
            >
              {isGeneratingParty ? "Ella is Creating..." : "Generate AI Wedding Party"}
            </Button>
          </div>
        )}

        {isGeneratingParty && (
          <div className="text-center py-8">
            <LoadingSpinner text="Ella is generating AI wedding party members..." />
          </div>
        )}

        {partyMembers.length > 0 && (
          <>
            <div className="flex justify-between items-center mb-4">
              <p className="text-sm text-muted-foreground">
                AI-generated wedding party for {plannerCtx.currentWedding.weddingDetails.coupleNames}
              </p>
              <Button
                onClick={handleGenerateAIWeddingParty}
                variant="secondary"
                size="sm"
                leftIcon={<SparklesIcon className="w-4 h-4" />}
                isLoading={isGeneratingParty}
                disabled={isGeneratingParty}
              >
                Regenerate with AI
              </Button>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {partyMembers.map(member => (
                <Card key={member.id} className="text-center !p-4 hover:!shadow-primary/40">
                  <Image
                    src={member.avatar}
                    alt={member.name}
                    width={96}
                    height={96}
                    className="w-24 h-24 rounded-full mx-auto mb-3 border-2 border-primary object-cover"
                  />
                  <h4 className="font-semibold text-foreground">{member.name}</h4>
                  <p className="text-sm text-primary mb-1">{member.role}</p>
                  {member.description && (
                    <p className="text-xs text-muted-foreground">{member.description}</p>
                  )}
                </Card>
              ))}
            </div>
          </>
        )}
      </Card>

      <Card title="Collaboration Tools (Coming Soon)">
        <p className="text-muted-foreground text-center py-8">
          Soon, you&apos;ll be able to use this space for:
        </p>
        <ul className="list-disc list-inside text-muted-foreground text-center space-y-2">
          <li>Group chats facilitated by Ella</li>
          <li>Assigning tasks and tracking progress</li>
          <li>Sharing important updates and documents</li>
          <li>Role-based permissions for sensitive information</li>
        </ul>
      </Card>
    </div>
  );
};

export default WeddingPartyPage;