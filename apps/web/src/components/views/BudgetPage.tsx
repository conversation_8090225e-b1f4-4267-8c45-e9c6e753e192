import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import AISuggestionModal from '@/src/components/feature/budget/AISuggestionModal';
import BudgetPageContent from '@/src/components/feature/budget/BudgetPageContent';
import BudgetSolutionModal from '@/src/components/feature/budget/BudgetSolutionModal';
import { CurrencyDollarIcon } from '@/src/components/icons/HeroIcons';
import { useBudgetAISuggestion } from '@/src/hooks/useBudgetAISuggestion';
import { useBudgetInsights } from '@/src/hooks/useBudgetInsights';
import { useBudgetManager } from '@/src/hooks/useBudgetManager';
import { CrudItem, useCrudManager } from '@/src/hooks/useCrudManager';
import { getAutonomyButtonText, validateBudgetSolutionRequest } from '@/src/utils/budgetPageUtils';
import {
  BudgetFormState,
  BudgetItemType,
  budgetValidator,
  initialBudgetFormState
} from '@/src/utils/budgetUtils';

interface BudgetItemWithCrud extends BudgetItemType, CrudItem { }

const BudgetPage: React.FC = () => {
  const budgetManager = useBudgetManager();
  const budgetInsights = useBudgetInsights(budgetManager.addToast);

  const {
    budgetItemsContext,
    overBudgetItems,
    isAISuggesting,
    budgetTotals,
    setBudgetItemsContext,
    suggestBudgetItemByAI,
    plannerCtx,
    addToast,
  } = budgetManager;

  const {
    showBudgetSolutionModal,
    budgetSolutionFor,
    budgetAISolutions,
    isFetchingBudgetSolutions,
    handleAskEllaForSolutions,
    closeBudgetSolutionModal,
  } = budgetInsights;

  const {
    showAISuggestionConfirmModal,
    aiSuggestedItemForReview,
    handleReviewSuggestion,
    handleConfirmAISuggestion,
    handleDismissAISuggestion,
    closeAISuggestionModal,
  } = useBudgetAISuggestion(setBudgetItemsContext, addToast);

  const {
    items,
    showForm,
    editingItem,
    formState,
    setFormState,
    formErrors,
    handleInputChange,
    handleSubmit,
    handleEdit,
    handleDelete,
    handleAddNew,
    handleCancel,
  } = useCrudManager<BudgetItemWithCrud, BudgetFormState>({
    initialItems: budgetItemsContext as BudgetItemWithCrud[],
    initialFormState: initialBudgetFormState,
    itemValidator: budgetValidator,
    crudSetItems: (newItems) => {
      const itemsToSet = typeof newItems === 'function' ? newItems(budgetItemsContext as BudgetItemWithCrud[]) : newItems;
      setBudgetItemsContext(itemsToSet);
    },
    itemTypeForToast: "Budget Item",
  });

  const handleAISuggestion = async () => {
    await suggestBudgetItemByAI(
      () => { }, // onProactiveAdd - already handled in the hook
      (item: BudgetFormState) => { // onBalancedSuggestion
        setFormState(item);
        if (editingItem) handleCancel();
        if (!showForm) handleAddNew();
      },
      handleReviewSuggestion // onReviewSuggestion
    );
  };

  const handleAskForSolutions = (item: any) => {
    if (
      !validateBudgetSolutionRequest(
        plannerCtx?.currentWedding?.weddingDetails,
        addToast as (message: string, type: string) => void
      )
    ) {
      return;
    }
    handleAskEllaForSolutions(
      item,
      budgetItemsContext as BudgetItemType[],
      plannerCtx!.currentWedding!.weddingDetails,
      plannerCtx!.appMode
    );
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner text="Loading budget details..." />
      </div>
    );
  }

  const clientCoupleNames = plannerCtx.currentWedding.weddingDetails.coupleNames;
  const autonomyButtonText = getAutonomyButtonText(plannerCtx.ellaAutonomyLevel);
  const hasWeddingDetails = !!plannerCtx.currentWedding.weddingDetails;

  return (
    <div className="space-y-6">
      <PageTitle
        title={`${clientCoupleNames}'s Budget Manager`}
        subtitle="Track client expenses and stay on top of their wedding finances. Ella can provide insights!"
        icon={<CurrencyDollarIcon />}
      />

      <BudgetPageContent
        clientCoupleNames={clientCoupleNames}
        overBudgetItems={overBudgetItems}
        budgetTotals={budgetTotals}
        items={items}
        showForm={showForm}
        editingItem={editingItem}
        formState={formState}
        formErrors={formErrors}
        isAISuggesting={isAISuggesting}
        autonomyButtonText={autonomyButtonText}
        hasWeddingDetails={hasWeddingDetails}
        isFetchingBudgetSolutions={isFetchingBudgetSolutions}
        budgetSolutionFor={budgetSolutionFor}
        onAddNew={handleAddNew}
        onAISuggestion={handleAISuggestion}
        onAskForSolutions={handleAskForSolutions}
        handleInputChange={handleInputChange}
        handleSubmit={handleSubmit}
        handleCancel={handleCancel}
        handleEdit={handleEdit}
        handleDelete={handleDelete}
      />

      <BudgetSolutionModal
        isOpen={showBudgetSolutionModal}
        onClose={closeBudgetSolutionModal}
        budgetSolutionFor={budgetSolutionFor}
        clientCoupleNames={clientCoupleNames}
        isFetchingBudgetSolutions={isFetchingBudgetSolutions}
        budgetAISolutions={budgetAISolutions}
      />

      <AISuggestionModal
        isOpen={showAISuggestionConfirmModal}
        onClose={closeAISuggestionModal}
        aiSuggestedItem={aiSuggestedItemForReview}
        clientCoupleNames={clientCoupleNames}
        onConfirm={handleConfirmAISuggestion}
        onDismiss={handleDismissAISuggestion}
      />
    </div>
  );
};

export default BudgetPage;
