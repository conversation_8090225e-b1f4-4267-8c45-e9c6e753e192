import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import Textarea from '@/src/components/common/Textarea';
import { ClipboardDocumentIcon as DocumentTextIcon, ClipboardDocumentIcon as FileIcon, LinkIcon, PlusIcon, TrashIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { DataSource, DataSourceType } from '@/src/types/index';
import React, { ChangeEvent, FormEvent, useContext, useState } from 'react';

type AddDataSourceFormState = {
  name: string;
  type: DataSourceType;
  sourceUrl: string; // For URL type
  file: File | null; // For file uploads
  textContent: string; // For text input type
};

const initialFormState: AddDataSourceFormState = {
  name: '',
  type: 'text_input',
  sourceUrl: '',
  file: null,
  textContent: '',
};

const MAX_TXT_FILE_SIZE_MB = 5;
const MAX_TXT_FILE_SIZE_BYTES = MAX_TXT_FILE_SIZE_MB * 1024 * 1024;

const DataSourcesPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();
  const [formState, setFormState] = useState<AddDataSourceFormState>(initialFormState);
  const [formErrors, setFormErrors] = useState<Partial<Record<keyof AddDataSourceFormState, string>>>({});
  const [isAdding, setIsAdding] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);


  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormState(prev => ({ ...prev, [name]: value }));
    if (formErrors[name as keyof AddDataSourceFormState]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
    if (name === 'type') { // Reset other fields when type changes
      setFormState(prev => ({ ...prev, sourceUrl: '', file: null, textContent: '', type: value as DataSourceType }));
      setPreviewUrl(null);
      const fileInput = document.getElementById('dataSourceFile') as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    }
  };

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    setFormErrors(prev => ({ ...prev, file: undefined }));
    setPreviewUrl(null);
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (formState.type === 'txt' && file.size > MAX_TXT_FILE_SIZE_BYTES) {
        setFormErrors(prev => ({ ...prev, file: `TXT file is too large. Max ${MAX_TXT_FILE_SIZE_MB}MB.` }));
        addToast(`TXT file too large (max ${MAX_TXT_FILE_SIZE_MB}MB).`, "error");
        setFormState(prev => ({ ...prev, file: null }));
        e.target.value = ""; // Reset file input
        return;
      }
      if (formState.type === 'pdf' && file.type !== 'application/pdf') {
        setFormErrors(prev => ({ ...prev, file: `Invalid file type. Please select a PDF.` }));
        addToast("Invalid file type. Please select a PDF.", "error");
        setFormState(prev => ({ ...prev, file: null }));
        e.target.value = "";
        return;
      }
      if (formState.type === 'csv' && !(file.type === 'text/csv' || file.name.endsWith('.csv'))) {
        setFormErrors(prev => ({ ...prev, file: `Invalid file type. Please select a CSV.` }));
        addToast("Invalid file type. Please select a CSV.", "error");
        setFormState(prev => ({ ...prev, file: null }));
        e.target.value = "";
        return;
      }

      setFormState(prev => ({ ...prev, file }));

      // Preview for TXT files
      if (formState.type === 'txt' && file.type === 'text/plain') {
        const reader = new FileReader();
        reader.onloadend = () => { setPreviewUrl(reader.result as string); };
        reader.readAsText(file.slice(0, 2000)); // Preview first 2000 chars
      }

    } else {
      setFormState(prev => ({ ...prev, file: null }));
    }
  };

  const validateForm = (): boolean => {
    const errors: Partial<Record<keyof AddDataSourceFormState, string>> = {};
    if (!formState.name.trim()) errors.name = "Data source name is required.";
    if (formState.type === 'url' && !formState.sourceUrl.trim()) {
      errors.sourceUrl = "URL is required for URL type.";
    } else if (formState.type === 'url' && !/^https?:\/\/.+/.test(formState.sourceUrl.trim())) {
      errors.sourceUrl = "Invalid URL format. Must start with http:// or https://";
    }
    if ((formState.type === 'txt' || formState.type === 'pdf' || formState.type === 'csv') && !formState.file) {
      errors.file = "File is required for this type.";
    }
    if (formState.type === 'text_input' && !formState.textContent.trim()) {
      errors.textContent = "Text content is required.";
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!plannerCtx || !validateForm()) return;
    setIsAdding(true);
    try {
      const dataSourcePayload: Omit<DataSource, 'id' | 'uploadDate' | 'status'> = {
        name: formState.name.trim(),
        type: formState.type,
      };

      if (formState.type === 'url') {
        dataSourcePayload.sourceUrl = formState.sourceUrl.trim();
      } else if (formState.type === 'text_input') {
        dataSourcePayload.fullContent = formState.textContent.trim();
        dataSourcePayload.contentPreview = formState.textContent.trim().substring(0, 200) + (formState.textContent.trim().length > 200 ? '...' : '');
      } else if (formState.file) {
        dataSourcePayload.originalFileName = formState.file.name;
        dataSourcePayload.size = formState.file.size;
        if (formState.type === 'txt') {
          // For TXT, read and store content if small enough
          const fileContent = await formState.file.text();
          dataSourcePayload.fullContent = fileContent;
          dataSourcePayload.contentPreview = fileContent.substring(0, 200) + (fileContent.length > 200 ? '...' : '');
        }
        // For PDF/CSV, we're conceptually "uploading". Backend would handle actual storage/processing.
        // No fullContent stored on client for PDF/CSV here.
      }
      await plannerCtx.addDataSource(dataSourcePayload);
      setFormState(initialFormState);
      setPreviewUrl(null);
      const fileInput = document.getElementById('dataSourceFile') as HTMLInputElement;
      if (fileInput) fileInput.value = "";
      // Toast is handled by addDataSource in context
    } catch (error) {
      console.error("Error adding data source:", error);
      addToast("Failed to add data source.", "error");
    } finally {
      setIsAdding(false);
    }
  };

  const handleDeleteDataSource = (id: string) => {
    if (window.confirm("Are you sure you want to delete this data source? This will also remove it from any connected AI agents.")) {
      plannerCtx?.deleteDataSource(id);
      // Toast is handled by deleteDataSource in context
    }
  };

  if (!plannerCtx) {
    return <LoadingSpinner text="Loading Data Sources..." />;
  }

  const { dataSources } = plannerCtx;

  const DATA_SOURCE_TYPES: { value: DataSourceType; label: string; icon: React.FC<React.SVGProps<SVGSVGElement>> }[] = [
    { value: 'text_input', label: 'Manual Text Input', icon: DocumentTextIcon },
    { value: 'url', label: 'Website URL', icon: LinkIcon },
    { value: 'txt', label: 'Text File (.txt)', icon: FileIcon },
    { value: 'pdf', label: 'PDF Document (.pdf)', icon: FileIcon },
    { value: 'csv', label: 'CSV File (.csv)', icon: FileIcon },
  ];

  return (
    <div className="space-y-6">
      <PageTitle title="Manage Data Sources" subtitle="Add and manage documents, URLs, or text to provide context to your AI agents." icon={<DocumentTextIcon />} />

      <Card title="Add New Data Source">
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Data Source Name"
            name="name"
            value={formState.name}
            onChange={handleInputChange}
            placeholder="e.g., Client's Venue Contract, Preferred Vendor List"
            error={formErrors.name}
            required
          />
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-muted-foreground mb-1.5">Type</label>
            <select
              id="type"
              name="type"
              value={formState.type}
              onChange={handleInputChange}
              className="w-full px-3 py-2 rounded-md bg-input border border-input-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
            >
              {DATA_SOURCE_TYPES.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
            </select>
          </div>

          {formState.type === 'url' && (
            <Input label="Source URL" name="sourceUrl" type="url" value={formState.sourceUrl} onChange={handleInputChange} placeholder="https://example.com/document.pdf" error={formErrors.sourceUrl} />
          )}
          {(formState.type === 'txt' || formState.type === 'pdf' || formState.type === 'csv') && (
            <div>
              <label htmlFor="dataSourceFile" className="block text-sm font-medium text-muted-foreground mb-1.5">
                Upload {formState.type.toUpperCase()} File
              </label>
              <input
                type="file"
                id="dataSourceFile"
                accept={formState.type === 'txt' ? '.txt' : formState.type === 'pdf' ? '.pdf' : '.csv'}
                onChange={handleFileChange}
                className="block w-full text-sm text-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90 file:cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:border-ring"
              />
              {formErrors.file && <p className="mt-1.5 text-xs text-destructive">{formErrors.file}</p>}
              {previewUrl && formState.type === 'txt' && !formErrors.file && (
                <Card title="TXT File Preview (first 2000 chars)" className="mt-3 !bg-secondary/30 !p-3">
                  <pre className="whitespace-pre-wrap text-xs text-muted-foreground max-h-32 overflow-y-auto custom-scrollbar">{previewUrl}</pre>
                </Card>
              )}
            </div>
          )}
          {formState.type === 'text_input' && (
            <Textarea label="Paste Text Content" name="textContent" value={formState.textContent} onChange={handleInputChange} rows={6} placeholder="Paste your text content here..." error={formErrors.textContent} />
          )}
          <Button type="submit" isLoading={isAdding} disabled={isAdding} leftIcon={<PlusIcon className="w-5 h-5" />}>
            {isAdding ? 'Adding Source...' : 'Add Data Source'}
          </Button>
        </form>
      </Card>

      <Card title="Available Data Sources">
        {dataSources.length === 0 ? (
          <p className="text-muted-foreground text-center py-8">No data sources added yet.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-secondary/50">
                <tr>
                  {['Name', 'Type', 'Preview / Info', 'Added', 'Status', 'Actions'].map(h => (
                    <th key={h} scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">{h}</th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {dataSources.map(ds => (
                  <tr key={ds.id} className="hover:bg-accent/50 transition-colors">
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-foreground">{ds.name}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">
                      <span className="inline-flex items-center capitalize">
                        {React.createElement(DATA_SOURCE_TYPES.find(opt => opt.value === ds.type)?.icon || FileIcon, { className: 'w-4 h-4 mr-1.5' })}
                        {ds.type}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-muted-foreground max-w-xs truncate">
                      {ds.type === 'url' ? <a href={ds.sourceUrl} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">{ds.sourceUrl}</a>
                        : ds.contentPreview || ds.originalFileName || '-'}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{new Date(ds.uploadDate).toLocaleDateString()}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <span className={`px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${ds.status === 'Ready' ? 'bg-positive/20 text-positive' : 'bg-warning/20 text-warning-foreground'}`}>
                        {ds.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium space-x-2">
                      <Button size="sm" variant="ghost" onClick={() => handleDeleteDataSource(ds.id)} className="!p-1 text-destructive hover:text-destructive/80" title="Delete Data Source">
                        <TrashIcon className="w-4 h-4" />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
};

export default DataSourcesPage;
