import Card from '@/src/components/common/Card';
import PageTitle from '@/src/components/common/PageTitle';
import DashboardSorting from '@/src/components/feature/dashboard/DashboardSorting';
import EmptyDashboard from '@/src/components/feature/dashboard/EmptyDashboard';
import WeddingCard from '@/src/components/feature/dashboard/WeddingCard';
import { BriefcaseIcon } from '@/src/components/icons/HeroIcons';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import {
  SortOption,
  getBudgetStatus,
  getNextCriticalTask,
  getTaskProgress,
  sortWeddings
} from '@/src/utils/dashboardUtils';
import { useRouter } from 'next/navigation';
import React, { useContext, useMemo, useState } from 'react';

const PlannerDashboardPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const router = useRouter();
  const [sortOption, setSortOption] = useState<SortOption>('createdAt-desc');

  const sortedWeddings = useMemo(() => {
    const managedWeddings = plannerCtx?.managedWeddings || [];
    return sortWeddings(managedWeddings, sortOption);
  }, [plannerCtx?.managedWeddings, sortOption]);

  if (!plannerCtx) {
    return <div className="text-center p-10">Loading planner data...</div>;
  }

  const { managedWeddings, setCurrentWeddingId, deleteManagedWedding } = plannerCtx;

  const handleSelectWedding = (weddingId: string) => {
    setCurrentWeddingId(weddingId);
    router.push(`/wedding/${weddingId}/overview`);
  };

  const handleDeleteWedding = (e: React.MouseEvent, weddingId: string) => {
    e.stopPropagation();
    if (window.confirm("Are you sure you want to delete this client's wedding data? This action cannot be undone.")) {
      deleteManagedWedding(weddingId);
    }
  };

  const handleEditWedding = (e: React.MouseEvent, weddingId: string) => {
    e.stopPropagation();
    router.push(`/add-wedding-client?edit=${weddingId}`);
  };

  return (
    <div className="space-y-8">
      <PageTitle
        title="Planner Dashboard"
        subtitle="Welcome! Manage all your client weddings from here."
        icon={<BriefcaseIcon />}
      />

      <Card>
        <DashboardSorting
          weddingCount={managedWeddings.length}
          sortOption={sortOption}
          onSortChange={setSortOption}
        />

        {sortedWeddings.length === 0 ? (
          <EmptyDashboard />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedWeddings.map((wedding) => {
              const budget = getBudgetStatus(wedding);
              const tasks = getTaskProgress(wedding);
              const criticalTask = getNextCriticalTask(wedding);

              return (
                <WeddingCard
                  key={wedding.id}
                  wedding={wedding}
                  budget={budget}
                  tasks={tasks}
                  criticalTask={criticalTask}
                  onSelectWedding={handleSelectWedding}
                  onEditWedding={handleEditWedding}
                  onDeleteWedding={handleDeleteWedding}
                />
              );
            })}
          </div>
        )}
      </Card>
    </div>
  );
};

export default PlannerDashboardPage;
