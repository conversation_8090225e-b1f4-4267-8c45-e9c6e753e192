import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import PageTitle from '@/src/components/common/PageTitle';
import EnvironmentStatus from '@/src/components/EnvironmentStatus';
import { CogIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { AppMode, EllaAutonomyLevel } from '@/src/types/index';
import React, { useContext, useState } from 'react';

const SettingsPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();

  const [notifications, setNotifications] = useState({
    email: true,
    inApp: true,
    taskReminders: 'daily',
  });

  const handleAutonomyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (plannerCtx) {
      plannerCtx.setEllaAutonomyLevel(e.target.value as EllaAutonomyLevel);
      // Toast is handled in PlannerContext for this change
    }
  };

  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      setNotifications(prev => ({ ...prev, [name]: (e.target as HTMLInputElement).checked }));
    } else {
      setNotifications(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleResetPlatformData = () => {
    if (window.confirm("Are you sure you want to reset ALL platform data? This will clear your planner profile, ALL client wedding data, and take you back to the initial setup. This action is irreversible.")) {
      if (plannerCtx) {
        plannerCtx.setIsPlannerOnboarded(false);
        plannerCtx.setPlannerProfile(null);
        localStorage.removeItem('sayyes_managed_weddings');
        localStorage.removeItem('sayyes_current_wedding_id');
        localStorage.removeItem('sayyes_planner_profile');
        localStorage.removeItem('sayyes_ella_autonomy_level');

        addToast("Platform data reset. Please refresh the application.", "warning", 7000);
        setTimeout(() => {
          window.location.hash = "/dashboard";
          window.location.reload();
        }, 1500);
      } else {
        addToast("Error: Could not reset data. Context not available.", "error");
      }
    } else {
      addToast("Platform data reset cancelled.", "info");
    }
  };

  const handleAppModeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (plannerCtx) {
      const newMode = e.target.value as AppMode;
      plannerCtx.setAppMode(newMode);
      // Toast is handled in PlannerContext for this change
    }
  };

  if (!plannerCtx) {
    return <div className="text-muted-foreground">Loading settings...</div>;
  }


  return (
    <div className="space-y-6">
      <PageTitle title="Global Settings" subtitle="Customize your SayYes Planner AI experience and manage global preferences." icon={<CogIcon />} />

      <EnvironmentStatus />

      <Card title="Application Mode">
        <div>
          <label htmlFor="appModeSelect" className="block text-sm font-medium text-muted-foreground mb-1.5">Current Application Mode</label>
          <select
            id="appModeSelect"
            name="appModeSelect"
            value={plannerCtx.appMode}
            onChange={handleAppModeChange}
            className="w-full max-w-md px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
          >
            <option value="dev">Development Mode (Uses Mock Data & AI Stubs)</option>
            <option value="prod">Production Mode (Uses Real AI - API Key Required)</option>
          </select>
          <p className="text-xs text-muted-foreground/80 mt-1.5">
            Development mode helps test UI and flow. Production mode enables full AI capabilities. Switching modes will reset AI-populated data for all client weddings to reflect the new mode.
          </p>
        </div>
      </Card>

      <Card title="Gemini API Key Configuration">
        <div className="space-y-4">
          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex items-start space-x-2">
                <div className="text-blue-600 mt-0.5">
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-blue-800">Personal API Key Override</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Configure your personal Gemini API key to override environment variables. This key takes priority over server-configured keys and enables AI features even if server keys are missing.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <input
                type="password"
                value={plannerCtx.geminiApiKey || ''}
                onChange={(e) => plannerCtx.setGeminiApiKey(e.target.value)}
                placeholder="Enter your Gemini API key..."
                className="w-full px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
              />
              <p className="text-xs text-muted-foreground/80">
                Get your API key from{' '}
                <a
                  href="https://makersuite.google.com/app/apikey"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:underline"
                >
                  Google AI Studio
                </a>.
                {plannerCtx.geminiApiKey ? ' Your personal key is configured and will be used for AI features.' : ' No personal key configured - using environment variables if available.'}
              </p>
            </div>

            {plannerCtx.geminiApiKey && (
              <div className="flex space-x-2">
                <button
                  onClick={() => plannerCtx.setGeminiApiKey('')}
                  className="px-3 py-2 text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90"
                >
                  Clear Key
                </button>
              </div>
            )}
          </div>
        </div>
      </Card>

      <Card title="Ella AI Configuration (Global)">
        <div className="space-y-4">
          <div>
            <label htmlFor="ellaAutonomy" className="block text-sm font-medium text-muted-foreground mb-1.5">Ella's Default Autonomy Level</label>
            <select
              id="ellaAutonomy"
              name="ellaAutonomy"
              value={plannerCtx.ellaAutonomyLevel}
              onChange={handleAutonomyChange}
              className="w-full max-w-md px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
            >
              <option value="review_all">Require Review for Most Actions</option>
              <option value="balanced">Balanced (User Review for Critical Decisions - Default)</option>
              <option value="proactive">More Independent for Routine Tasks</option>
            </select>
            <p className="text-xs text-muted-foreground/80 mt-1.5">Set the default behavior for Ella across new client weddings. This impacts how AI suggestions are presented and integrated.</p>
          </div>
        </div>
      </Card>

      <Card title="Notification Preferences (Global - Mock)">
        <div className="space-y-4">
          <div className="flex items-center">
            <input type="checkbox" id="emailNotifications" name="email" checked={notifications.email} onChange={handleNotificationChange} className="h-5 w-5 text-primary bg-input border-border rounded focus:ring-primary mr-2" />
            <label htmlFor="emailNotifications" className="text-sm text-muted-foreground">Receive Email Notifications (Global)</label>
          </div>
          <div className="flex items-center">
            <input type="checkbox" id="inAppNotifications" name="inApp" checked={notifications.inApp} onChange={handleNotificationChange} className="h-5 w-5 text-primary bg-input border-border rounded focus:ring-primary mr-2" />
            <label htmlFor="inAppNotifications" className="text-sm text-muted-foreground">Enable In-App Alerts (Global)</label>
          </div>
          <div>
            <label htmlFor="taskReminders" className="block text-sm font-medium text-muted-foreground mb-1.5">Default Task Reminder Frequency for New Clients</label>
            <select
              id="taskReminders"
              name="taskReminders"
              value={notifications.taskReminders}
              onChange={handleNotificationChange}
              className="w-full max-w-xs px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="critical_only">Critical Only</option>
              <option value="off">Off</option>
            </select>
          </div>
          <Button onClick={() => addToast('Global notification settings saved (mock)!', 'success')} variant="primary">Save Global Notification Settings</Button>
        </div>
      </Card>

      <Card title="Platform Data Management">
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">Manage your global application data.</p>
          <Button onClick={handleResetPlatformData} variant="danger">
            Reset All Platform Data
          </Button>
          <p className="text-xs text-muted-foreground/80 mt-1.5">Warning: This action is irreversible and will clear your planner profile and ALL client wedding data from this browser.</p>
        </div>
      </Card>
    </div>
  );
};

export default SettingsPage;
