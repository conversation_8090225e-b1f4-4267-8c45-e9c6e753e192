import Button from '@/src/components/common/Button';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import AnimatedAIIndicator from '@/src/components/feature/onboarding/AnimatedAIIndicator';
import ErrorCard from '@/src/components/feature/onboarding/ErrorCard';
import OnboardingForm from '@/src/components/feature/onboarding/OnboardingForm';
import VisionSnapshotCard from '@/src/components/feature/onboarding/VisionSnapshotCard';
import SayYesLogo from '@/src/components/icons/SayYesLogo';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { useEditMode } from '@/src/hooks/useEditMode';
import { useOnboardingForm } from '@/src/hooks/useOnboardingForm';
import { useVisionSnapshot } from '@/src/hooks/useVisionSnapshot';
import { convertFormDataToWeddingDetails } from '@/src/utils/onboardingUtils';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useContext, useState } from 'react';

const AddWeddingClientPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const searchParams = useSearchParams();
  const editWeddingId = searchParams.get('edit');
  const router = useRouter();
  const { addToast } = useToast();

  const [isLoading, setIsLoading] = useState(false);

  const editMode = useEditMode(
    editWeddingId,
    plannerCtx || null,
    (message: string, type: string) => addToast(message, type as any),
    (formData) => onboardingForm.setFormData(formData)
  );

  const onboardingForm = useOnboardingForm(editMode.isEditMode);
  const visionSnapshot = useVisionSnapshot();

  const {
    formData,
    formErrors,
    handleInputChange,
    validateForm,
  } = onboardingForm;

  const {
    visionSnapshotMessage,
    showPortalButton,
    generateSnapshot,
    createUpdateConfirmationMessage,
    clearVisionSnapshot,
  } = visionSnapshot;

  const {
    isEditMode,
    editError,
    isLoadingEdit,
    validateEditWeddingExists,
  } = editMode;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm((message: string, type: string) => addToast(message, type as any)) || !plannerCtx) return;

    setIsLoading(true);
    clearVisionSnapshot();

    const detailsToSubmit = convertFormDataToWeddingDetails(formData);

    try {
      if (isEditMode && editWeddingId) {
        if (!validateEditWeddingExists(editWeddingId)) {
          setIsLoading(false);
          return;
        }

        plannerCtx.updateManagedWedding(editWeddingId, { weddingDetails: detailsToSubmit });
        addToast(`${detailsToSubmit.coupleNames}'s wedding details updated!`, "success");
        createUpdateConfirmationMessage(detailsToSubmit.coupleNames);
        plannerCtx.setCurrentWeddingId(editWeddingId);
      } else {
        await generateSnapshot(detailsToSubmit, plannerCtx.appMode, (message: string, type: string) => addToast(message, type as any));
        const newWedding = plannerCtx.addManagedWedding({ weddingDetails: detailsToSubmit });
        addToast(`New client wedding for ${newWedding.weddingDetails.coupleNames} added!`, "success");
        plannerCtx.setCurrentWeddingId(newWedding.id);
      }
    } catch (error) {
      console.error("Error during add/edit wedding client submission:", error);
      addToast(`Error ${isEditMode ? 'updating' : 'processing'} client details.`, "error");
    } finally {
      setIsLoading(false);
    }
  };

  const completeAndGoToWedding = () => {
    if (plannerCtx?.currentWeddingId) {
      router.push(`/wedding/${plannerCtx.currentWeddingId}/overview`);
    } else if (editWeddingId) {
      router.push(`/wedding/${editWeddingId}/overview`);
    } else {
      router.push('/dashboard');
    }
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  if (!plannerCtx) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <LoadingSpinner text="Initializing SayYes Planner AI..." color="text-primary" />
      </div>
    );
  }

  if (isLoadingEdit && !visionSnapshotMessage && !editError) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <LoadingSpinner
          text={isEditMode ? "Loading client details..." : "Setting up client portal..."}
          color="text-primary"
        />
      </div>
    );
  }

  if (editError) {
    return <ErrorCard errorMessage={editError} onBackToDashboard={handleBackToDashboard} />;
  }

  return (
    <div className="min-h-screen bg-animated-gradient flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 overflow-hidden">
      <div className="mb-8 flex flex-col items-center text-center onboarding-welcome-animate">
        <SayYesLogo className="w-16 h-16 sm:w-20 sm:h-20" />
        <h1 className="text-4xl sm:text-5xl font-bold text-foreground mt-5">
          {isEditMode ? "Edit Wedding Client Details" : "Add New Wedding Client"}
        </h1>
        {!isEditMode && <AnimatedAIIndicator />}
        <p className="text-muted-foreground mt-3 max-w-lg text-base sm:text-lg">
          {isEditMode
            ? `Update the details for ${formData.coupleNames || 'this client'}.`
            : "Let's get some basic details for your new clients. Ella will then help you kickstart their planning!"}
        </p>
      </div>

      {isLoading && visionSnapshotMessage === null && (
        <LoadingSpinner
          text={isEditMode ? "Updating client details..." : "Ella is preparing the vision snapshot for your client..."}
          color="text-primary"
        />
      )}

      {!isLoading && visionSnapshotMessage && (
        <VisionSnapshotCard
          visionSnapshotMessage={visionSnapshotMessage.text}
          isEditMode={isEditMode}
          showPortalButton={showPortalButton}
          onGoToPortal={completeAndGoToWedding}
          onTryAgain={clearVisionSnapshot}
        />
      )}

      {!isLoading && !visionSnapshotMessage && (
        <OnboardingForm
          formData={formData}
          formErrors={formErrors}
          isEditMode={isEditMode}
          onInputChange={handleInputChange}
          onSubmit={handleSubmit}
        />
      )}

      {!visionSnapshotMessage && !editError && (
        <p className="text-xs text-muted-foreground/70 mt-8 text-center max-w-xl">
          {isEditMode
            ? "Updating client information will reflect across their dedicated wedding portal."
            : "Client information is used to personalize their wedding plan and will be stored locally in your browser with your planner data."}
        </p>
      )}

      <Button onClick={handleBackToDashboard} variant="ghost" className="mt-8">
        Back to Dashboard
      </Button>
    </div>
  );
};

export default AddWeddingClientPage;
