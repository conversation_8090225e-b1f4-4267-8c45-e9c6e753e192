import PageTitle from '@/src/components/common/PageTitle';
import VendorGrid from '@/src/components/feature/vendors/VendorGrid';
import VendorSearchForm from '@/src/components/feature/vendors/VendorSearchForm';
import { BuildingStorefrontIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { DatabaseVendor, VendorSearchParams, VendorsService } from '@/src/services/vendorsService';
import { VendorCategory } from '@/src/types/index';
import React, { useCallback, useEffect, useState } from 'react';

const VendorDirectoryPage: React.FC = () => {
    const { addToast } = useToast();
    const [vendors, setVendors] = useState<DatabaseVendor[]>([]);
    const [loading, setLoading] = useState(false);
    const [searchParams, setSearchParams] = useState<VendorSearchParams>({
        search: '',
        city: '',
        state: '',
        limit: 20,
        offset: 0
    });
    const [selectedCategory, setSelectedCategory] = useState<VendorCategory>('Venue');
    const [availableCategories, setAvailableCategories] = useState<{ category: string; count: number }[]>([]);

    const loadAvailableCategories = useCallback(async () => {
        try {
            const categories = await VendorsService.getAvailableCategories();
            setAvailableCategories(categories);
        } catch (error) {
            console.error('Error loading categories:', error);
            addToast('Error loading vendor categories', 'error');
        }
    }, [addToast]);

    const searchVendors = useCallback(async () => {
        setLoading(true);
        try {
            const results = await VendorsService.searchVendorsByCategory(selectedCategory, searchParams);
            setVendors(results);
        } catch (error) {
            console.error('Error searching vendors:', error);
            addToast('Error searching vendors', 'error');
            setVendors([]);
        } finally {
            setLoading(false);
        }
    }, [selectedCategory, searchParams, addToast]);

    useEffect(() => {
        loadAvailableCategories();
    }, [loadAvailableCategories]);

    useEffect(() => {
        searchVendors();
    }, [selectedCategory, searchParams.city, searchParams.state, searchVendors]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        searchVendors();
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setSearchParams(prev => ({ ...prev, [name]: value }));
    };

    const handleCategoryChange = (category: VendorCategory) => {
        setSelectedCategory(category);
    };

    const handleAddToWeddingPlan = (vendor: DatabaseVendor) => {
        // TODO: Add vendor to wedding plan
        addToast(`Added ${vendor.business_name} to your vendor considerations`, 'success');
    };

    const handleLoadMore = () => {
        setSearchParams(prev => ({
            ...prev,
            offset: (prev.offset || 0) + (prev.limit || 20)
        }));
        searchVendors();
    };

    return (
        <div className="space-y-6">
            <PageTitle
                title="Vendor Directory"
                subtitle="Discover and connect with local wedding vendors"
                icon={<BuildingStorefrontIcon />}
            />

            <VendorSearchForm
                searchParams={searchParams}
                selectedCategory={selectedCategory}
                availableCategories={availableCategories}
                loading={loading}
                onSearch={handleSearch}
                onInputChange={handleInputChange}
                onCategoryChange={handleCategoryChange}
            />

            <VendorGrid
                vendors={vendors}
                selectedCategory={selectedCategory}
                loading={loading}
                searchParams={searchParams}
                onAddToWeddingPlan={handleAddToWeddingPlan}
                onLoadMore={handleLoadMore}
            />
        </div>
    );
};

export default VendorDirectoryPage; 