// src/pages/styling.tsx
import OnboardingTooltip from '@/src/components/OnboardingTooltip';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import React, { useState } from 'react';

import AgentChatBubble from '@/src/components/AgentChatBubble';

import Spinner from '@/src/components/Spinner';

export default function StylingPage() {
  const [outfit, setOutfit] = useState({ name: '', type: '' });
  const { runAgenticAction, loading, error, result } = useAgenticAction();

  const handleAddOutfit = async (e: React.FormEvent) => {
    e.preventDefault();
    await runAgenticAction('add-outfit', 'styling', { clientId: 'CURRENT_CLIENT_ID', outfit });
    setOutfit({ name: '', type: '' });
  };

  return (
    <div>
      <OnboardingTooltip content="This action is handled by <PERSON>, your AI wedding planner agent. She coordinates specialized agents to update your live data in real time!">
        <h2>Add Outfit</h2>
      </OnboardingTooltip>
      <form onSubmit={handleAddOutfit}>
        <input type="text" placeholder="Outfit Name" value={outfit.name} onChange={e => setOutfit({ ...outfit, name: e.target.value })} required />
        <input type="text" placeholder="Type" value={outfit.type} onChange={e => setOutfit({ ...outfit, type: e.target.value })} required />
        <button type="submit" disabled={loading}>Add Outfit</button>
      </form>
      {loading && <AgentChatBubble message="Ella is working on your request..." />}

      {loading && <Spinner />}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {result && <p style={{ color: 'green' }}>Outfit added!</p>}
    </div>
  );
}
