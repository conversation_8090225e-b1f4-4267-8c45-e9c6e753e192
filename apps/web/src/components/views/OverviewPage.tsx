import Card from '@/src/components/common/Card';
import CountdownTimer from '@/src/components/common/CountdownTimer';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import OverviewChatInterface from '@/src/components/feature/overview/OverviewChatInterface';
import PriorityTasksOverview from '@/src/components/feature/overview/PriorityTasksOverview';
import VisionBoardOverview from '@/src/components/feature/overview/VisionBoardOverview';
import { HomeIcon } from '@/src/components/icons/HeroIcons';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { getPriorityTasks, getVisionBoardSummary } from '@/src/utils/overviewUtils';
import Link from 'next/link';
import React, { useContext } from 'react';

const OverviewPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner text="Loading wedding details..." />
      </div>
    );
  }

  const { currentWedding } = plannerCtx;
  const { weddingDetails, tasks: weddingTasks, visionBoardItems } = currentWedding;

  const visionBoardSummary = getVisionBoardSummary(visionBoardItems, weddingDetails);
  const priorityTasksData = getPriorityTasks(weddingTasks);

  return (
    <div className="space-y-8">
      <PageTitle
        title={`Portal for: ${weddingDetails.coupleNames}`}
        subtitle={`Managing their ${weddingDetails.vibe || 'special'} wedding. Use this hub for all planning activities.`}
        icon={<HomeIcon />}
      />

      {weddingDetails.weddingDate && (
        <Card title="Wedding Countdown">
          <CountdownTimer targetDate={weddingDetails.weddingDate} />
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <VisionBoardOverview
          weddingId={currentWedding.id}
          visionBoardSummary={visionBoardSummary}
          weddingVibe={weddingDetails.vibe || 'special'}
        />

        <PriorityTasksOverview
          weddingId={currentWedding.id}
          priorityTasksData={priorityTasksData}
        />

        <OverviewChatInterface
          coupleNames={weddingDetails.coupleNames}
        />
      </div>

      <Card title="What&apos;s Next for This Client?">
        <p className="text-muted-foreground">
          Ella is constantly working behind the scenes. Check the{' '}
          <Link
            href={`/wedding/${currentWedding.id}/ai-agent-system`}
            className="text-primary hover:underline"
          >
            AI Agent System
          </Link>{' '}
          for this wedding&apos;s AI activity. Ready to dive deeper? Explore the sections in the sidebar for {weddingDetails.coupleNames}.
        </p>
      </Card>
    </div>
  );
};

export default OverviewPage;
