import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import { PencilIcon, PlusIcon, SparklesIcon, TrashIcon, UsersIcon, XCircleIcon } from '@/src/components/icons/HeroIcons';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { CrudItem, useCrudManager } from '@/src/hooks/useCrudManager';
import { ellaSuggestGuest } from '@/src/services/ai/index';
import { Guest } from '@/src/types/index';
import { generateClientSideId } from '@/src/utils/idUtils';
import React, { useContext, useEffect, useMemo, useState } from 'react';

interface GuestItem extends Guest, CrudItem { }

type GuestFormState = Omit<Guest, 'id'>;

const initialGuestFormState: GuestFormState = {
  name: '',
  rsvpStatus: 'Pending',
  dietaryRestrictions: '',
  plusOne: false,
  plusOneName: '',
  group: '',
};

const GuestsPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();
  const [isAISuggesting, setIsAISuggesting] = useState(false);

  const guestValidator = (formState: GuestFormState) => {
    const errors: Record<string, string> = {};
    if (!formState.name.trim()) errors.name = "Guest name is required.";
    return { isValid: Object.keys(errors).length === 0, errors };
  };

  const contextGuests = plannerCtx?.currentWedding?.guests || [];
  const setContextGuests = (newItems: GuestItem[] | ((prevItems: GuestItem[]) => GuestItem[])) => {
    if (plannerCtx && plannerCtx.currentWeddingId) {
      const finalItems = typeof newItems === 'function' ? newItems(plannerCtx.currentWedding?.guests || []) : newItems;
      plannerCtx.updateDataForCurrentWedding('guests', finalItems);
    }
  };

  const {
    items: _guests,
    showForm,
    editingItem,
    formState,
    setFormState,
    formErrors,
    handleInputChange,
    handleSubmit,
    handleEdit,
    handleDelete,
    handleAddNew,
    handleCancel,
  } = useCrudManager<GuestItem, GuestFormState>({
    initialItems: contextGuests,
    initialFormState: initialGuestFormState,
    itemValidator: guestValidator,
    crudSetItems: setContextGuests,
    itemTypeForToast: "Guest",
  });

  useEffect(() => {
    if (plannerCtx?.currentWedding && plannerCtx.appMode === 'dev' && plannerCtx.currentWedding.guests.length === 0 && !plannerCtx.currentWedding.isAIPopulated) {
      const devMockGuests: GuestItem[] = [
        { id: 'g-dev1', name: "Olivia Miller", rsvpStatus: 'Accepted', dietaryRestrictions: 'Vegan', plusOne: true, plusOneName: 'Ethan Jones', group: "Client's College Friends" },
        { id: 'g-dev2', name: "Benjamin Davis", rsvpStatus: 'Pending', group: "Client's Family", plusOne: false, plusOneName: '', dietaryRestrictions: 'None' },
        { id: 'g-dev3', name: "Chloe Garcia", rsvpStatus: 'Accepted', dietaryRestrictions: 'None', plusOne: false, plusOneName: '', group: "Client's Work Colleagues" },
        { id: 'g-dev4', name: "Liam Rodriguez", rsvpStatus: 'Declined', group: "Client's Childhood Friends", plusOne: false, plusOneName: '', dietaryRestrictions: 'Peanut Allergy' },
      ];
      if (plannerCtx.currentWeddingId) {
        plannerCtx.updateDataForCurrentWedding('guests', devMockGuests);
        addToast("Loaded placeholder guest list for client.", "info", 3000);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [plannerCtx?.currentWedding?.id]); // Rerun if current wedding changes


  const rsvpStatusOptions: Guest['rsvpStatus'][] = ['Pending', 'Accepted', 'Declined'];

  const handleGuestInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    handleInputChange(e);
    if (e.target.name === 'plusOne' && !(e.target as HTMLInputElement).checked) {
      setFormState(prev => ({ ...prev, plusOneName: '' }));
    }
  };

  const customHandleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(e);
  };

  const suggestGuestByAI = async () => {
    if (!plannerCtx || !plannerCtx.currentWedding || !plannerCtx.currentWedding.weddingDetails) {
      addToast("Client's wedding details are not available for AI suggestion.", "error");
      return;
    }
    setIsAISuggesting(true);
    addToast("Ella is thinking of a guest suggestion...", "info");
    try {
      const suggestion = await ellaSuggestGuest(plannerCtx.currentWedding.guests, plannerCtx.currentWedding.weddingDetails, plannerCtx.appMode);
      if (suggestion.name && suggestion.group) {
        const newGuest: GuestItem = {
          ...initialGuestFormState,
          id: generateClientSideId(),
          name: `${suggestion.name} (AI Suggested for Client)`,
          group: suggestion.group,
        };
        setContextGuests(prev => [...prev, newGuest]);
        addToast(`Ella suggested adding a guest to client's group: "${suggestion.group}"`, "success");
      } else {
        addToast("Ella couldn't come up with a new guest suggestion for this client right now.", "info");
      }
    } catch (error) {
      console.error("Error suggesting guest:", error);
      addToast("Error suggesting guest by AI.", "error");
    } finally {
      setIsAISuggesting(false);
    }
  };

  const getRSVPBadgeColor = (status: Guest['rsvpStatus']) => {
    switch (status) {
      case 'Accepted': return 'bg-positive/20 text-positive';
      case 'Declined': return 'bg-destructive/20 text-destructive';
      case 'Pending': return 'bg-warning/20 text-warning-foreground';
      default: return 'bg-muted/20 text-muted-foreground';
    }
  };

  const guestStats = useMemo(() => {
    const currentGuests = plannerCtx?.currentWedding?.guests || [];
    const accepted = currentGuests.filter(g => g.rsvpStatus === 'Accepted').length;
    const plusOnesAccepted = currentGuests.filter(g => g.rsvpStatus === 'Accepted' && g.plusOne).length;
    const declined = currentGuests.filter(g => g.rsvpStatus === 'Declined').length;
    const pending = currentGuests.filter(g => g.rsvpStatus === 'Pending').length;
    return { totalInvited: currentGuests.length, accepted: accepted + plusOnesAccepted, declined, pending };
  }, [plannerCtx?.currentWedding?.guests]);

  const handleInviteToWeddingParty = (guestName: string) => {
    addToast(`Inviting ${guestName} to wedding party - Functionality coming soon!`, "info");
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return <div className="flex items-center justify-center h-screen"><LoadingSpinner text="Loading guest list..." /></div>;
  }
  const clientCoupleNames = plannerCtx.currentWedding.weddingDetails.coupleNames;

  return (
    <div className="space-y-6">
      <PageTitle title={`${clientCoupleNames}'s Guest Management`} subtitle="Organize client's guest list, track RSVPs, and manage special requests." icon={<UsersIcon />} />

      <Card title="Client Guest Summary">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div><p className="text-sm text-muted-foreground">Invited (Guests)</p><p className="text-2xl font-bold text-primary">{guestStats.totalInvited}</p></div>
          <div><p className="text-sm text-muted-foreground">Attending (+1s)</p><p className="text-2xl font-bold text-positive">{guestStats.accepted}</p></div>
          <div><p className="text-sm text-muted-foreground">Declined</p><p className="text-2xl font-bold text-destructive">{guestStats.declined}</p></div>
          <div><p className="text-sm text-muted-foreground">Pending RSVP</p><p className="text-2xl font-bold text-warning-foreground">{guestStats.pending}</p></div>
        </div>
      </Card>

      <div className="flex space-x-3">
        {!showForm && (<Button onClick={handleAddNew} leftIcon={<PlusIcon className="w-5 h-5" />} variant="primary">Add New Guest for Client</Button>)}
        <Button onClick={suggestGuestByAI} leftIcon={<SparklesIcon className="w-5 h-5" />} variant="secondary" isLoading={isAISuggesting} disabled={isAISuggesting || !plannerCtx?.currentWedding?.weddingDetails}>
          {isAISuggesting ? "Ella is Thinking..." : "Ella Suggests Guest for Client"}
        </Button>
      </div>

      {showForm && (
        <Card title={editingItem ? "Edit Client's Guest" : "Add New Guest for Client"}>
          <form onSubmit={customHandleSubmit} className="space-y-4">
            <Input label="Full Name" name="name" value={formState.name} onChange={handleGuestInputChange} placeholder="e.g., John Doe" error={formErrors?.name} required />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="rsvpStatus" className="block text-sm font-medium text-muted-foreground mb-1.5">RSVP Status</label>
                <select id="rsvpStatus" name="rsvpStatus" value={formState.rsvpStatus} onChange={handleGuestInputChange} className="w-full px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary">
                  {rsvpStatusOptions.map(status => <option key={status} value={status}>{status}</option>)}
                </select>
              </div>
              <Input label="Group (e.g., Client's Family)" name="group" value={formState.group || ''} onChange={handleGuestInputChange} placeholder="Optional" />
            </div>
            <Input label="Dietary Restrictions / Notes" name="dietaryRestrictions" value={formState.dietaryRestrictions || ''} onChange={handleGuestInputChange} placeholder="e.g., Vegetarian, Nut Allergy" />
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <input type="checkbox" id="plusOne" name="plusOne" checked={formState.plusOne || false} onChange={handleGuestInputChange} className="h-5 w-5 text-primary bg-input border-border rounded focus:ring-primary mr-2" />
                <label htmlFor="plusOne" className="text-sm text-muted-foreground">Bringing a Plus One?</label>
              </div>
              {formState.plusOne && (<Input label="Plus One Name" name="plusOneName" value={formState.plusOneName || ''} onChange={handleGuestInputChange} placeholder="Name of Plus One" className="flex-grow" />)}
            </div>
            <div className="flex space-x-3 pt-2">
              <Button type="submit" variant="primary">{editingItem ? 'Update Guest' : 'Save Guest'}</Button>
              <Button type="button" onClick={handleCancel} variant="ghost" leftIcon={<XCircleIcon className="w-5 h-5" />}>Cancel</Button>
            </div>
          </form>
        </Card>
      )}

      <Card title="Client's Guest List">
        {(plannerCtx.currentWedding.guests || []).length === 0 && !isAISuggesting ? (
          <p className="text-muted-foreground text-center py-8">Client&apos;s guest list is empty.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-secondary/50">
                <tr>{['Name', 'RSVP', 'Dietary', '+1', 'Group', 'Wedding Party', 'Actions'].map(h => <th key={h} scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">{h}</th>)}</tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {(plannerCtx.currentWedding.guests || []).map((guest) => (
                  <tr key={guest.id} className="hover:bg-accent/50 transition-colors">
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-foreground">{guest.name}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm"><span className={`px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${getRSVPBadgeColor(guest.rsvpStatus)}`}>{guest.rsvpStatus}</span></td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground truncate max-w-xs">{guest.dietaryRestrictions || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{guest.plusOne ? `Yes (${guest.plusOneName || 'N/A'})` : 'No'}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{guest.group || '-'}</td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleInviteToWeddingParty(guest.name)}
                        className="!p-1 text-primary hover:text-primary/80"
                        title="Invite to Wedding Party"
                        aria-label={`Invite ${guest.name} to wedding party`}
                      >
                        <PlusIcon className="w-4 h-4" /> <span className="ml-1 hidden sm:inline">Invite</span>
                      </Button>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium space-x-2">
                      <Button size="sm" variant="ghost" onClick={() => handleEdit(guest as GuestItem)} className="!p-1 text-primary hover:text-primary/80" title="Edit Guest"><PencilIcon className="w-4 h-4" /></Button>
                      <Button size="sm" variant="ghost" onClick={() => handleDelete(guest.id)} className="!p-1 text-destructive hover:text-destructive/80" title="Delete Guest"><TrashIcon className="w-4 h-4" /></Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </div>
  );
};

export default GuestsPage;
