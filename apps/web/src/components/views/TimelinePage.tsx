import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import Modal from '@/src/components/common/Modal';
import PageTitle from '@/src/components/common/PageTitle';
import Textarea from '@/src/components/common/Textarea';
import TaskBreakdownModal from '@/src/components/feature/timeline/TaskBreakdownModal';
import TaskItem from '@/src/components/feature/timeline/TaskItem';
import {
  CalendarDaysIcon,
  PlusIcon,
  SparklesIcon,
  XCircleIcon
} from '@/src/components/icons/HeroIcons';
import { CrudItem, useCrudManager } from '@/src/hooks/useCrudManager';
import { useTaskBreakdown } from '@/src/hooks/useTaskBreakdown';
import { TaskFormState, initialTaskFormState, useTaskManager } from '@/src/hooks/useTaskManager';
import { TaskFilterOption, TaskItem as TaskItemType, taskValidator } from '@/src/utils/taskUtils';
import { FC, Fragment } from 'react';

interface TaskItemWithCrud extends TaskItemType, CrudItem { }

const TimelinePage: FC = () => {
  const taskManager = useTaskManager();

  const {
    contextTasks,
    filteredContextTasks,
    sortedContextTasks,
    groupedTopLevelTasks,
    groupOrder,
    taskFilter,
    isAISuggesting,
    coupleNamesArray,
    todayForComparison,
    setTaskFilter,
    toggleTaskCompletion,
    suggestTaskByAI,
    setContextTasks,
    plannerCtx,
    addToast,
  } = taskManager;

  const {
    items: _tasks,
    showForm: showTaskForm,
    editingItem: editingTask,
    formState: taskFormState,
    setFormState: setTaskFormState,
    formErrors: taskFormErrors,
    handleInputChange: handleTaskInputChange,
    handleSubmit: handleTaskSubmit,
    handleEdit: handleEditTask,
    handleDelete: handleDeleteTask,
    handleAddNew: handleAddNewTask,
    handleCancel: handleCancelTaskForm,
  } = useCrudManager<TaskItemWithCrud, TaskFormState>({
    initialItems: contextTasks,
    initialFormState: initialTaskFormState,
    itemValidator: taskValidator,
    crudSetItems: setContextTasks,
    itemTypeForToast: "Task",
  });

  const taskBreakdown = useTaskBreakdown(setContextTasks, addToast);

  const {
    showBreakdownModal,
    selectedTaskForBreakdown,
    suggestedSubTasks,
    selectedSubTasksToAdd,
    parentTaskUpdateSuggestion,
    isBreakingDownTask,
    handleOpenBreakdownModal,
    handleSubTaskCheckboxChange,
    handleAddSelectedSubTasks,
    handleUpdateParentTask,
    closeBreakdownModal,
  } = taskBreakdown;

  const handleOpenBreakdown = (task: TaskItemType) => {
    if (!plannerCtx?.currentWedding?.weddingDetails) {
      addToast("Client's wedding details are not available for task breakdown.", "error");
      return;
    }
    handleOpenBreakdownModal(
      task,
      plannerCtx.currentWedding.weddingDetails,
      plannerCtx.appMode
    );
  };

  if (!plannerCtx || !plannerCtx.currentWedding) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner text="Loading timeline..." />
      </div>
    );
  }

  const clientCoupleNames = plannerCtx.currentWedding.weddingDetails.coupleNames;
  const filterOptions: { label: string; value: TaskFilterOption }[] = [
    { label: 'All Client Tasks', value: 'all' },
    { label: 'Planner-Assigned', value: 'planner' },
    { label: 'Planner-Internal', value: 'internal' },
  ];

  return (
    <div className="space-y-6">
      <PageTitle
        title={`${clientCoupleNames}'s Wedding Timeline`}
        subtitle="Client's personalized checklist to keep everything on track. Filter tasks for your workflow."
        icon={<CalendarDaysIcon />}
      />

      <div className="flex flex-wrap gap-3 items-center">
        {!showTaskForm && (
          <Button
            onClick={handleAddNewTask}
            leftIcon={<PlusIcon className="w-5 h-5" />}
            variant="primary"
          >
            Add New Task for Client
          </Button>
        )}
        <Button
          onClick={suggestTaskByAI}
          leftIcon={<SparklesIcon className="w-5 h-5" />}
          variant="secondary"
          isLoading={isAISuggesting}
          disabled={isAISuggesting || !plannerCtx?.currentWedding?.weddingDetails}
        >
          {isAISuggesting ? "Ella is Thinking..." : "Ella Suggests Task for Client"}
        </Button>
        <div className="ml-auto flex gap-2 items-center">
          <span className="text-sm text-muted-foreground">Filter:</span>
          {filterOptions.map(opt => (
            <Button
              key={opt.value}
              size="sm"
              variant={taskFilter === opt.value ? 'primary' : 'ghost'}
              onClick={() => setTaskFilter(opt.value)}
              className={taskFilter === opt.value ? 'shadow-md' : ''}
            >
              {opt.label}
            </Button>
          ))}
        </div>
      </div>

      {showTaskForm && (
        <Modal
          isOpen={showTaskForm}
          onClose={handleCancelTaskForm}
          title={editingTask ? "Edit Client Task" : "Add New Client Task"}
          size="lg"
        >
          <form onSubmit={handleTaskSubmit} className="space-y-4">
            <Input
              label="Task Title"
              name="title"
              value={taskFormState.title}
              onChange={handleTaskInputChange}
              required
              error={taskFormErrors?.title}
            />
            <Textarea
              label="Description"
              name="description"
              value={taskFormState.description || ''}
              onChange={handleTaskInputChange}
              rows={3}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Due Date"
                name="dueDate"
                type="date"
                value={taskFormState.dueDate || ''}
                onChange={handleTaskInputChange}
              />
              <div>
                <Input
                  label="Assigned To"
                  name="assignedTo"
                  value={taskFormState.assignedTo || ''}
                  onChange={handleTaskInputChange}
                  placeholder="Planner, Client Name, Couple"
                />
                {coupleNamesArray.length > 0 && (
                  <div className="mt-2 flex flex-wrap gap-2">
                    {coupleNamesArray.map(name => (
                      <Button
                        key={name}
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={() => setTaskFormState(prev => ({ ...prev, assignedTo: name }))}
                        className="!text-xs !py-1 !px-2 border border-border hover:bg-accent"
                      >
                        {name}
                      </Button>
                    ))}
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => setTaskFormState(prev => ({ ...prev, assignedTo: "Couple" }))}
                      className="!text-xs !py-1 !px-2 border border-border hover:bg-accent"
                    >
                      Couple
                    </Button>
                    <Button
                      type="button"
                      size="sm"
                      variant="ghost"
                      onClick={() => setTaskFormState(prev => ({ ...prev, assignedTo: "Planner" }))}
                      className="!text-xs !py-1 !px-2 border border-border hover:bg-accent"
                    >
                      Planner
                    </Button>
                  </div>
                )}
              </div>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPlannerInternal"
                name="isPlannerInternal"
                checked={taskFormState.isPlannerInternal || false}
                onChange={handleTaskInputChange}
                className="h-4 w-4 text-primary bg-input border-border rounded focus:ring-primary mr-2"
              />
              <label htmlFor="isPlannerInternal" className="text-sm text-muted-foreground">
                Mark as Planner-Internal Task (hidden from client view)
              </label>
            </div>
            <div className="flex space-x-3 pt-2">
              <Button type="submit" variant="primary">
                {editingTask ? 'Update Task' : 'Save Task'}
              </Button>
              <Button
                type="button"
                onClick={handleCancelTaskForm}
                variant="ghost"
                leftIcon={<XCircleIcon className="w-5 h-5" />}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Modal>
      )}

      {groupOrder.map(groupName => {
        const tasksInGroup = groupedTopLevelTasks[groupName];
        if (!tasksInGroup || tasksInGroup.length === 0) return null;
        return (
          <Card
            key={groupName}
            title={groupName}
            className={`${groupName === "Completed" ? "opacity-70" : ""} ${groupName === "Past Due" ? "!border-destructive/50 shadow-destructive/20" : ""}`}
          >
            <ul className="space-y-3">
              {tasksInGroup.map(topLvlTask => {
                const subtasks = sortedContextTasks.filter(st => st.parentId === topLvlTask.id);
                return (
                  <Fragment key={topLvlTask.id}>
                    <TaskItem
                      task={topLvlTask}
                      isSubTask={false}
                      todayForComparison={todayForComparison}
                      onToggleCompletion={toggleTaskCompletion}
                      onEdit={handleEditTask}
                      onDelete={handleDeleteTask}
                      onBreakdown={handleOpenBreakdown}
                    />
                    {subtasks.length > 0 && (
                      <ul className="space-y-2 mt-2">
                        {subtasks.map(subT => (
                          <TaskItem
                            key={subT.id}
                            task={subT}
                            isSubTask={true}
                            todayForComparison={todayForComparison}
                            onToggleCompletion={toggleTaskCompletion}
                            onEdit={handleEditTask}
                            onDelete={handleDeleteTask}
                          />
                        ))}
                      </ul>
                    )}
                  </Fragment>
                );
              })}
            </ul>
          </Card>
        );
      })}

      {(filteredContextTasks || []).length === 0 && !isAISuggesting && !showTaskForm && (
        <Card>
          <p className="text-muted-foreground text-center py-8">
            {taskFilter === 'all' && "Client's timeline is empty (non-internal tasks)."}
            {taskFilter === 'planner' && "No tasks assigned to 'Planner' for this client (non-internal tasks)."}
            {taskFilter === 'internal' && "No planner-internal tasks found for this client."}
          </p>
        </Card>
      )}

      <TaskBreakdownModal
        isOpen={showBreakdownModal}
        onClose={closeBreakdownModal}
        selectedTask={selectedTaskForBreakdown}
        clientCoupleNames={clientCoupleNames}
        isBreakingDown={isBreakingDownTask}
        suggestedSubTasks={suggestedSubTasks}
        selectedSubTasksToAdd={selectedSubTasksToAdd}
        parentTaskUpdateSuggestion={parentTaskUpdateSuggestion}
        onSubTaskCheckboxChange={handleSubTaskCheckboxChange}
        onAddSelectedSubTasks={handleAddSelectedSubTasks}
        onUpdateParentTask={handleUpdateParentTask}
      />
    </div>
  );
};

export default TimelinePage;