// src/pages/timeline.tsx
import OnboardingTooltip from '@/src/components/OnboardingTooltip';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import React, { useState } from 'react';

import AgentChatBubble from '@/src/components/AgentChatBubble';


export default function TimelinePage() {
  const [milestone, setMilestone] = useState({ name: '', date: '' });
  const { runAgenticAction, loading, error, result } = useAgenticAction();

  const handleAddMilestone = async (e: React.FormEvent) => {
    e.preventDefault();
    await runAgenticAction('add-milestone', 'timeline', { clientId: 'CURRENT_CLIENT_ID', milestone });
    setMilestone({ name: '', date: '' });
  };

  return (
    <div>
      <OnboardingTooltip content="This action is handled by <PERSON>, your AI wedding planner agent. She coordinates specialized agents to update your live data in real time!">
        <h2>Add Milestone</h2>
      </OnboardingTooltip>
      <form onSubmit={handleAddMilestone}>
        <input
          type="text"
          placeholder="Milestone Name"
          value={milestone.name}
          onChange={e => setMilestone({ ...milestone, name: e.target.value })}
          required
          aria-label="Milestone Name"
        />
        <input
          type="date"
          placeholder="Date"
          value={milestone.date}
          onChange={e => setMilestone({ ...milestone, date: e.target.value })}
          required
          aria-label="Milestone Date"
        />
        <button type="submit" disabled={loading}>Add Milestone</button>
      </form>
      {loading && <AgentChatBubble message="Ella is working on your request..." />}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {result && <p style={{ color: 'green' }}>Milestone added!</p>}
    </div>
  );
}
