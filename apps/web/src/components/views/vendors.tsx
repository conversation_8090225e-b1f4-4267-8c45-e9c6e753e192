// src/pages/vendors.tsx
import OnboardingTooltip from '@/src/components/OnboardingTooltip';
import { useAgenticAction } from '@/src/hooks/useAgenticAction';
import React, { useState } from 'react';

import AgentChatBubble from '@/src/components/AgentChatBubble';

import Spinner from '@/src/components/Spinner';

export default function VendorsPage() {
  const [vendor, setVendor] = useState({ name: '', contact: '' });
  const { runAgenticAction, loading, error, result } = useAgenticAction();

  const handleAddVendor = async (e: React.FormEvent) => {
    e.preventDefault();
    await runAgenticAction('add-vendor', 'vendor', { clientId: 'CURRENT_CLIENT_ID', vendor });
    setVendor({ name: '', contact: '' });
  };

  return (
    <div>
      <OnboardingTooltip content="This action is handled by <PERSON>, your AI wedding planner agent. She coordinates specialized agents to update your live data in real time!">
        <h2>Add Vendor</h2>
      </OnboardingTooltip>
      <form onSubmit={handleAddVendor}>
        <input
          type="text"
          placeholder="Vendor Name"
          value={vendor.name}
          onChange={e => setVendor({ ...vendor, name: e.target.value })}
          required
        />
        <input
          type="text"
          placeholder="Contact Info"
          value={vendor.contact}
          onChange={e => setVendor({ ...vendor, contact: e.target.value })}
          required
        />
        <button type="submit" disabled={loading}>Add Vendor</button>
      </form>
      {loading && <AgentChatBubble message="Ella is working on your request..." />}

      {loading && <Spinner />}
      {error && <p style={{ color: 'red' }}>{error}</p>}
      {result && <p style={{ color: 'green' }}>Vendor added!</p>}
    </div>
  );
}
