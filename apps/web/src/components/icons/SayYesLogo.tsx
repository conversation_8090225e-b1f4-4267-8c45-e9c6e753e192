/**
 * SayYesLogo component for the application.
 * @param props - The props of the SayYesLogo component.
 * @returns The SayYesLogo component.
 */
import { FC, SVGProps } from 'react';

const SayYesLogo: FC<SVGProps<SVGSVGElement>> = (props) => (
  <svg width="48" height="48" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg" {...props}>
    <defs>
      <linearGradient id="sayYesLogoGradientUniqueApp" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style={{ stopColor: 'hsl(271, 76%, 53%)', stopOpacity: 1 }} />
        <stop offset="100%" style={{ stopColor: 'hsl(271, 76%, 65%)', stopOpacity: 1 }} />
      </linearGradient>
    </defs>
    {/* Solid circle background with gradient */}
    <circle cx="50" cy="50" r="45" fill="url(#sayYesLogoGradientUniqueApp)" />
    {/* Larger, bolder 'SY' text, centered, with contrasting color */}
    <text
      x="50"
      y="50"
      textAnchor="middle"
      dy=".3em" /* Vertical alignment adjustment */
      fontSize="40" /* Increased font size */
      fontWeight="800" /* Bolder font weight */
      fill="hsl(var(--color-primary-foreground))"
      fontFamily="Poppins, sans-serif" /* Apply Poppins if available, otherwise sans-serif */
    >
      SY
    </text>
  </svg>
);

export default SayYesLogo;