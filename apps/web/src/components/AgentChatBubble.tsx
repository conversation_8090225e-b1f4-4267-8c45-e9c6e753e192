// src/components/AgentChatBubble.tsx
import AgentAvatar from './AgentAvatar';

interface AgentChatBubbleProps {
  message: string;
  name?: string;
  src?: string;
}

export default function AgentChatBubble({
  message,
  name = 'Ella',
  src = '/ella-avatar.png'
}: AgentChatBubbleProps) {
  return (
    <div className="flex items-start space-x-2 my-2">
      <AgentAvatar name={name} src={src} />
      <div className="bg-primary/10 rounded-lg px-4 py-2 text-sm max-w-xs">
        {message}
      </div>
    </div>
  );
}
