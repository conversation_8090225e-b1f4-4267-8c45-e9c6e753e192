import React, { forwardRef } from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  containerClassName?: string;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({ label, id, error, icon, className = '', type = 'text', containerClassName = '', ...props }, ref) => {
  return (
    <div className={`w-full ${containerClassName}`}>
      {label && <label htmlFor={id} className="block text-sm font-medium text-muted-foreground mb-1.5">{label}</label>}
      <div className="relative">
        {icon && <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-muted-foreground">{React.cloneElement(icon as React.ReactElement<{ className?: string }>, { className: 'h-5 w-5' })}</div>}
        <input
          ref={ref}
          id={id}
          type={type}
          className={`
            block w-full h-10 px-3 py-2 rounded-md
            bg-input border border-input-border
            text-foreground placeholder:text-muted-foreground
            focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0 focus-visible:border-input-focus-border
            disabled:cursor-not-allowed disabled:opacity-50
            sm:text-sm
            transition-colors duration-200
            ${icon ? 'pl-10' : 'px-3'}
            ${error ? 'border-destructive focus-visible:border-destructive focus-visible:ring-destructive' : ''}
            ${className}
          `}
          {...props}
        />
      </div>
      {error && <p className="mt-1.5 text-xs text-destructive">{error}</p>}
    </div>
  );
});

Input.displayName = 'Input';
export default Input;