import { XCircleIcon } from '@/src/components/icons/HeroIcons';
import React, { useEffect } from 'react';
import Card from './Card';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl'; // Added more sizes
  footer?: React.ReactNode; // Optional footer content
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md', footer }) => {
  useEffect(() => {
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) {
    return null;
  }

  const sizeClasses = {
    sm: 'max-w-sm', // 24rem
    md: 'max-w-md', // 28rem
    lg: 'max-w-lg', // 32rem
    xl: 'max-w-xl', // 36rem
    '2xl': 'max-w-2xl', // 42rem
    '3xl': 'max-w-3xl', // 48rem
    '4xl': 'max-w-4xl', // 56rem
  };

  return (
    <div
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
      className="fixed inset-0 z-50 flex items-center justify-center p-4 animate-fadeIn" // Use z-50 for higher stack order
    >
      <div
        className="absolute inset-0 bg-background/80 backdrop-blur-sm transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        aria-hidden="true"
      ></div>

      <Card className={`relative z-50 w-full ${sizeClasses[size]} !shadow-xl flex flex-col max-h-[90vh] animate-slideInUp !p-0`}> {/* Removed default Card padding */}
        <div className="flex items-center justify-between p-5 sm:p-6 border-b border-border">
          <h3 id="modal-title" className="text-lg font-semibold text-card-foreground">
            {title}
          </h3>
          <button
            onClick={onClose}
            aria-label="Close modal"
            className="text-muted-foreground hover:text-foreground transition-colors p-1 rounded-full hover:bg-accent -mr-2" // Adjusted for better click target
          >
            <XCircleIcon className="w-6 h-6" />
          </button>
        </div>
        <div className="overflow-y-auto flex-grow custom-scrollbar p-5 sm:p-6">
          {children}
        </div>
        {footer && (
          <div className="p-5 sm:p-6 border-t border-border bg-muted/30">
            {footer}
          </div>
        )}
      </Card>
    </div>
  );
};

export default Modal;