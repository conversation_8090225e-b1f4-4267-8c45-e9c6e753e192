/**
 * SliderInput component for the application.
 * @param label - The label of the SliderInput component.
 * @param id - The id of the SliderInput component.
 * @param min - The minimum value of the SliderInput component.
 * @param max - The maximum value of the SliderInput component.
 * @param step - The step value of the SliderInput component.
 * @param value - The value of the SliderInput component.
 * @param onChange - The onChange event of the SliderInput component.
 */

interface SliderInputProps {
  label: string;
  id: string;
  min: number;
  max: number;
  step: number;
  value: number;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  unit?: string;
  className?: string;
  error?: string;
}

const SliderInput: React.FC<SliderInputProps> = ({
  label,
  id,
  min,
  max,
  step,
  value,
  onChange,
  unit = '',
  className = '',
  error,
}) => {
  return (
    <div className={`w-full ${className}`}>
      <label htmlFor={id} className="block text-sm font-medium text-muted-foreground mb-2">
        {label}: <span className="font-semibold text-primary">{unit}{value.toLocaleString()}</span>
      </label>
      <input
        type="range"
        id={id}
        name={id}
        min={min}
        max={max}
        step={step}
        value={value}
        onChange={onChange}
        className={`
          w-full h-2 bg-slate-500 rounded-lg appearance-none cursor-pointer
          focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:ring-offset-background
          [&::-webkit-slider-thumb]:appearance-none
          [&::-webkit-slider-thumb]:h-5
          [&::-webkit-slider-thumb]:w-5
          [&::-webkit-slider-thumb]:rounded-full
          [&::-webkit-slider-thumb]:bg-primary
          [&::-webkit-slider-thumb]:cursor-pointer
          [&::-webkit-slider-thumb]:shadow-md
          [&::-webkit-slider-thumb]:hover:bg-primary/80
          [&::-moz-range-thumb]:h-5
          [&::-moz-range-thumb]:w-5
          [&::-moz-range-thumb]:rounded-full
          [&::-moz-range-thumb]:bg-primary
          [&::-moz-range-thumb]:border-none
          [&::-moz-range-thumb]:cursor-pointer
          [&::-moz-range-thumb]:shadow-md
          [&::-moz-range-thumb]:hover:bg-primary/80
          ${error ? 'border border-destructive' : ''}
        `}
      />
      {error && <p className="mt-1.5 text-xs text-destructive">{error}</p>}
    </div>
  );
};

export default SliderInput;
