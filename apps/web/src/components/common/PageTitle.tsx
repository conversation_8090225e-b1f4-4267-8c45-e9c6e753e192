/**
 * PageTitle component for the application.
 * @param title - The title of the PageTitle component.
 * @param subtitle - The subtitle of the PageTitle component.
 * @param icon - The icon of the PageTitle component.
 * @returns The PageTitle component.
 */
import { cloneElement, isValidElement, type FC, type ReactElement, type ReactNode } from 'react';

interface PageTitleProps {
  title: string;
  subtitle?: string;
  icon?: ReactNode;
}

const PageTitle: FC<PageTitleProps> = ({ title, subtitle, icon }) => {
  return (
    <div className="mb-8 pb-4 border-b border-border">
      <div className="flex items-center space-x-3">
        {icon && isValidElement(icon) && (
          <span className="text-primary">
            {cloneElement(icon as ReactElement<{ className?: string }>, { className: "w-7 h-7 sm:w-8 sm:h-8" })}
          </span>
        )}
        <h1 className="text-3xl sm:text-4xl font-bold text-foreground">{title}</h1>
      </div>
      {subtitle && <p className="mt-2 text-sm text-muted-foreground">{subtitle}</p>}
    </div>
  );
};

export default PageTitle;