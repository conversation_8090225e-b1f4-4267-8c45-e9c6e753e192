/**
 * Card component for the application.
 * @param title - The title of the Card component.
 * @param children - The children of the Card component.
 * @param className - The className of the Card component.
 * @param titleClassName - The className of the Card title.
 * @returns The Card component.
 */

interface CardProps {
  title?: string;
  children: React.ReactNode;
  className?: string;
  titleClassName?: string;
}

const Card: React.FC<CardProps> = ({ title, children, className = '', titleClassName = '' }) => {
  return (
    <div
      className={`
        bg-card text-card-foreground
        border border-border 
        rounded-lg 
        p-5 sm:p-6
        shadow-md
        transition-all duration-300 ease-in-out
        hover:border-primary/50
        ${className}
      `}
    >
      {title && (
        <h3 className={`text-xl font-semibold mb-4 text-card-foreground ${titleClassName}`}>
          {title}
        </h3>
      )}
      {children}
    </div>
  );
};

export default Card;