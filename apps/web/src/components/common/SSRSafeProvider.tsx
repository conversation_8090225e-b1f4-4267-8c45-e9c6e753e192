'use client';

import { ReactNode, useEffect, useState } from 'react';

interface SSRSafeProviderProps {
    children: ReactNode;
    fallback?: ReactNode;
}

/**
 * SSRSafeProvider ensures that children are only rendered on the client side
 * to prevent SSR hydration mismatches with context providers
 */
export default function SSRSafeProvider({ children, fallback = null }: SSRSafeProviderProps) {
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    if (!isClient) {
        return <>{fallback}</>;
    }

    return <>{children}</>;
} 