"use client"

import { CheckCircleIcon, ExclamationTriangleIcon, InformationCircleIcon, XCircleIcon as SolidXCircleIcon } from '@/src/components/icons/HeroIcons';
import { ToastMessage } from '@/src/types/ui';
import React, { useEffect } from 'react';

interface ToastProps {
  toast: ToastMessage;
  onDismiss: (id: string) => void;
}

const ICONS: Record<ToastMessage['type'], React.FC<React.SVGProps<SVGSVGElement>>> = {
  success: CheckCircleIcon,
  error: SolidXCircleIcon,
  info: InformationCircleIcon,
  warning: ExclamationTriangleIcon,
};

const BG_COLORS: Record<ToastMessage['type'], string> = {
  success: 'bg-positive/90 border-positive',
  error: 'bg-destructive/90 border-destructive',
  info: 'bg-primary/90 border-primary',
  warning: 'bg-warning/90 border-warning',
};

const TEXT_COLORS: Record<ToastMessage['type'], string> = {
  success: 'text-positive-foreground',
  error: 'text-destructive-foreground',
  info: 'text-primary-foreground',
  warning: 'text-warning-foreground',
}

const Toast: React.FC<ToastProps> = ({ toast, onDismiss }) => {
  const { id, message, type, duration = 5000 } = toast;
  const Icon = toast.icon || ICONS[type];

  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(id);
    }, duration);

    return () => clearTimeout(timer);
  }, [id, duration, onDismiss]);

  return (
    <div
      role="alert"
      aria-live="assertive"
      className={`
        flex items-start p-4 rounded-md shadow-2xl
        border-l-4 w-full max-w-sm transition-all duration-300 ease-in-out
        ${BG_COLORS[type]} ${TEXT_COLORS[type]}
        hover:shadow-glow-primary
      `}
    >
      {Icon && (
        <div className="flex-shrink-0 mr-3 mt-0.5">
          <Icon className="w-6 h-6" />
        </div>
      )}
      <div className="flex-grow">
        <p className="text-sm font-medium">{message}</p>
      </div>
      <div className="ml-3 flex-shrink-0">
        <button
          onClick={() => onDismiss(id)}
          className={`
            -mx-1.5 -my-1.5 p-1.5 rounded-md inline-flex
            focus:outline-none focus:ring-2 focus:ring-offset-2 
            ${type === 'warning' ? 'hover:bg-warning/80 focus:ring-offset-warning focus:ring-warning-foreground' :
              type === 'info' ? `hover:bg-primary/80 focus:ring-offset-primary focus:ring-background` :
                type === 'success' ? `hover:bg-positive/80 focus:ring-offset-positive focus:ring-background` :
                  `hover:bg-destructive/80 focus:ring-offset-destructive focus:ring-background`
            }
            transition ease-in-out duration-150
          `}
          aria-label="Dismiss"
        >
          <SolidXCircleIcon className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default Toast;
