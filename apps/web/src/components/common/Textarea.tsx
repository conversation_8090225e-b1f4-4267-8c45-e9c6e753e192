import { cloneElement, isValidElement } from 'react';

/**
 * Textarea component for the application.
 * @param label - The label of the Textarea component.
 * @param id - The id of the Textarea component.
 * @param error - The error of the Textarea component.
 * @param icon - The icon of the Textarea component.
 * @param containerClassName - The className of the Textarea container.
 */
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  containerClassName?: string;
}

const Textarea: React.FC<TextareaProps> = ({ label, id, error, icon, className = '', containerClassName = '', ...props }) => {
  return (
    <div className={`w-full ${containerClassName}`}>
      {label && <label htmlFor={id} className="block text-sm font-medium text-muted-foreground mb-1.5">{label}</label>}
      <div className="relative">
        {icon && isValidElement(icon) && <div className="absolute top-3 left-0 pl-3 flex items-center pointer-events-none text-muted-foreground">{cloneElement(icon as React.ReactElement<{ className?: string }>, { className: 'h-5 w-5' })}</div>}
        <textarea
          id={id}
          className={`
            block w-full px-3 py-2 rounded-md
            bg-input border border-input-border
            text-foreground placeholder:text-muted-foreground
            focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-0 focus-visible:border-input-focus-border
            disabled:cursor-not-allowed disabled:opacity-50
            sm:text-sm
            transition-colors duration-200
            min-h-[80px]
            ${icon ? 'pl-10' : 'px-3'}
            ${error ? 'border-destructive focus-visible:border-destructive focus-visible:ring-destructive' : ''}
            ${className}
          `}
          {...props}
        />
      </div>
      {error && <p className="mt-1.5 text-xs text-destructive">{error}</p>}
    </div>
  );
};

export default Textarea;