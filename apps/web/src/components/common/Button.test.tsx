import { fireEvent, render } from '@testing-library/react';
import { describe, expect, it } from 'bun:test';
import Button from './Button';

describe('Button Component', () => {
    it('renders with default props', () => {
        const { getByRole } = render(<Button>Click me</Button>);
        const button = getByRole('button', { name: /click me/i });
        expect(button).toBeInTheDocument();
    });

    it('renders with different variants', () => {
        const { getByRole, rerender } = render(<Button variant="primary">Primary</Button>);
        expect(getByRole('button')).toHaveClass('bg-primary');

        rerender(<Button variant="secondary">Secondary</Button>);
        expect(getByRole('button')).toHaveClass('bg-secondary');

        rerender(<Button variant="danger">Danger</Button>);
        expect(getByRole('button')).toHaveClass('bg-destructive');
    });

    it('renders with different sizes', () => {
        const { getByRole, rerender } = render(<Button size="sm">Small</Button>);
        expect(getByRole('button')).toHaveClass('h-9');

        rerender(<Button size="md">Medium</Button>);
        expect(getByRole('button')).toHaveClass('h-10');

        rerender(<Button size="lg">Large</Button>);
        expect(getByRole('button')).toHaveClass('h-11');
    });

    it('shows loading state correctly', () => {
        const { getByRole } = render(<Button isLoading>Loading</Button>);
        const button = getByRole('button');
        expect(button).toBeDisabled();
        expect(button.querySelector('svg')).toBeInTheDocument(); // Loading spinner
    });

    it('renders with left and right icons', () => {
        const leftIcon = <span data-testid="left-icon">👈</span>;
        const rightIcon = <span data-testid="right-icon">👉</span>;

        const { getByTestId } = render(
            <Button leftIcon={leftIcon} rightIcon={rightIcon}>
                With Icons
            </Button>
        );

        expect(getByTestId('left-icon')).toBeInTheDocument();
        expect(getByTestId('right-icon')).toBeInTheDocument();
    });

    it('handles click events', () => {
        const handleClick = () => { };
        let clickCount = 0;

        const { getByRole } = render(<Button onClick={() => { clickCount++; handleClick(); }}>Click me</Button>);

        fireEvent.click(getByRole('button'));
        expect(clickCount).toBe(1);
    });

    it('is disabled when loading', () => {
        let clickCount = 0;
        const handleClick = () => { clickCount++; };

        const { getByRole } = render(<Button isLoading onClick={handleClick}>Loading</Button>);

        const button = getByRole('button');
        expect(button).toBeDisabled();

        fireEvent.click(button);
        expect(clickCount).toBe(0);
    });

    it('applies custom className', () => {
        const { getByRole } = render(<Button className="custom-class">Custom</Button>);
        expect(getByRole('button')).toHaveClass('custom-class');
    });

    it('passes through other HTML attributes', () => {
        const { getByTestId } = render(<Button data-testid="custom-button" aria-label="Custom button">Test</Button>);
        const button = getByTestId('custom-button');
        expect(button).toHaveAttribute('aria-label', 'Custom button');
    });
}); 