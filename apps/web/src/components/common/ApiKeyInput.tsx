import { EyeIcon, EyeSlashIcon } from '@/src/components/icons/HeroIcons';
import React, { useEffect, useState } from 'react';

interface ApiKeyInputProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
    label?: string;
    description?: string;
}

const ApiKeyInput: React.FC<ApiKeyInputProps> = ({
    value,
    onChange,
    placeholder = "Enter your API key...",
    label = "API Key",
    description
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [tempValue, setTempValue] = useState(value);

    useEffect(() => {
        setTempValue(value);
    }, [value]);

    const handleSave = () => {
        onChange(tempValue);
        setIsEditing(false);
        setIsVisible(false);
    };

    const handleCancel = () => {
        setTempValue(value);
        setIsEditing(false);
        setIsVisible(false);
    };

    const handleClear = () => {
        setTempValue('');
        onChange('');
        setIsEditing(false);
        setIsVisible(false);
    };

    const maskKey = (key: string) => {
        if (!key) return '';
        if (key.length <= 8) return '••••••••••••••••';
        return key.substring(0, 4) + '••••••••••••••••' + key.substring(key.length - 4);
    };

    return (
        <div className="space-y-2">
            {label && (
                <label className="block text-sm font-medium text-muted-foreground">
                    {label}
                </label>
            )}

            <div className="flex items-center space-x-2">
                {isEditing ? (
                    <div className="flex-1 flex items-center space-x-2">
                        <div className="relative flex-1">
                            <input
                                type={isVisible ? "text" : "password"}
                                value={tempValue}
                                onChange={(e) => setTempValue(e.target.value)}
                                placeholder={placeholder}
                                className="w-full px-4 py-2.5 pr-10 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                                autoFocus
                            />
                            <button
                                type="button"
                                onClick={() => setIsVisible(!isVisible)}
                                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                            >
                                {isVisible ? (
                                    <EyeSlashIcon className="h-4 w-4" />
                                ) : (
                                    <EyeIcon className="h-4 w-4" />
                                )}
                            </button>
                        </div>
                        <button
                            type="button"
                            onClick={handleSave}
                            className="px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
                        >
                            Save
                        </button>
                        <button
                            type="button"
                            onClick={handleCancel}
                            className="px-3 py-2 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90"
                        >
                            Cancel
                        </button>
                    </div>
                ) : (
                    <div className="flex-1 flex items-center space-x-2">
                        <div className="flex-1 px-4 py-2.5 rounded-md bg-input border border-border text-muted-foreground font-mono">
                            {value ? maskKey(value) : 'No API key configured'}
                        </div>
                        <button
                            type="button"
                            onClick={() => setIsEditing(true)}
                            className="px-3 py-2 text-sm bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/90"
                        >
                            {value ? 'Edit' : 'Add'}
                        </button>
                        {value && (
                            <button
                                type="button"
                                onClick={handleClear}
                                className="px-3 py-2 text-sm bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90"
                            >
                                Clear
                            </button>
                        )}
                    </div>
                )}
            </div>

            {description && (
                <p className="text-xs text-muted-foreground/80">
                    {description}
                </p>
            )}
        </div>
    );
};

export default ApiKeyInput; 