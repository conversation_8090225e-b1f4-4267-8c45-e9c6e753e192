import { generateEllaAvatar, generateUserAvatar } from '@/src/services/ai/index';
import { AppMode } from '@/src/types/index';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

interface DynamicAvatarProps {
    type: 'ella' | 'user';
    alt: string;
    width: number;
    height: number;
    className?: string;
    appMode?: AppMode;
}

const DynamicAvatar: React.FC<DynamicAvatarProps> = ({
    type,
    alt,
    width,
    height,
    className,
    appMode = 'dev'
}) => {
    const [avatarUrl, setAvatarUrl] = useState<string>('');
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const generateAvatar = async () => {
            try {
                setIsLoading(true);
                let url: string;

                if (type === 'ella') {
                    url = await generateEllaAvatar(appMode);
                } else {
                    url = await generateUserAvatar(appMode);
                }

                setAvatarUrl(url);
            } catch (error) {
                console.error(`Error generating ${type} avatar:`, error);
                // Fallback to a simple SVG
                const fallbackColor = type === 'ella' ? '#FF95A0' : '#6366F1';
                const fallbackSvg = `data:image/svg+xml;base64,${btoa(`
          <svg width="${width}" height="${height}" viewBox="0 0 ${width} ${height}" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="${width / 2}" cy="${height / 2}" r="${width / 2}" fill="${fallbackColor}"/>
            <svg width="${width * 0.6}" height="${height * 0.6}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" x="${width * 0.2}" y="${height * 0.2}">
              <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19C3 16.33 8.33 15 12 15C15.67 15 21 16.33 21 19ZM12 13C14.76 13 17 10.76 17 8C17 5.24 14.76 3 12 3C9.24 3 7 5.24 7 8C7 10.76 9.24 13 12 13Z" fill="white"/>
            </svg>
          </svg>
        `)}`;
                setAvatarUrl(fallbackSvg);
            } finally {
                setIsLoading(false);
            }
        };

        generateAvatar();
    }, [type, appMode, width, height]);

    if (isLoading) {
        return (
            <div
                className={`${className} bg-gray-200 animate-pulse rounded-full flex items-center justify-center`}
                style={{ width, height }}
            >
                <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
            </div>
        );
    }

    return (
        <Image
            src={avatarUrl}
            alt={alt}
            width={width}
            height={height}
            className={className}
        />
    );
};

export default DynamicAvatar; 