import { parseDateStringToLocalMidnight } from '@/src/utils/dateUtils';
import React, { useEffect, useState } from 'react';

interface CountdownTimerProps {
  targetDate: string;
}

interface TimeLeft {
  days?: number;
  hours?: number;
  minutes?: number;
  seconds?: number;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({ targetDate }) => {
  const calculateTimeLeft = (): TimeLeft => {
    const targetDateLocal = parseDateStringToLocalMidnight(targetDate);
    if (!targetDateLocal) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }
    const difference = +targetDateLocal - +new Date();
    let timeLeft: TimeLeft = {};

    if (difference > 0) {
      timeLeft = {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / 1000 / 60) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    } else {
      timeLeft = { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }
    return timeLeft;
  };

  const [timeLeft, setTimeLeft] = useState<TimeLeft>(calculateTimeLeft());

  useEffect(() => {
    const timer = setTimeout(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearTimeout(timer);
  });

  const timerComponents: React.JSX.Element[] = [];
  const timeKeys: (keyof TimeLeft)[] = ['days', 'hours', 'minutes', 'seconds'];


  timeKeys.forEach((interval) => {
    const value = timeLeft[interval];
    if (value !== undefined) {
      timerComponents.push(
        <div key={interval} className="flex flex-col items-center mx-2 p-3 bg-secondary rounded-lg shadow-inner min-w-[70px]">
          <span className="text-3xl font-bold text-primary">{String(value).padStart(2, '0')}</span>
          <span className="text-xs uppercase text-muted-foreground">{interval}</span>
        </div>
      );
    }
  });

  const targetDateLocalForCheck = parseDateStringToLocalMidnight(targetDate);
  const isWeddingDayOrPassed = targetDateLocalForCheck ? +targetDateLocalForCheck - +new Date() <= 0 : true;


  return (
    <div className="flex justify-center items-center my-4">
      {timerComponents.length > 0 && !isWeddingDayOrPassed ? timerComponents : <span className="text-xl text-muted-foreground">The big day has arrived or passed!</span>}
    </div>
  );
};

export default CountdownTimer;