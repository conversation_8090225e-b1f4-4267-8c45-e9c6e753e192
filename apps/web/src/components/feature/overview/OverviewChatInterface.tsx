import Card from '@/src/components/common/Card';
import ChatInterface from '@/src/components/feature/chat/ChatInterface';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { askEllaGeneral } from '@/src/services/ai';
import {
    addMessageIfNotExists,
    cleanupDuplicateWelcomeMessages,
    createEllaMessage,
    createErrorMessage,
    createUserMessage,
    createWelcomeMessage,
    hasWelcomeMessage
} from '@/src/utils/chatUtils';
import React, { useContext, useEffect, useState } from 'react';

interface OverviewChatInterfaceProps {
    coupleNames: string;
}

const OverviewChatInterface: React.FC<OverviewChatInterfaceProps> = ({ coupleNames }) => {
    const plannerCtx = useContext(PlannerContext);
    const [isLoadingElla, setIsLoadingElla] = useState(false);

    useEffect(() => {
        if (!plannerCtx?.currentWedding?.id) return;

        const currentWeddingId = plannerCtx.currentWedding.id;
        const chatHistory = plannerCtx.currentWedding.chatHistory || [];

        // First, clean up any existing duplicates
        const cleanedHistory = cleanupDuplicateWelcomeMessages(chatHistory);
        if (cleanedHistory.length !== chatHistory.length) {
            // Update the chat history if we removed duplicates
            plannerCtx.updateDataForCurrentWedding('chatHistory', cleanedHistory);
            return; // Exit early to let the update trigger a re-render
        }

        // Add welcome message only if one doesn't exist
        if (!hasWelcomeMessage(cleanedHistory)) {
            const welcomeMessage = createWelcomeMessage(currentWeddingId, coupleNames);
            const { updated, history } = addMessageIfNotExists(cleanedHistory, welcomeMessage);

            if (updated) {
                plannerCtx.updateDataForCurrentWedding('chatHistory', history);
            }
        }
    }, [plannerCtx?.currentWedding?.id, plannerCtx?.currentWedding?.chatHistory?.length, coupleNames, plannerCtx]);

    const handleSendToElla = async (messageText: string) => {
        if (!plannerCtx || !plannerCtx.currentWedding) return;

        setIsLoadingElla(true);
        const userMessage = createUserMessage(messageText, 'user-overview');
        plannerCtx.addChatMessageToCurrentWedding(userMessage);

        try {
            const response = await askEllaGeneral(
                messageText,
                plannerCtx.currentWedding.weddingDetails,
                plannerCtx.appMode
            );
            const ellaMessage = createEllaMessage(response.text, 'ella-overview');
            plannerCtx.addChatMessageToCurrentWedding(ellaMessage);
        } catch (error) {
            console.error("Error asking Ella:", error);
            const errorMessage = createErrorMessage('error-overview');
            plannerCtx.addChatMessageToCurrentWedding(errorMessage);
        } finally {
            setIsLoadingElla(false);
        }
    };



    if (!plannerCtx?.currentWedding) {
        return null;
    }

    return (
        <Card title="Chat with Ella" className="lg:col-span-1 h-[400px] lg:h-auto flex flex-col">
            <div className="flex-grow overflow-hidden">
                <ChatInterface
                    messages={plannerCtx.currentWedding.chatHistory || []}
                    onSendMessage={handleSendToElla}
                    isLoadingInteraction={isLoadingElla}
                    placeholder={`Ask Ella about ${coupleNames}'s wedding...`}
                    title=""
                    showEllaAvatar={true}
                />
            </div>
        </Card>
    );
};

export default OverviewChatInterface; 