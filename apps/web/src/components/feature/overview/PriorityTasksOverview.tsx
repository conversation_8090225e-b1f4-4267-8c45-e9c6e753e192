import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import Modal from '@/src/components/common/Modal';
import Textarea from '@/src/components/common/Textarea';
import { CheckBadgeIcon, PencilIcon, XCircleIcon } from '@/src/components/icons/HeroIcons';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { CrudItem, useCrudManager } from '@/src/hooks/useCrudManager';
import { TaskFormState, initialTaskFormState } from '@/src/hooks/useTaskManager';
import { parseAndFormatDateStringForDisplay } from '@/src/utils/dateUtils';
import { PriorityTasksData, isTaskOverdue } from '@/src/utils/overviewUtils';
import { TaskItem, taskValidator } from '@/src/utils/taskUtils';
import Link from 'next/link';
import React, { useContext } from 'react';

interface PriorityTasksOverviewProps {
    weddingId: string;
    priorityTasksData: PriorityTasksData;
}

interface TaskItemWithCrud extends TaskItem, CrudItem { }

const PriorityTasksOverview: React.FC<PriorityTasksOverviewProps> = ({
    weddingId,
    priorityTasksData,
}) => {
    const { tasks: priorityTasks, totalCount } = priorityTasksData;
    const plannerCtx = useContext(PlannerContext);

    const contextTasks = plannerCtx?.currentWedding?.tasks || [];

    const setContextTasks = (
        newItems: TaskItem[] | ((prevItems: TaskItem[]) => TaskItem[])
    ) => {
        if (plannerCtx && plannerCtx.currentWeddingId) {
            const finalItems =
                typeof newItems === "function"
                    ? newItems(plannerCtx.currentWedding?.tasks || [])
                    : newItems;
            plannerCtx.updateDataForCurrentWedding("tasks", finalItems);
        }
    };

    const {
        showForm: showTaskForm,
        editingItem: editingTask,
        formState: taskFormState,
        setFormState: setTaskFormState,
        formErrors: taskFormErrors,
        handleInputChange: handleTaskInputChange,
        handleSubmit: handleTaskSubmit,
        handleEdit: handleEditTask,
        handleCancel: handleCancelTaskForm,
    } = useCrudManager<TaskItemWithCrud, TaskFormState>({
        initialItems: contextTasks,
        initialFormState: initialTaskFormState,
        itemValidator: taskValidator,
        crudSetItems: setContextTasks,
        itemTypeForToast: "Task",
    });

    const coupleNamesArray = plannerCtx?.currentWedding?.weddingDetails?.coupleNames
        ? plannerCtx.currentWedding.weddingDetails.coupleNames.split(' & ')
        : [];

    return (
        <>
            <Card title="Client's Priority Tasks" className="lg:col-span-1">
                {totalCount > 0 ? (
                    <ul className="space-y-3">
                        {priorityTasks.slice(0, 3).map(task => {
                            const taskIsOverdue = task.dueDate ? isTaskOverdue(task.dueDate) : false;
                            return (
                                <li key={task.id} className="flex justify-between items-center p-3 bg-secondary rounded-md hover:bg-accent transition-colors cursor-pointer group"
                                    onClick={() => handleEditTask(task as TaskItemWithCrud)}>
                                    <span className="text-sm text-foreground truncate max-w-[70%]">
                                        {task.title}
                                    </span>
                                    <div className="flex items-center gap-2">
                                        <span className={`text-xs whitespace-nowrap ${taskIsOverdue ? 'text-destructive font-semibold' : 'text-primary'
                                            }`}>
                                            {task.dueDate
                                                ? `Due: ${parseAndFormatDateStringForDisplay(task.dueDate)}`
                                                : 'No due date'
                                            }
                                        </span>
                                        <PencilIcon className="w-4 h-4 text-muted-foreground group-hover:text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
                                    </div>
                                </li>
                            );
                        })}
                    </ul>
                ) : (
                    <p className="text-sm text-muted-foreground">
                        No outstanding tasks for this client. Well done!
                    </p>
                )}
                <Link href={`/wedding/${weddingId}/priority-tasks`}>
                    <Button variant="secondary" size="sm" className="w-full mt-4">
                        View All Tasks <CheckBadgeIcon className="w-4 h-4 ml-2" />
                    </Button>
                </Link>
            </Card>

            {/* Task Edit Modal */}
            {showTaskForm && (
                <Modal
                    isOpen={showTaskForm}
                    onClose={handleCancelTaskForm}
                    title={editingTask ? "Edit Task" : "Add New Task"}
                    size="lg"
                >
                    <form onSubmit={handleTaskSubmit} className="space-y-4">
                        <Input
                            label="Task Title"
                            name="title"
                            value={taskFormState.title}
                            onChange={handleTaskInputChange}
                            required
                            error={taskFormErrors?.title}
                        />
                        <Textarea
                            label="Description"
                            name="description"
                            value={taskFormState.description || ''}
                            onChange={handleTaskInputChange}
                            rows={3}
                        />
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <Input
                                label="Due Date"
                                name="dueDate"
                                type="date"
                                value={taskFormState.dueDate || ''}
                                onChange={handleTaskInputChange}
                            />
                            <div>
                                <Input
                                    label="Assigned To"
                                    name="assignedTo"
                                    value={taskFormState.assignedTo || ''}
                                    onChange={handleTaskInputChange}
                                    placeholder="Planner, Client Name, Couple"
                                />
                                {coupleNamesArray.length > 0 && (
                                    <div className="mt-2 flex flex-wrap gap-2">
                                        {coupleNamesArray.map(name => (
                                            <Button
                                                key={name}
                                                type="button"
                                                size="sm"
                                                variant="ghost"
                                                onClick={() => setTaskFormState(prev => ({ ...prev, assignedTo: name }))}
                                                className="!text-xs !py-1 !px-2 border border-border hover:bg-accent"
                                            >
                                                {name}
                                            </Button>
                                        ))}
                                        <Button
                                            type="button"
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => setTaskFormState(prev => ({ ...prev, assignedTo: "Couple" }))}
                                            className="!text-xs !py-1 !px-2 border border-border hover:bg-accent"
                                        >
                                            Couple
                                        </Button>
                                        <Button
                                            type="button"
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => setTaskFormState(prev => ({ ...prev, assignedTo: "Planner" }))}
                                            className="!text-xs !py-1 !px-2 border border-border hover:bg-accent"
                                        >
                                            Planner
                                        </Button>
                                    </div>
                                )}
                            </div>
                        </div>
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="isPlannerInternal"
                                name="isPlannerInternal"
                                checked={taskFormState.isPlannerInternal || false}
                                onChange={handleTaskInputChange}
                                className="h-4 w-4 text-primary bg-input border-border rounded focus:ring-primary mr-2"
                            />
                            <label htmlFor="isPlannerInternal" className="text-sm text-muted-foreground">
                                Mark as Planner-Internal Task (hidden from client view)
                            </label>
                        </div>
                        <div className="flex space-x-3 pt-2">
                            <Button type="submit" variant="primary">
                                {editingTask ? 'Update Task' : 'Save Task'}
                            </Button>
                            <Button
                                type="button"
                                onClick={handleCancelTaskForm}
                                variant="ghost"
                                leftIcon={<XCircleIcon className="w-5 h-5" />}
                            >
                                Cancel
                            </Button>
                        </div>
                    </form>
                </Modal>
            )}
        </>
    );
};

export default PriorityTasksOverview; 