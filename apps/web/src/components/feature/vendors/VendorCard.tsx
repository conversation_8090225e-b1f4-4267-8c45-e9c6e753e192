import Button from '@/src/components/common/Button';
import { EnvelopeIcon, GlobeAltIcon, MapPinIcon, PhoneIcon, StarIcon } from '@/src/components/icons/HeroIcons';
import { DatabaseVendor } from '@/src/services/vendorsService';
import { formatRating, formatWebsite, truncateDescription } from '@/src/utils/vendorUtils';

interface VendorCardProps {
    vendor: DatabaseVendor;
    onAddToWeddingPlan: (vendor: DatabaseVendor) => void;
}

const VendorCard: React.FC<VendorCardProps> = ({ vendor, onAddToWeddingPlan }) => {
    const displayName = vendor.business_name || (vendor as any).name || (vendor as any).venue_name || 'Vendor';

    return (
        <div className="border border-border rounded-lg p-4 hover:shadow-md transition-shadow bg-card">
            {/* Header */}
            <div className="mb-3">
                <h3 className="font-semibold text-lg text-card-foreground truncate" title={displayName}>
                    {displayName}
                </h3>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground mt-1">
                    <MapPinIcon className="w-4 h-4" />
                    <span title={`${vendor.city}, ${vendor.state}`}>{vendor.city}, {vendor.state}</span>
                </div>
            </div>

            {/* Description */}
            {vendor.description && (
                <p className="text-sm text-muted-foreground mb-3 line-clamp-3">
                    {truncateDescription(vendor.description)}
                </p>
            )}

            {/* Rating */}
            {vendor.review_score && (
                <div className="flex items-center space-x-2 mb-3">
                    <StarIcon className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm font-medium">
                        {formatRating(vendor.review_score)}
                    </span>
                    {vendor.review_count && (
                        <span className="text-xs text-muted-foreground">
                            ({vendor.review_count} reviews)
                        </span>
                    )}
                </div>
            )}

            {/* Contact Info */}
            <div className="space-y-2 mb-4">
                {vendor.phone && (
                    <div className="flex items-center space-x-2 text-sm">
                        <PhoneIcon className="w-4 h-4 text-muted-foreground" />
                        <a
                            href={`tel:${vendor.phone}`}
                            className="text-primary hover:underline"
                        >
                            {vendor.phone}
                        </a>
                    </div>
                )}
                {vendor.email && (
                    <div className="flex items-center space-x-2 text-sm">
                        <EnvelopeIcon className="w-4 h-4 text-muted-foreground" />
                        <a
                            href={`mailto:${vendor.email}`}
                            className="text-primary hover:underline truncate"
                        >
                            {vendor.email}
                        </a>
                    </div>
                )}
                {vendor.website && (
                    <div className="flex items-center space-x-2 text-sm">
                        <GlobeAltIcon className="w-4 h-4 text-muted-foreground" />
                        <a
                            href={formatWebsite(vendor.website)}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-primary hover:underline truncate"
                        >
                            Visit Website
                        </a>
                    </div>
                )}
            </div>

            {/* Action Button */}
            <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => onAddToWeddingPlan(vendor)}
            >
                Add to Wedding Plan
            </Button>
        </div>
    );
};

export default VendorCard; 