import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import { DatabaseVendor, VendorSearchParams } from '@/src/services/vendorsService';
import { VendorCategory } from '@/src/types/index';
import VendorCard from './VendorCard';

interface VendorGridProps {
    vendors: DatabaseVendor[];
    selectedCategory: VendorCategory;
    loading: boolean;
    searchParams: VendorSearchParams;
    onAddToWeddingPlan: (vendor: DatabaseVendor) => void;
    onLoadMore: () => void;
}

const VendorGrid: React.FC<VendorGridProps> = ({
    vendors,
    selectedCategory,
    loading,
    searchParams,
    onAddToWeddingPlan,
    onLoadMore,
}) => {
    const showLoadMore = !loading && vendors.length > 0 && vendors.length >= (searchParams.limit || 20);

    return (
        <>
            <Card title={`${selectedCategory} Vendors`}>
                {loading ? (
                    <div className="flex justify-center py-8">
                        <LoadingSpinner text="Searching vendors..." />
                    </div>
                ) : vendors.length === 0 ? (
                    <div className="text-center py-8">
                        <p className="text-muted-foreground">No vendors found matching your criteria.</p>
                        <p className="text-sm text-muted-foreground mt-2">Try adjusting your search filters.</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {vendors.map((vendor) => (
                            <VendorCard
                                key={vendor.id}
                                vendor={vendor}
                                onAddToWeddingPlan={onAddToWeddingPlan}
                            />
                        ))}
                    </div>
                )}
            </Card>

            {/* Load More */}
            {showLoadMore && (
                <div className="text-center">
                    <Button
                        variant="outline"
                        onClick={onLoadMore}
                    >
                        Load More Vendors
                    </Button>
                </div>
            )}
        </>
    );
};

export default VendorGrid; 