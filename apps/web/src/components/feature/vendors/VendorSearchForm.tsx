import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import { MagnifyingGlassIcon } from '@/src/components/icons/HeroIcons';
import { VendorSearchParams } from '@/src/services/vendorsService';
import { VendorCategory } from '@/src/types/index';

interface VendorSearchFormProps {
    searchParams: VendorSearchParams;
    selectedCategory: VendorCategory;
    availableCategories: { category: string; count: number }[];
    loading: boolean;
    onSearch: (e: React.FormEvent) => void;
    onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onCategoryChange: (category: VendorCategory) => void;
}

const VENDOR_CATEGORIES: VendorCategory[] = [
    'Venue', 'Photographer', 'Caterer', 'Florist', 'DJ/Band', 'Attire',
    'Stationery', 'Transportation', 'Cake', 'Officiant', 'Videographer',
    'Wedding Planner', 'Hair & Makeup', 'Favors', 'Other'
];

const VendorSearchForm: React.FC<VendorSearchFormProps> = ({
    searchParams,
    selectedCategory,
    availableCategories,
    loading,
    onSearch,
    onInputChange,
    onCategoryChange,
}) => {
    return (
        <Card title="Search Vendors">
            <form onSubmit={onSearch} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label htmlFor="category" className="block text-sm font-medium text-muted-foreground mb-1.5">
                            Category
                        </label>
                        <select
                            id="category"
                            value={selectedCategory}
                            onChange={(e) => onCategoryChange(e.target.value as VendorCategory)}
                            className="w-full px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                        >
                            {VENDOR_CATEGORIES.map(cat => {
                                const categoryData = availableCategories.find(c => c.category === cat);
                                return (
                                    <option key={cat} value={cat}>
                                        {cat} {categoryData ? `(${categoryData.count})` : ''}
                                    </option>
                                );
                            })}
                        </select>
                    </div>
                    <Input
                        label="Search"
                        name="search"
                        value={searchParams.search || ''}
                        onChange={onInputChange}
                        placeholder="Vendor name or description..."
                    />
                    <Input
                        label="City"
                        name="city"
                        value={searchParams.city || ''}
                        onChange={onInputChange}
                        placeholder="e.g. San Francisco"
                    />
                    <Input
                        label="State"
                        name="state"
                        value={searchParams.state || ''}
                        onChange={onInputChange}
                        placeholder="e.g. CA"
                    />
                </div>
                <div className="flex space-x-3">
                    <Button
                        type="submit"
                        variant="primary"
                        leftIcon={<MagnifyingGlassIcon className="w-5 h-5" />}
                        isLoading={loading}
                    >
                        Search Vendors
                    </Button>
                </div>
            </form>
        </Card>
    );
};

export default VendorSearchForm; 