import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import { ExclamationTriangleIcon } from '@/src/components/icons/HeroIcons';

interface ErrorCardProps {
    errorMessage: string;
    onBackToDashboard: () => void;
}

const ErrorCard: React.FC<ErrorCardProps> = ({
    errorMessage,
    onBackToDashboard,
}) => {
    return (
        <div className="min-h-screen bg-animated-gradient flex flex-col items-center justify-center p-4 text-center">
            <Card className="w-full max-w-md shadow-glow-primary">
                <div className="flex flex-col items-center">
                    <ExclamationTriangleIcon className="w-16 h-16 text-destructive mb-4" />
                    <h2 className="text-2xl font-semibold text-destructive mb-3">Error Loading Client</h2>
                    <p className="text-muted-foreground mb-6">{errorMessage}</p>
                    <Button onClick={onBackToDashboard} variant="primary">
                        Back to Dashboard
                    </Button>
                </div>
            </Card>
        </div>
    );
};

export default ErrorCard; 