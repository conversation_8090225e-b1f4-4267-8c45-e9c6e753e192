/**
 * AnimatedAIIndicator component for the application.
 * @returns The AnimatedAIIndicator component.
 */

const AnimatedAIIndicator: React.FC = () => (
    <svg width="60" height="20" viewBox="0 0 60 20" className="block mx-auto mt-3 opacity-70">
        <circle className="ai-pulse-dot" cx="10" cy="10" r="3" fill="hsl(var(--color-primary))" />
        <circle className="ai-pulse-dot" style={{ animationDelay: '0.2s' }} cx="30" cy="10" r="3" fill="hsl(var(--color-primary))" />
        <circle className="ai-pulse-dot" style={{ animationDelay: '0.4s' }} cx="50" cy="10" r="3" fill="hsl(var(--color-primary))" />
    </svg>
);

export default AnimatedAIIndicator; 