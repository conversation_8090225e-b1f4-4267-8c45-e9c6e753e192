import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import Textarea from '@/src/components/common/Textarea';
import { BUDGET_OPTIONS, GUEST_COUNT_OPTIONS, VIBE_OPTIONS } from '@/src/constants/onboardingConstants';
import { AddWeddingFormData } from '@/src/utils/onboardingUtils';

interface OnboardingFormProps {
    formData: AddWeddingFormData;
    formErrors: Partial<Record<keyof AddWeddingFormData, string>>;
    isEditMode: boolean;
    onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
    onSubmit: (e: React.FormEvent) => void;
}

const OnboardingForm: React.FC<OnboardingFormProps> = ({
    formData,
    formErrors,
    isEditMode,
    onInputChange,
    onSubmit,
}) => {
    return (
        <Card className="w-full max-w-xl shadow-glow-primary hover:shadow-glow-primary-lg">
            <form onSubmit={onSubmit} className="space-y-6">
                <Input
                    label="Client Couple's Names"
                    id="coupleNames"
                    name="coupleNames"
                    value={formData.coupleNames}
                    onChange={onInputChange}
                    placeholder="e.g., Alex & Jamie"
                    error={formErrors.coupleNames}
                    required
                />

                <Input
                    label="Prospective Wedding Date for Client"
                    id="weddingDate"
                    name="weddingDate"
                    type="date"
                    value={formData.weddingDate}
                    onChange={onInputChange}
                    error={formErrors.weddingDate}
                    required
                    className="appearance-none"
                />

                <Input
                    label="Client's Primary Location / City"
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={onInputChange}
                    placeholder="e.g., San Francisco, CA or Tuscany, Italy"
                    error={formErrors.location}
                    required
                />

                <div>
                    <label htmlFor="vibe" className="block text-sm font-medium text-muted-foreground mb-1.5">
                        Client&apos;s Wedding Vibe/Style
                    </label>
                    <select
                        id="vibe"
                        name="vibe"
                        value={formData.vibe}
                        onChange={onInputChange}
                        className="w-full px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    >
                        {VIBE_OPTIONS.map(opt => (
                            <option key={opt} value={opt}>{opt}</option>
                        ))}
                    </select>
                </div>

                {formData.vibe === "Other" && (
                    <Textarea
                        label="Describe Client's Custom Vibe"
                        id="customVibe"
                        name="customVibe"
                        value={formData.customVibe}
                        onChange={onInputChange}
                        placeholder="e.g., Whimsical fairytale with a touch of vintage glamour"
                        rows={3}
                        error={formErrors.customVibe}
                        required
                    />
                )}

                <div>
                    <label htmlFor="guestCount" className="block text-sm font-medium text-muted-foreground mb-1.5">
                        Client&apos;s Estimated Guest Count
                    </label>
                    <select
                        id="guestCount"
                        name="guestCount"
                        value={formData.guestCount}
                        onChange={onInputChange}
                        className={`w-full px-4 py-2.5 rounded-md bg-input border ${formErrors.guestCount ? 'border-destructive' : 'border-border'
                            } text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary`}
                    >
                        {GUEST_COUNT_OPTIONS.map(opt => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                        ))}
                    </select>
                    {formErrors.guestCount && (
                        <p className="mt-1.5 text-xs text-destructive">{formErrors.guestCount}</p>
                    )}
                </div>

                <div>
                    <label htmlFor="initialBudget" className="block text-sm font-medium text-muted-foreground mb-1.5">
                        Client&apos;s Initial Budget Idea
                    </label>
                    <select
                        id="initialBudget"
                        name="initialBudget"
                        value={formData.initialBudget}
                        onChange={onInputChange}
                        className={`w-full px-4 py-2.5 rounded-md bg-input border ${formErrors.initialBudget ? 'border-destructive' : 'border-border'
                            } text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary`}
                    >
                        {BUDGET_OPTIONS.map(opt => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                        ))}
                    </select>
                    {formErrors.initialBudget && (
                        <p className="mt-1.5 text-xs text-destructive">{formErrors.initialBudget}</p>
                    )}
                </div>

                <div className="pt-2">
                    <Button type="submit" variant="primary" size="lg" className="w-full button-pulse-primary">
                        {isEditMode ? "Save Client Changes" : "Create Client Wedding Portal"}
                    </Button>
                </div>
            </form>
        </Card>
    );
};

export default OnboardingForm; 