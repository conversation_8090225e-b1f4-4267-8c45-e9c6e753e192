import Button from '@/src/components/common/Button';
import customImageLoader from '@/src/utils/imageLoader';
import Image from 'next/image';
import { useState } from 'react';

interface VisionSnapshotCardProps {
    visionSnapshotMessage: string;
    isEditMode: boolean;
    showPortalButton: boolean;
    onGoToPortal: () => void;
    onTryAgain: () => void;
}

const VisionSnapshotCard: React.FC<VisionSnapshotCardProps> = ({
    visionSnapshotMessage,
    isEditMode,
    showPortalButton,
    onGoToPortal,
    onTryAgain,
}) => {
    const [imageError, setImageError] = useState(false);

    // Extract image URL if present in the message
    const imageUrlMatch = visionSnapshotMessage.match(/!\[.*?\]\((.*?)\)/);
    const imageUrl = imageUrlMatch ? imageUrlMatch[1] : null;

    return (
        <div className="bg-card rounded-xl shadow-lg p-6 max-w-2xl w-full animate-fadeIn">
            <h2 className="text-2xl font-semibold mb-4">
                {isEditMode ? "Client Details Updated" : "Vision Snapshot"}
            </h2>

            <div className="prose prose-sm dark:prose-invert">
                {imageUrl && !imageError ? (
                    <div className="relative w-full h-48 mb-4 rounded-lg overflow-hidden">
                        <Image
                            src={imageUrl}
                            alt="Vision snapshot"
                            fill
                            style={{ objectFit: 'cover' }}
                            loader={customImageLoader}
                            onError={() => setImageError(true)}
                        />
                    </div>
                ) : null}

                <div dangerouslySetInnerHTML={{ __html: visionSnapshotMessage.replace(/!\[.*?\]\(.*?\)/g, '') }} />
            </div>

            <div className="mt-6 flex flex-col sm:flex-row gap-3">
                {showPortalButton && (
                    <Button onClick={onGoToPortal} className="w-full sm:w-auto">
                        {isEditMode ? "Return to Wedding Portal" : "Go to Wedding Portal"}
                    </Button>
                )}
                <Button onClick={onTryAgain} variant="outline" className="w-full sm:w-auto">
                    Try Again
                </Button>
            </div>
        </div>
    );
};

export default VisionSnapshotCard;
