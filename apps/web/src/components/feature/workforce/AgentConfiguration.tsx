import { CheckCircleIcon } from '@/src/components/icons/HeroIcons';
import { AVAILABLE_LLM_MODELS, PREDEFINED_AGENTS } from '@/src/constants';
import { LLMModel, WorkforceConfiguration } from '@/src/types/index';

interface AgentConfigurationProps {
    config: WorkforceConfiguration;
    onAgentToggle: (agentId: string) => void;
    onModelChange: (agentId: string, model: LLMModel) => void;
}

const AgentConfiguration: React.FC<AgentConfigurationProps> = ({
    config,
    onAgentToggle,
    onModelChange
}) => {
    return (
        <div className="space-y-4">
            <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-foreground">Agent Configuration</h3>
                <div className="text-sm text-muted-foreground">
                    {config.agents.filter(a => a.isEnabled).length} of {config.agents.length} agents enabled
                </div>
            </div>

            <div className="space-y-3">
                {config.agents.map(agent => {
                    const predefinedAgent = PREDEFINED_AGENTS.find((p: any) => p.id === agent.agentId);
                    if (!predefinedAgent) return null;

                    return (
                        <div key={agent.agentId} className="p-4 border rounded-lg bg-card">
                            <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-3">
                                    <button
                                        onClick={() => onAgentToggle(agent.agentId)}
                                        className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${agent.isEnabled
                                            ? 'bg-primary border-primary text-primary-foreground'
                                            : 'border-border bg-background'
                                            }`}
                                    >
                                        {agent.isEnabled && <CheckCircleIcon className="w-4 h-4" />}
                                    </button>
                                    <div>
                                        <h4 className="font-medium text-foreground">{predefinedAgent.name}</h4>
                                        <p className="text-xs text-muted-foreground">{predefinedAgent.description}</p>
                                    </div>
                                </div>
                                <div className={`px-2 py-1 rounded text-xs font-medium ${predefinedAgent.agentType === 'orchestrator' ? 'bg-primary/10 text-primary' :
                                    predefinedAgent.agentType === 'manager' ? 'bg-secondary/50 text-secondary-foreground' :
                                        'bg-accent/20 text-accent-foreground'
                                    }`}>
                                    {predefinedAgent.agentType.toUpperCase()}
                                </div>
                            </div>

                            {agent.isEnabled && (
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3 pt-3 border-t border-border">
                                    <div>
                                        <label className="block text-xs font-medium text-muted-foreground mb-1">
                                            AI Model
                                        </label>
                                        <select
                                            value={agent.llmModel}
                                            onChange={(e) => onModelChange(agent.agentId, e.target.value as LLMModel)}
                                            className="w-full px-2 py-1 text-xs rounded border border-border bg-background text-foreground"
                                        >
                                            {AVAILABLE_LLM_MODELS.map((model: LLMModel) => (
                                                <option key={model} value={model}>{model}</option>
                                            ))}
                                        </select>
                                    </div>

                                    <div>
                                        <label className="block text-xs font-medium text-muted-foreground mb-1">
                                            Max Tasks
                                        </label>
                                        <input
                                            type="number"
                                            min="1"
                                            max="10"
                                            value={agent.maxConcurrentTasks}
                                            className="w-full px-2 py-1 text-xs rounded border border-border bg-background text-foreground"
                                            readOnly
                                        />
                                    </div>

                                    <div>
                                        <label className="block text-xs font-medium text-muted-foreground mb-1">
                                            Autonomy
                                        </label>
                                        <select
                                            value={agent.autonomyLevel}
                                            className="w-full px-2 py-1 text-xs rounded border border-border bg-background text-foreground"
                                            disabled
                                        >
                                            <option value="review_all">Review All</option>
                                            <option value="balanced">Balanced</option>
                                            <option value="proactive">Proactive</option>
                                        </select>
                                    </div>
                                </div>
                            )}

                            {predefinedAgent.capabilities && (
                                <div className="mt-3 pt-2 border-t border-border">
                                    <div className="flex flex-wrap gap-1">
                                        {predefinedAgent.capabilities.slice(0, 4).map(capability => (
                                            <span key={capability} className="px-2 py-1 bg-secondary/30 text-xs rounded">
                                                {capability}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default AgentConfiguration; 