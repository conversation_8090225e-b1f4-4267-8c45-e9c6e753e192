import { CheckCircleIcon, SparklesIcon } from '@/src/components/icons/HeroIcons';
import { WorkforceConfigManager } from '@/src/services/WorkforceConfigManager';
import { ManagedWedding } from '@/src/types/index';

interface TemplateSelectionProps {
    currentWedding: ManagedWedding;
    selectedTemplate: string | null;
    onTemplateSelect: (templateId: string) => void;
}

const TemplateSelection: React.FC<TemplateSelectionProps> = ({
    currentWedding,
    selectedTemplate,
    onTemplateSelect
}) => {
    const templates = WorkforceConfigManager.getDefaultTemplates();
    const recommendations = WorkforceConfigManager.getAgentRecommendations({
        guestCount: currentWedding.weddingDetails.guestCount.toString(),
        initialBudget: currentWedding.weddingDetails.initialBudget?.toString(),
        vibe: currentWedding.weddingDetails.vibe,
        location: currentWedding.weddingDetails.location
    });

    return (
        <div className="space-y-4">
            <div className="mb-4">
                <h3 className="text-lg font-medium text-foreground mb-2">
                    Recommended for {currentWedding.weddingDetails.coupleNames}
                </h3>
                <div className="p-3 bg-primary/10 rounded-lg border border-primary/20">
                    <p className="text-sm text-primary font-medium">
                        Recommended: {recommendations.recommendedTemplate.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </p>
                    <ul className="text-xs text-muted-foreground mt-2 space-y-1">
                        {recommendations.reasonings.map((reason, idx) => (
                            <li key={idx}>• {reason}</li>
                        ))}
                    </ul>
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templates.map(template => {
                    const isSelected = selectedTemplate === template.id;
                    const isRecommended = recommendations.recommendedTemplate === template.id && !selectedTemplate;

                    return (
                        <div
                            key={template.id}
                            className={`p-4 border rounded-lg cursor-pointer transition-colors ${isSelected
                                ? 'border-primary bg-primary/10 ring-2 ring-primary/20'
                                : isRecommended
                                    ? 'border-primary bg-primary/5'
                                    : 'border-border bg-card hover:bg-secondary/30'
                                }`}
                            onClick={() => onTemplateSelect(template.id)}
                        >
                            <div className="flex items-center gap-2 mb-2">
                                <div className={`px-2 py-1 rounded text-xs font-medium ${template.category === 'basic' ? 'bg-green-100 text-green-800' :
                                    template.category === 'premium' ? 'bg-blue-100 text-blue-800' :
                                        'bg-purple-100 text-purple-800'
                                    }`}>
                                    {template.category.toUpperCase()}
                                </div>
                                {isSelected && (
                                    <CheckCircleIcon className="w-4 h-4 text-primary" />
                                )}
                                {recommendations.recommendedTemplate === template.id && !isSelected && (
                                    <SparklesIcon className="w-4 h-4 text-primary" />
                                )}
                            </div>

                            <h4 className="font-medium text-foreground mb-1">{template.name}</h4>
                            <p className="text-xs text-muted-foreground mb-3">{template.description}</p>

                            <div className="space-y-2 text-xs">
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Agents:</span>
                                    <span className="font-medium">{template.defaultAgents.length}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-muted-foreground">Est. Cost:</span>
                                    <span className="font-medium">${template.estimatedMonthlyCost}/mo</span>
                                </div>
                            </div>

                            <ul className="text-xs text-muted-foreground mt-3 space-y-1">
                                {template.recommendedFor.slice(0, 2).map((rec, idx) => (
                                    <li key={idx}>• {rec}</li>
                                ))}
                            </ul>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default TemplateSelection; 