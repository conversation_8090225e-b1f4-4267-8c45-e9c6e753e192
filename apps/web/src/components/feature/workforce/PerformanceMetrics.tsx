import Card from '@/src/components/common/Card';
import {
    ClipboardDocumentIcon,
    CpuChipIcon,
    CurrencyDollarIcon,
    ExclamationTriangleIcon,
    SparklesIcon
} from '@/src/components/icons/HeroIcons';
import { WorkforceConfiguration } from '@/src/types/index';

interface PerformanceMetricsProps {
    currentConfig: WorkforceConfiguration;
    performanceAnalysis: {
        overallScore: number;
        costEfficiency: number;
        bottlenecks: string[];
        recommendations: string[];
    };
}

const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({
    currentConfig,
    performanceAnalysis
}) => {
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium text-foreground">Performance Analysis</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                        <ClipboardDocumentIcon className="w-5 h-5 text-primary" />
                        <span className="font-medium text-sm">Overall Score</span>
                    </div>
                    <div className="text-2xl font-bold text-primary">
                        {(performanceAnalysis.overallScore * 100).toFixed(0)}%
                    </div>
                </Card>

                <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                        <CurrencyDollarIcon className="w-5 h-5 text-secondary" />
                        <span className="font-medium text-sm">Cost Efficiency</span>
                    </div>
                    <div className="text-2xl font-bold text-secondary">
                        {performanceAnalysis.costEfficiency.toFixed(1)}
                    </div>
                    <div className="text-xs text-muted-foreground">tasks/$</div>
                </Card>

                <Card className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                        <CpuChipIcon className="w-5 h-5 text-accent" />
                        <span className="font-medium text-sm">Active Agents</span>
                    </div>
                    <div className="text-2xl font-bold text-accent">
                        {currentConfig.agents.filter(a => a.isEnabled).length}
                    </div>
                </Card>
            </div>

            {performanceAnalysis.bottlenecks.length > 0 && (
                <Card className="p-4 border-orange-200">
                    <div className="flex items-center gap-2 mb-2">
                        <ExclamationTriangleIcon className="w-5 h-5 text-orange-500" />
                        <span className="font-medium text-sm">Bottlenecks Detected</span>
                    </div>
                    <ul className="text-sm text-orange-700 space-y-1">
                        {performanceAnalysis.bottlenecks.map((bottleneck, idx) => (
                            <li key={idx}>• {bottleneck}</li>
                        ))}
                    </ul>
                </Card>
            )}

            {performanceAnalysis.recommendations.length > 0 && (
                <Card className="p-4 border-blue-200">
                    <div className="flex items-center gap-2 mb-2">
                        <SparklesIcon className="w-5 h-5 text-blue-500" />
                        <span className="font-medium text-sm">Recommendations</span>
                    </div>
                    <ul className="text-sm text-blue-700 space-y-1">
                        {performanceAnalysis.recommendations.map((rec, idx) => (
                            <li key={idx}>• {rec}</li>
                        ))}
                    </ul>
                </Card>
            )}
        </div>
    );
};

export default PerformanceMetrics; 