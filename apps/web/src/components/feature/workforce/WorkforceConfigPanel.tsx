import Button from '@/src/components/common/Button';
import AgentConfiguration from '@/src/components/feature/workforce/AgentConfiguration';
import CostAnalysis from '@/src/components/feature/workforce/CostAnalysis';
import PerformanceMetrics from '@/src/components/feature/workforce/PerformanceMetrics';
import TabNavigation from '@/src/components/feature/workforce/TabNavigation';
import TemplateSelection from '@/src/components/feature/workforce/TemplateSelection';
import { BanknotesIcon, ChartBarIcon, CogIcon, CpuChipIcon, XCircleIcon } from '@/src/components/icons/HeroIcons';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { WorkforceConfigManager } from '@/src/services/WorkforceConfigManager';
import { LLMModel, WorkforceConfiguration } from '@/src/types/index';
import React, { useContext, useState } from 'react';

interface WorkforceConfigPanelProps {
    isVisible: boolean;
    onClose: () => void;
}

const WorkforceConfigPanel: React.FC<WorkforceConfigPanelProps> = ({ isVisible, onClose }) => {
    const plannerCtx = useContext(PlannerContext);
    const [activeTab, setActiveTab] = useState<'templates' | 'agents' | 'performance' | 'costs'>('templates');
    const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
    const [workforceConfig, setWorkforceConfig] = useState<WorkforceConfiguration | null>(null);

    if (!isVisible || !plannerCtx?.currentWedding) return null;

    const currentWedding = plannerCtx.currentWedding;
    const currentConfig = workforceConfig || currentWedding.workforceConfig ||
        WorkforceConfigManager.createWorkforceConfig(currentWedding.id, 'basic-wedding-template');

    const costAnalysis = WorkforceConfigManager.calculateEstimatedCosts(currentConfig);
    const performanceAnalysis = WorkforceConfigManager.analyzePerformance(currentConfig);

    const handleTemplateSelect = (templateId: string) => {
        const newConfig = WorkforceConfigManager.createWorkforceConfig(
            currentWedding.id,
            templateId,
            `Custom Workforce for ${currentWedding.weddingDetails.coupleNames}`
        );
        setWorkforceConfig(newConfig);
        setSelectedTemplate(templateId);
    };

    const handleAgentToggle = (agentId: string) => {
        if (!workforceConfig) return;

        const updatedAgents = workforceConfig.agents.map(agent =>
            agent.agentId === agentId ? { ...agent, isEnabled: !agent.isEnabled } : agent
        );

        setWorkforceConfig({ ...workforceConfig, agents: updatedAgents });
    };

    const handleModelChange = (agentId: string, model: LLMModel) => {
        if (!workforceConfig) return;

        const updatedAgents = workforceConfig.agents.map(agent =>
            agent.agentId === agentId ? { ...agent, llmModel: model } : agent
        );

        setWorkforceConfig({ ...workforceConfig, agents: updatedAgents });
    };

    const handleSaveConfiguration = () => {
        if (!workforceConfig) return;

        plannerCtx.updateManagedWedding(currentWedding.id, {
            workforceConfig: workforceConfig
        });
        onClose();
    };

    const tabs = [
        { id: 'templates', label: 'Templates', icon: CpuChipIcon },
        { id: 'agents', label: 'Agents', icon: CogIcon },
        { id: 'performance', label: 'Performance', icon: ChartBarIcon },
        { id: 'costs', label: 'Costs', icon: BanknotesIcon }
    ];

    const renderTabContent = () => {
        switch (activeTab) {
            case 'templates':
                return (
                    <TemplateSelection
                        currentWedding={currentWedding}
                        selectedTemplate={selectedTemplate}
                        onTemplateSelect={handleTemplateSelect}
                    />
                );
            case 'agents':
                return (
                    <AgentConfiguration
                        config={currentConfig}
                        onAgentToggle={handleAgentToggle}
                        onModelChange={handleModelChange}
                    />
                );
            case 'performance':
                return (
                    <PerformanceMetrics
                        currentConfig={currentConfig}
                        performanceAnalysis={performanceAnalysis}
                    />
                );
            case 'costs':
                return (
                    <CostAnalysis
                        currentConfig={currentConfig}
                        costAnalysis={costAnalysis}
                    />
                );
            default:
                return null;
        }
    };

    return (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <div className="bg-background border border-border rounded-lg max-w-6xl w-full max-h-[90vh] overflow-hidden">
                <div className="flex items-center justify-between p-6 border-b border-border">
                    <h2 className="text-xl font-semibold text-foreground">
                        Workforce Configuration for {currentWedding.weddingDetails.coupleNames}
                    </h2>
                    <button
                        onClick={onClose}
                        className="p-2 hover:bg-secondary rounded-md transition-colors"
                    >
                        <XCircleIcon className="w-5 h-5 text-muted-foreground" />
                    </button>
                </div>

                <TabNavigation
                    tabs={tabs}
                    activeTab={activeTab}
                    onTabChange={(tabId) => setActiveTab(tabId as any)}
                />

                <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
                    {renderTabContent()}
                </div>

                <div className="flex items-center justify-between p-6 border-t border-border bg-secondary/20">
                    <div className="text-sm text-muted-foreground">
                        Configuration will be applied to {currentWedding.weddingDetails.coupleNames}'s wedding
                    </div>
                    <div className="flex gap-3">
                        <Button variant="outline" onClick={onClose}>
                            Cancel
                        </Button>
                        <Button variant="primary" onClick={handleSaveConfiguration}>
                            Save Configuration
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default WorkforceConfigPanel; 