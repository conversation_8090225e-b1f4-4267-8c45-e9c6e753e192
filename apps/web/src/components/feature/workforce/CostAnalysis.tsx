import Card from '@/src/components/common/Card';
import { PREDEFINED_AGENTS } from '@/src/constants';
import { WorkforceConfiguration } from '@/src/types/index';

interface CostAnalysisProps {
    currentConfig: WorkforceConfiguration;
    costAnalysis: {
        daily: number;
        monthly: number;
        breakdown: Record<string, number>;
    };
}

const CostAnalysis: React.FC<CostAnalysisProps> = ({
    currentConfig,
    costAnalysis
}) => {
    return (
        <div className="space-y-4">
            <h3 className="text-lg font-medium text-foreground">Cost Analysis</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                    <h4 className="font-medium text-foreground mb-3">Estimated Costs</h4>
                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <span className="text-muted-foreground">Daily:</span>
                            <span className="font-medium">${costAnalysis.daily.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-muted-foreground">Monthly:</span>
                            <span className="font-bold text-lg">${costAnalysis.monthly.toFixed(2)}</span>
                        </div>
                    </div>
                </Card>

                <Card className="p-4">
                    <h4 className="font-medium text-foreground mb-3">Budget Status</h4>
                    <div className="space-y-2">
                        <div className="flex justify-between">
                            <span className="text-muted-foreground">Budget:</span>
                            <span className="font-medium">${currentConfig.globalSettings.costBudget || 0}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-muted-foreground">Remaining:</span>
                            <span className={`font-medium ${(currentConfig.globalSettings.costBudget || 0) - costAnalysis.monthly > 0
                                ? 'text-green-600' : 'text-red-600'
                                }`}>
                                ${((currentConfig.globalSettings.costBudget || 0) - costAnalysis.monthly).toFixed(2)}
                            </span>
                        </div>
                    </div>
                </Card>
            </div>

            <Card className="p-4">
                <h4 className="font-medium text-foreground mb-3">Cost Breakdown by Agent</h4>
                <div className="space-y-2">
                    {Object.entries(costAnalysis.breakdown).map(([agentId, cost]) => {
                        const agent = PREDEFINED_AGENTS.find(a => a.id === agentId);
                        return (
                            <div key={agentId} className="flex justify-between items-center">
                                <span className="text-sm text-muted-foreground">{agent?.name || agentId}</span>
                                <span className="font-medium">${cost.toFixed(2)}/mo</span>
                            </div>
                        );
                    })}
                </div>
            </Card>
        </div>
    );
};

export default CostAnalysis; 