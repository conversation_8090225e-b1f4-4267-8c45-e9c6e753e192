import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import Modal from '@/src/components/common/Modal';
import { UserCircleIcon } from '@/src/components/icons/HeroIcons';
import { Task } from '@/src/types/index';
import { parseAndFormatDateStringForDisplay } from '@/src/utils/dateUtils';
import { TaskItem } from '@/src/utils/taskUtils';

type SubTaskSuggestion = Omit<Task, 'id' | 'isCompleted' | 'parentId'>;

interface TaskBreakdownModalProps {
    isOpen: boolean;
    onClose: () => void;
    selectedTask: TaskItem | null;
    clientCoupleNames: string;
    isBreakingDown: boolean;
    suggestedSubTasks: SubTaskSuggestion[];
    selectedSubTasksToAdd: boolean[];
    parentTaskUpdateSuggestion: Partial<Omit<Task, 'id' | 'isCompleted' | 'parentId'>> | null;
    onSubTaskCheckboxChange: (index: number) => void;
    onAddSelectedSubTasks: () => void;
    onUpdateParentTask: () => void;
}

const TaskBreakdownModal: React.FC<TaskBreakdownModalProps> = ({
    isOpen,
    onClose,
    selectedTask,
    clientCoupleNames,
    isBreakingDown,
    suggestedSubTasks,
    selectedSubTasksToAdd,
    parentTaskUpdateSuggestion,
    onSubTaskCheckboxChange,
    onAddSelectedSubTasks,
    onUpdateParentTask,
}) => {
    if (!selectedTask) return null;

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={`Break Down Task for ${clientCoupleNames}: ${selectedTask.title}`}
            size="xl"
        >
            {isBreakingDown ? (
                <div className="flex flex-col items-center justify-center min-h-[200px]">
                    <LoadingSpinner text="Ella is analyzing the task..." />
                </div>
            ) : (
                <div className="space-y-6">
                    {parentTaskUpdateSuggestion && Object.keys(parentTaskUpdateSuggestion).length > 0 && (
                        <Card title="Suggested Update for Parent Task" className="bg-secondary/30">
                            {parentTaskUpdateSuggestion.title && (
                                <p className="text-sm">
                                    New Title: <span className="italic text-primary">{parentTaskUpdateSuggestion.title}</span>
                                </p>
                            )}
                            {parentTaskUpdateSuggestion.description && (
                                <p className="text-sm mt-1">
                                    New Description: <span className="italic text-primary">{parentTaskUpdateSuggestion.description}</span>
                                </p>
                            )}
                            <Button
                                onClick={onUpdateParentTask}
                                size="sm"
                                variant="secondary"
                                className="mt-3"
                            >
                                Apply Parent Update
                            </Button>
                        </Card>
                    )}

                    {suggestedSubTasks.length > 0 ? (
                        <Card title="Suggested Sub-tasks">
                            <ul className="space-y-3 max-h-60 overflow-y-auto custom-scrollbar pr-2">
                                {suggestedSubTasks.map((subTask, index) => (
                                    <li key={index} className="p-3 bg-input rounded-md">
                                        <div className="flex items-start">
                                            <input
                                                type="checkbox"
                                                checked={selectedSubTasksToAdd[index] || false}
                                                onChange={() => onSubTaskCheckboxChange(index)}
                                                className="h-5 w-5 text-primary bg-background border-border rounded focus:ring-primary mr-3 mt-1 shrink-0"
                                                id={`subtask-chkbx-${index}`}
                                            />
                                            <label htmlFor={`subtask-chkbx-${index}`} className="flex-grow cursor-pointer">
                                                <p className="font-medium text-foreground">{subTask.title}</p>
                                                {subTask.description && (
                                                    <p className="text-xs text-muted-foreground mt-0.5">{subTask.description}</p>
                                                )}
                                                {subTask.dueDate && (
                                                    <p className="text-xs text-primary mt-0.5">
                                                        Due: {parseAndFormatDateStringForDisplay(subTask.dueDate)}
                                                    </p>
                                                )}
                                                {subTask.assignedTo && (
                                                    <p className="text-xs text-primary/80 mt-0.5 flex items-center">
                                                        <UserCircleIcon className="w-3 h-3 mr-1" />
                                                        {subTask.assignedTo}
                                                    </p>
                                                )}
                                            </label>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                            <Button
                                onClick={onAddSelectedSubTasks}
                                variant="primary"
                                className="mt-4"
                                disabled={selectedSubTasksToAdd.every(s => !s)}
                            >
                                Add Selected Sub-tasks
                            </Button>
                        </Card>
                    ) : (
                        <p className="text-muted-foreground">Ella couldn't break this task down further.</p>
                    )}

                    <div className="mt-6 pt-4 border-t border-border flex justify-end">
                        <Button onClick={onClose} variant="ghost">Done</Button>
                    </div>
                </div>
            )}
        </Modal>
    );
};

export default TaskBreakdownModal; 