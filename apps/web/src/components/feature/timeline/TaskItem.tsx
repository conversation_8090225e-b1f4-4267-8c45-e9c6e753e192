import Button from '@/src/components/common/Button';
import {
    BriefcaseIcon,
    CheckCircleIcon,
    ListBulletIcon,
    PencilIcon,
    TrashIcon,
    UserCircleIcon
} from '@/src/components/icons/HeroIcons';
import { parseAndFormatDateStringForDisplay } from '@/src/utils/dateUtils';
import { isTaskOverdue, TaskItem as TaskItemType } from '@/src/utils/taskUtils';

interface TaskItemProps {
    task: TaskItemType;
    isSubTask: boolean;
    todayForComparison: Date | null;
    onToggleCompletion: (taskId: string) => void;
    onEdit: (task: TaskItemType) => void;
    onDelete: (taskId: string) => void;
    onBreakdown?: (task: TaskItemType) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({
    task,
    isSubTask,
    todayForComparison,
    onToggleCompletion,
    onEdit,
    onDelete,
    onBreakdown,
}) => {
    const isOverdue = isTaskOverdue(task, todayForComparison);

    return (
        <li
            className={`p-3 rounded-md transition-all duration-200 ${task.isCompleted ? 'bg-positive/10' : 'bg-secondary hover:bg-accent'
                } ${isSubTask ? 'ml-6 border-l-2 border-primary/30 pl-3' : ''
                } ${task.isPlannerInternal ? 'border-l-4 border-purple-500' : ''
                }`}
        >
            <div className="flex items-start justify-between">
                <div className="flex items-start flex-grow mr-2">
                    <button
                        onClick={() => onToggleCompletion(task.id)}
                        className="mr-3 mt-1 focus:outline-none flex-shrink-0"
                        aria-label={task.isCompleted ? `Mark ${task.title} as incomplete` : `Mark ${task.title} as complete`}
                    >
                        {task.isCompleted ? (
                            <CheckCircleIcon className="w-6 h-6 text-positive" />
                        ) : (
                            <div className="w-6 h-6 border-2 border-muted-foreground/50 rounded-full hover:border-primary"></div>
                        )}
                    </button>
                    <div className="flex-grow">
                        <span className={`text-sm font-medium ${task.isCompleted ? 'line-through text-muted-foreground' : 'text-foreground'
                            }`}>
                            {task.isPlannerInternal && (
                                <span title="Planner-Internal Task">
                                    <BriefcaseIcon className="w-4 h-4 inline-block mr-1.5 text-purple-500" />
                                </span>
                            )}
                            {task.title}
                            {task.isPlannerInternal && (
                                <span className="text-xs text-purple-500 ml-1.5">(Internal)</span>
                            )}
                        </span>
                        {task.description && (
                            <p className="text-xs text-muted-foreground/80 mt-0.5">{task.description}</p>
                        )}
                        {task.assignedTo && (
                            <p className="text-xs text-primary/80 mt-0.5 flex items-center">
                                <UserCircleIcon className="w-3 h-3 mr-1" /> {task.assignedTo}
                            </p>
                        )}
                    </div>
                </div>
                <div className="flex flex-col items-end space-y-1 flex-shrink-0">
                    {task.dueDate && (
                        <span className={`text-xs ${task.isCompleted
                            ? 'text-muted-foreground/70'
                            : isOverdue
                                ? 'text-destructive font-semibold'
                                : 'text-primary'
                            }`}>
                            {task.isCompleted ? 'Completed' : 'Due'}: {parseAndFormatDateStringForDisplay(task.dueDate)}
                        </span>
                    )}
                    <div className="flex space-x-1">
                        {!task.isCompleted && !isSubTask && onBreakdown && (
                            <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => onBreakdown(task)}
                                className="!p-1 text-muted-foreground hover:text-primary"
                                title="Break Down Task"
                                aria-label="Break down task"
                            >
                                <ListBulletIcon className="w-4 h-4" />
                            </Button>
                        )}
                        <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onEdit(task)}
                            className="!p-1 text-muted-foreground hover:text-primary"
                            title="Edit Task"
                            aria-label="Edit task"
                        >
                            <PencilIcon className="w-4 h-4" />
                        </Button>
                        <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onDelete(task.id)}
                            className="!p-1 text-muted-foreground hover:text-destructive"
                            title="Delete Task"
                            aria-label="Delete task"
                        >
                            <TrashIcon className="w-4 h-4" />
                        </Button>
                    </div>
                </div>
            </div>
        </li>
    );
};

export default TaskItem; 