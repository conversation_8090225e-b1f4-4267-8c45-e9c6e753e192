import Card from '@/src/components/common/Card';

interface BudgetSummaryProps {
  totalEstimated: number;
  totalActual: number;
  budgetDifference: number;
  formatCurrency: (amount: number) => string;
}

const BudgetSummary: React.FC<BudgetSummaryProps> = ({ totalEstimated, totalActual, budgetDifference, formatCurrency }) => {
  return (
    <Card title="Budget Overview">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
        <div>
          <p className="text-sm text-muted-foreground">Total Estimated</p>
          <p className="text-2xl font-bold text-primary">{formatCurrency(totalEstimated)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Total Actual Spent</p>
          <p className="text-2xl font-bold text-foreground">{formatCurrency(totalActual)}</p>
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Remaining / (Over Budget)</p>
          <p className={`text-2xl font-bold ${budgetDifference >= 0 ? 'text-positive' : 'text-destructive'}`}>
            {formatCurrency(budgetDifference)}
          </p>
        </div>
      </div>
    </Card>
  );
};

export default BudgetSummary;