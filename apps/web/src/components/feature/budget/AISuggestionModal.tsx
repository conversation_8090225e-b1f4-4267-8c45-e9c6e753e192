import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Modal from '@/src/components/common/Modal';
import { CheckCircleIcon } from '@/src/components/icons/HeroIcons';
import { BudgetFormState, formatCurrency } from '@/src/utils/budgetUtils';

interface AISuggestionModalProps {
    isOpen: boolean;
    onClose: () => void;
    aiSuggestedItem: BudgetFormState | null;
    clientCoupleNames: string;
    onConfirm: () => void;
    onDismiss: () => void;
}

const AISuggestionModal: React.FC<AISuggestionModalProps> = ({
    isOpen,
    onClose,
    aiSuggestedItem,
    clientCoupleNames,
    onConfirm,
    onDismiss,
}) => {
    if (!aiSuggestedItem) return null;

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={`Ella Suggests for ${clientCoupleNames}`}
            size="md"
        >
            <div className="space-y-4">
                <p className="text-muted-foreground">
                    Ella has a suggestion for your client&apos;s budget. Would you like to add it?
                </p>
                <Card className="!bg-input !shadow-none">
                    <p><strong className="text-primary">Category:</strong> {aiSuggestedItem.category}</p>
                    <p><strong className="text-primary">Estimated Cost:</strong> {formatCurrency(aiSuggestedItem.estimatedCost)}</p>
                    {aiSuggestedItem.vendor && (
                        <p><strong className="text-primary">Vendor:</strong> {aiSuggestedItem.vendor}</p>
                    )}
                </Card>
                <div className="flex justify-end space-x-3 pt-3 border-t border-border">
                    <Button onClick={onDismiss} variant="ghost">
                        No, thanks
                    </Button>
                    <Button
                        onClick={onConfirm}
                        variant="primary"
                        leftIcon={<CheckCircleIcon className="w-5 h-5" />}
                    >
                        Add to Budget
                    </Button>
                </div>
            </div>
        </Modal>
    );
};

export default AISuggestionModal; 