import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import { XCircleIcon } from '@/src/components/icons/HeroIcons';
import { BudgetItem } from '@/src/types/index';

interface BudgetFormProps {
  formState: Partial<BudgetItem>;
  editingItem: BudgetItem | null;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  resetForm: () => void;
  formErrors?: Record<string, string>;
}

const BudgetForm: React.FC<BudgetFormProps> = ({ formState, editingItem, handleInputChange, handleSubmit, resetForm, formErrors }) => {
  return (
    // Fix: Removed non-existent 'glowColor' prop from Card component.
    <Card title={editingItem ? "Edit Budget Item" : "Add New Budget Item"}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input label="Category" name="category" value={formState.category || ''} onChange={handleInputChange} placeholder="e.g., Venue, Catering" error={formErrors?.category} required />
          <Input label="Estimated Cost ($)" name="estimatedCost" type="number" value={formState.estimatedCost ?? ''} onChange={handleInputChange} placeholder="0.00" error={formErrors?.estimatedCost} required min="0" step="0.01" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input label="Actual Cost ($)" name="actualCost" type="number" value={formState.actualCost ?? ''} onChange={handleInputChange} placeholder="0.00" error={formErrors?.actualCost} min="0" step="0.01" />
          <Input label="Vendor" name="vendor" value={formState.vendor || ''} onChange={handleInputChange} placeholder="Vendor name" error={formErrors?.vendor} />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input label="Payment Due Date" name="paymentDueDate" type="date" value={formState.paymentDueDate || ''} onChange={handleInputChange} error={formErrors?.paymentDueDate} />
          <div className="flex items-center pt-6">
            <input type="checkbox" id="isPaid" name="isPaid" checked={formState.isPaid || false} onChange={handleInputChange} className="h-5 w-5 text-purple-600 border-slate-500 rounded focus:ring-purple-500 bg-slate-700 mr-2" />
            <label htmlFor="isPaid" className="text-sm text-slate-300">Mark as Paid</label>
          </div>
        </div>
        <div className="flex space-x-3 pt-2">
          <Button type="submit" variant="primary">{editingItem ? 'Update Item' : 'Save Item'}</Button>
          <Button type="button" onClick={resetForm} variant="ghost" leftIcon={<XCircleIcon className="w-5 h-5" />}>Cancel</Button>
        </div>
      </form>
    </Card>
  );
};

export default BudgetForm;