import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import { PencilIcon, TrashIcon } from '@/src/components/icons/HeroIcons';
import { CrudItem } from '@/src/hooks/useCrudManager';
import { BudgetItem } from '@/src/types/index';
import { parseAndFormatDateStringForDisplay } from '@/src/utils/dateUtils';

interface BudgetItemType extends BudgetItem, CrudItem { }

interface BudgetTableProps {
  items: BudgetItemType[];
  handleEditItem: (itemToEdit: BudgetItemType) => void;
  handleDeleteItem: (id: string) => void;
  formatCurrency: (amount: number | undefined) => string;
  totalEstimated: number;
  totalActual: number;
}

const BudgetTable: React.FC<BudgetTableProps> = ({ items, handleEditItem, handleDeleteItem, formatCurrency, totalActual, totalEstimated }) => {
  return (
    <Card title="Budget Breakdown">
      {items.length === 0 ? (
        <p className="text-muted-foreground text-center py-8">No budget items yet. Click &quot;Add New Item&quot; or ask <PERSON> for suggestions!</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-secondary/50">
              <tr>
                {['Category', 'Est. Cost', 'Actual Cost', 'Vendor', 'Due Date', 'Status', 'Actions'].map(header => (
                  <th key={header} scope="col" className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">{header}</th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {items.map((item) => (
                <tr key={item.id} className="hover:bg-accent/50 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-foreground">{item.category}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{formatCurrency(item.estimatedCost)}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{formatCurrency(item.actualCost)}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{item.vendor || '-'}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">{item.paymentDueDate ? parseAndFormatDateStringForDisplay(item.paymentDueDate) : '-'}</td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <span className={`px-2.5 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full ${item.isPaid ? 'bg-positive/20 text-positive' : 'bg-warning/20 text-warning-foreground'}`}>
                      {item.isPaid ? 'Paid' : 'Pending'}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium space-x-2">
                    <Button size="sm" variant="ghost" onClick={() => handleEditItem(item)} className="!p-1 text-primary hover:text-primary/80" aria-label="Edit item">
                      <PencilIcon className="w-4 h-4" />
                    </Button>
                    <Button size="sm" variant="ghost" onClick={() => handleDeleteItem(item.id)} className="!p-1 text-destructive hover:text-destructive/80" aria-label="Delete item">
                      <TrashIcon className="w-4 h-4" />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
            <tfoot>
              <tr className="border-t-2 border-border bg-secondary/50">
                <td className="px-4 py-3 text-sm font-bold text-foreground">Totals</td>
                <td className="px-4 py-3 text-sm font-bold text-primary">{formatCurrency(totalEstimated)}</td>
                <td className="px-4 py-3 text-sm font-bold text-foreground">{formatCurrency(totalActual)}</td>
                <td colSpan={4}></td>
              </tr>
            </tfoot>
          </table>
        </div>
      )}
    </Card>
  );
};

export default BudgetTable;