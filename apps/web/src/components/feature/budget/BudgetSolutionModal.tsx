import Button from '@/src/components/common/Button';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import Modal from '@/src/components/common/Modal';
import { BudgetItem } from '@/src/types/index';
import { formatCurrency } from '@/src/utils/budgetUtils';

interface BudgetSolutionModalProps {
    isOpen: boolean;
    onClose: () => void;
    budgetSolutionFor: BudgetItem | null;
    clientCoupleNames: string;
    isFetchingBudgetSolutions: boolean;
    budgetAISolutions: string | null;
}

const BudgetSolutionModal: React.FC<BudgetSolutionModalProps> = ({
    isOpen,
    onClose,
    budgetSolutionFor,
    clientCoupleNames,
    isFetchingBudgetSolutions,
    budgetAISolutions,
}) => {
    if (!budgetSolutionFor) return null;

    return (
        <Modal
            isOpen={isOpen}
            onClose={onClose}
            title={`Ella&apos;s Advice for ${clientCoupleNames}&apos;s ${budgetSolutionFor.category}`}
            size="lg"
        >
            {isFetchingBudgetSolutions ? (
                <div className="flex flex-col items-center justify-center min-h-[200px]">
                    <LoadingSpinner text="Ella is crafting some solutions..." color="text-primary" />
                </div>
            ) : (
                <div className="space-y-3">
                    <div className="p-3 bg-secondary/50 rounded-md mb-4">
                        <p className="text-sm text-muted-foreground">
                            For <strong className="text-primary">{budgetSolutionFor.category}</strong>:
                        </p>
                        <ul className="list-disc list-inside ml-4 text-sm">
                            <li>Estimated: <span className="font-semibold">{formatCurrency(budgetSolutionFor.estimatedCost)}</span></li>
                            <li>Actual: <span className="font-semibold text-destructive">{formatCurrency(budgetSolutionFor.actualCost)}</span></li>
                            <li>Over by: <span className="font-semibold text-destructive">{formatCurrency(budgetSolutionFor.actualCost - budgetSolutionFor.estimatedCost)}</span></li>
                        </ul>
                    </div>
                    {budgetAISolutions ? (
                        <div
                            className="whitespace-pre-wrap text-foreground text-sm leading-relaxed"
                            dangerouslySetInnerHTML={{ __html: budgetAISolutions.replace(/\n/g, '<br />') }}
                        />
                    ) : (
                        <p className="text-muted-foreground">No specific solutions available at the moment.</p>
                    )}
                </div>
            )}
            <div className="mt-6 pt-4 border-t border-border flex justify-end">
                <Button onClick={onClose} variant="primary">
                    Got it, thanks!
                </Button>
            </div>
        </Modal>
    );
};

export default BudgetSolutionModal; 