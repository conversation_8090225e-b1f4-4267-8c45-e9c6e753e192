import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import { SparklesIcon } from '@/src/components/icons/HeroIcons';
import { BudgetItem } from '@/src/types/index';
import { formatCurrency } from '@/src/utils/budgetUtils';

interface BudgetInsightsCardProps {
    overBudgetItems: BudgetItem[];
    clientCoupleNames: string;
    onAskForSolutions: (item: BudgetItem) => void;
    isFetchingBudgetSolutions: boolean;
    budgetSolutionFor: BudgetItem | null;
}

const BudgetInsightsCard: React.FC<BudgetInsightsCardProps> = ({
    overBudgetItems,
    clientCoupleNames,
    onAskForSolutions,
    isFetchingBudgetSolutions,
    budgetSolutionFor,
}) => {
    if (overBudgetItems.length === 0) return null;

    return (
        <Card title="<PERSON>'s Budget Insights for Client ✨" className="border-primary shadow-glow-primary">
            <div className="space-y-3">
                <p className="text-muted-foreground mb-2">
                    Ella has noticed some budget items for {clientCoupleNames} that are significantly over their estimates. Would you like her advice?
                </p>
                {overBudgetItems.map((item: BudgetItem) => (
                    <div key={item.id} className="p-3 bg-secondary/80 rounded-md flex justify-between items-center">
                        <div>
                            <p className="font-semibold text-foreground">{item.category}</p>
                            <p className="text-xs text-destructive">
                                Over by {formatCurrency(item.actualCost - item.estimatedCost)} (Est: {formatCurrency(item.estimatedCost)}, Actual: {formatCurrency(item.actualCost)})
                            </p>
                        </div>
                        <Button
                            onClick={() => onAskForSolutions(item)}
                            variant="secondary"
                            size="sm"
                            leftIcon={<SparklesIcon className="w-4 h-4" />}
                            isLoading={isFetchingBudgetSolutions && budgetSolutionFor?.id === item.id}
                        >
                            {isFetchingBudgetSolutions && budgetSolutionFor?.id === item.id ? "Thinking..." : "Get Advice"}
                        </Button>
                    </div>
                ))}
            </div>
        </Card>
    );
};

export default BudgetInsightsCard; 