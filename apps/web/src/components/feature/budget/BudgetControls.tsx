import Button from '@/src/components/common/Button';
import { PlusIcon, SparklesIcon } from '@/src/components/icons/HeroIcons';

interface BudgetControlsProps {
    showForm: boolean;
    onAddNew: () => void;
    onAISuggestion: () => void;
    isAISuggesting: boolean;
    autonomyButtonText: string;
    hasWeddingDetails: boolean;
}

const BudgetControls: React.FC<BudgetControlsProps> = ({
    showForm,
    onAddNew,
    onAISuggestion,
    isAISuggesting,
    autonomyButtonText,
    hasWeddingDetails,
}) => {
    return (
        <div className="flex space-x-3">
            {!showForm && (
                <Button
                    onClick={onAddNew}
                    leftIcon={<PlusIcon className="w-5 h-5" />}
                    variant="primary"
                >
                    Add New Item for Client
                </Button>
            )}
            <Button
                onClick={onAISuggestion}
                leftIcon={<SparklesIcon className="w-5 h-5" />}
                variant="secondary"
                isLoading={isAISuggesting}
                disabled={isAISuggesting || !hasWeddingDetails}
            >
                {isAISuggesting ? "Ella is Thinking..." : `${autonomyButtonText} for Client`}
            </Button>
        </div>
    );
};

export default BudgetControls;
