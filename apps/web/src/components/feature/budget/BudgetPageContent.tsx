import BudgetControls from '@/src/components/feature/budget/BudgetControls';
import BudgetForm from '@/src/components/feature/budget/BudgetForm';
import BudgetInsightsCard from '@/src/components/feature/budget/BudgetInsightsCard';
import BudgetSummary from '@/src/components/feature/budget/BudgetSummary';
import BudgetTable from '@/src/components/feature/budget/BudgetTable';
import { CrudItem } from '@/src/hooks/useCrudManager';
import { BudgetFormState, BudgetItemType, formatCurrency } from '@/src/utils/budgetUtils';

interface BudgetItemWithCrud extends BudgetItemType, CrudItem { }

interface BudgetPageContentProps {
    clientCoupleNames: string;
    overBudgetItems: BudgetItemType[];
    budgetTotals: {
        totalEstimated: number;
        totalActual: number;
        budgetDifference: number;
    };
    items: BudgetItemWithCrud[];
    showForm: boolean;
    editingItem: BudgetItemWithCrud | null;
    formState: BudgetFormState;
    formErrors: Record<string, string>;
    isAISuggesting: boolean;
    autonomyButtonText: string;
    hasWeddingDetails: boolean;
    isFetchingBudgetSolutions: boolean;
    budgetSolutionFor: BudgetItemType | null;
    onAddNew: () => void;
    onAISuggestion: () => void;
    onAskForSolutions: (item: BudgetItemType) => void;
    handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
    handleSubmit: (e: React.FormEvent) => void;
    handleCancel: () => void;
    handleEdit: (itemToEdit: BudgetItemWithCrud) => void;
    handleDelete: (id: string) => void;
}

const BudgetPageContent: React.FC<BudgetPageContentProps> = ({
    clientCoupleNames,
    overBudgetItems,
    budgetTotals,
    items,
    showForm,
    editingItem,
    formState,
    formErrors,
    isAISuggesting,
    autonomyButtonText,
    hasWeddingDetails,
    isFetchingBudgetSolutions,
    budgetSolutionFor,
    onAddNew,
    onAISuggestion,
    onAskForSolutions,
    handleInputChange,
    handleSubmit,
    handleCancel,
    handleEdit,
    handleDelete,
}) => {
    return (
        <>
            <BudgetInsightsCard
                overBudgetItems={overBudgetItems}
                clientCoupleNames={clientCoupleNames}
                onAskForSolutions={onAskForSolutions}
                isFetchingBudgetSolutions={isFetchingBudgetSolutions}
                budgetSolutionFor={budgetSolutionFor}
            />

            <BudgetSummary
                totalEstimated={budgetTotals.totalEstimated}
                totalActual={budgetTotals.totalActual}
                budgetDifference={budgetTotals.budgetDifference}
                formatCurrency={formatCurrency}
            />

            <BudgetControls
                showForm={showForm}
                onAddNew={onAddNew}
                onAISuggestion={onAISuggestion}
                isAISuggesting={isAISuggesting}
                autonomyButtonText={autonomyButtonText}
                hasWeddingDetails={hasWeddingDetails}
            />

            {showForm && (
                <BudgetForm
                    formState={formState}
                    editingItem={editingItem}
                    handleInputChange={handleInputChange}
                    handleSubmit={handleSubmit}
                    resetForm={handleCancel}
                    formErrors={formErrors}
                />
            )}

            <BudgetTable
                items={items}
                handleEditItem={handleEdit}
                handleDeleteItem={handleDelete}
                formatCurrency={formatCurrency}
                totalActual={budgetTotals.totalActual}
                totalEstimated={budgetTotals.totalEstimated}
            />
        </>
    );
};

export default BudgetPageContent; 