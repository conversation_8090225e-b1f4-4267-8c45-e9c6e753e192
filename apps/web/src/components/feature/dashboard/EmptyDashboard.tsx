import Button from '@/src/components/common/Button';
import { BriefcaseIcon, PlusIcon } from '@/src/components/icons/HeroIcons';
import Link from 'next/link';

const EmptyDashboard: React.FC = () => {
    return (
        <div className="text-center py-12">
            <BriefcaseIcon className="w-20 h-20 mx-auto text-muted-foreground mb-6" />
            <h3 className="text-2xl font-semibold text-foreground mb-3">Your Dashboard is Ready!</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Start organizing your client weddings by adding your first one. <PERSON>, your AI assistant, is here to help every step of the way.
            </p>
            <Link href="/add-wedding-client">
                <Button variant="primary" size="lg" leftIcon={<PlusIcon className="w-5 h-5" />} className="button-pulse-primary">
                    Add Your First Client Wedding
                </Button>
            </Link>
        </div>
    );
};

export default EmptyDashboard; 