import Button from '@/src/components/common/Button';
import { PlusIcon } from '@/src/components/icons/HeroIcons';
import { SortOption } from '@/src/utils/dashboardUtils';
import Link from 'next/link';

interface DashboardSortingProps {
    weddingCount: number;
    sortOption: SortOption;
    onSortChange: (option: SortOption) => void;
}

const DashboardSorting: React.FC<DashboardSortingProps> = ({
    weddingCount,
    sortOption,
    onSortChange
}) => {
    return (
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
            <h2 className="text-2xl font-semibold text-foreground">
                Your Client Weddings ({weddingCount})
            </h2>
            <div className="flex flex-col sm:flex-row items-center gap-4 w-full sm:w-auto">
                <div className="w-full sm:w-auto">
                    <label htmlFor="sortWeddings" className="sr-only">Sort Weddings</label>
                    <select
                        id="sortWeddings"
                        value={sortOption}
                        onChange={(e) => onSortChange(e.target.value as SortOption)}
                        className="w-full sm:w-auto px-4 py-2.5 rounded-md bg-input border border-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
                    >
                        <option value="createdAt-desc">Sort by: Date Added (Newest)</option>
                        <option value="createdAt-asc">Sort by: Date Added (Oldest)</option>
                        <option value="weddingDate-asc">Sort by: Wedding Date (Soonest)</option>
                        <option value="weddingDate-desc">Sort by: Wedding Date (Furthest)</option>
                        <option value="coupleName-asc">Sort by: Couple Name (A-Z)</option>
                        <option value="coupleName-desc">Sort by: Couple Name (Z-A)</option>
                        <option value="budget-desc">Sort by: Budget (Highest Est.)</option>
                        <option value="budget-asc">Sort by: Budget (Lowest Est.)</option>
                    </select>
                </div>
                <Link href="/add-wedding-client" className="w-full sm:w-auto">
                    <Button variant="primary" leftIcon={<PlusIcon className="w-5 h-5" />} className="w-full">
                        Add New Client
                    </Button>
                </Link>
            </div>
        </div>
    );
};

export default DashboardSorting; 