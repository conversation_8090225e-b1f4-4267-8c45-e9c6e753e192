import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import {
    CalendarDaysIcon,
    CheckCircleIcon,
    ClipboardDocumentIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    PencilIcon,
    TrashIcon
} from '@/src/components/icons/HeroIcons';
import { ManagedWedding, Task } from '@/src/types/index';
import { BudgetStatus, TaskProgress } from '@/src/utils/dashboardUtils';

interface WeddingCardProps {
    wedding: ManagedWedding;
    budget: BudgetStatus;
    tasks: TaskProgress;
    criticalTask: Task | null;
    onSelectWedding: (weddingId: string) => void;
    onEditWedding: (e: React.MouseEvent, weddingId: string) => void;
    onDeleteWedding: (e: React.MouseEvent, weddingId: string) => void;
}

const WeddingCard: React.FC<WeddingCardProps> = ({
    wedding,
    budget,
    tasks,
    criticalTask,
    onSelectWedding,
    onEditWedding,
    onDeleteWedding
}) => {
    return (
        <div
            className="cursor-pointer group h-full flex flex-col transition-all duration-300 ease-in-out hover:scale-[1.02]"
            onClick={() => onSelectWedding(wedding.id)}
            role="link"
            aria-label={`View details for ${wedding.weddingDetails.coupleNames || 'Untitled Wedding'}`}
            tabIndex={0}
            onKeyPress={(e) => { if (e.key === 'Enter') onSelectWedding(wedding.id); }}
        >
            <Card className="!p-0 flex flex-col justify-between flex-grow group-hover:shadow-glow-primary transition-shadow duration-300">
                <div className="p-5">
                    <h3 className="text-xl font-semibold text-primary mb-2 truncate group-hover:text-primary/80 transition-colors">
                        {wedding.weddingDetails.coupleNames || 'Untitled Wedding'}
                    </h3>
                    <div className="flex items-center text-sm text-muted-foreground mb-1">
                        <CalendarDaysIcon className="w-4 h-4 mr-2 text-primary/70 flex-shrink-0" />
                        <span>
                            {wedding.weddingDetails.weddingDate
                                ? new Date(wedding.weddingDetails.weddingDate).toLocaleDateString(undefined, {
                                    year: 'numeric',
                                    month: 'long',
                                    day: 'numeric'
                                })
                                : 'Date TBD'
                            }
                        </span>
                    </div>
                    <p className="text-xs text-muted-foreground/80 mb-4 truncate" title={`Vibe: ${wedding.weddingDetails.vibe || 'Not set'}`}>
                        Vibe: {wedding.weddingDetails.vibe || 'Not set'}
                    </p>

                    <div className="space-y-3 text-sm">
                        {/* Budget Status */}
                        <div className="flex items-center" title={`Budget Status: ${budget.status}`}>
                            <budget.Icon className={`w-5 h-5 mr-2 ${budget.color} flex-shrink-0`} />
                            <span className={`font-medium ${budget.color}`}>{budget.text}</span>
                        </div>

                        {/* Task Progress */}
                        <div className="flex items-center" title="Task Progress">
                            <ClipboardDocumentIcon className="w-5 h-5 mr-2 text-muted-foreground flex-shrink-0" />
                            <span className="text-muted-foreground mr-2">{tasks.text}</span>
                            {tasks.total > 0 && !tasks.allDone && (
                                <div className="flex-grow h-2.5 bg-muted rounded-full overflow-hidden relative top-px" style={{ minWidth: '60px' }}>
                                    <div
                                        className="h-full bg-primary transition-all duration-500 ease-out"
                                        style={{ width: `${tasks.percentage}%` }}
                                        aria-valuenow={tasks.percentage}
                                        aria-valuemin={0}
                                        aria-valuemax={100}
                                        role="progressbar"
                                        aria-label={`Task completion: ${tasks.percentage.toFixed(0)}%`}
                                    ></div>
                                </div>
                            )}
                        </div>

                        {/* Critical Task or Status */}
                        {criticalTask && (
                            <div className="flex items-start" title={`Next Critical Task: ${criticalTask.title}`}>
                                <ExclamationTriangleIcon className="w-5 h-5 mr-2 text-amber-500 mt-px flex-shrink-0" />
                                <div className="text-amber-500">
                                    <span className="font-medium truncate block">{criticalTask.title}</span>
                                    {criticalTask.dueDate && (
                                        <span className="text-xs">
                                            Due: {new Date(criticalTask.dueDate).toLocaleDateString()}
                                        </span>
                                    )}
                                </div>
                            </div>
                        )}

                        {!criticalTask && tasks.total > 0 && tasks.allDone && (
                            <div className="flex items-center">
                                <CheckCircleIcon className="w-5 h-5 mr-2 text-positive flex-shrink-0" />
                                <span className="text-positive font-medium">All tasks complete!</span>
                            </div>
                        )}

                        {!criticalTask && tasks.total === 0 && (
                            <div className="flex items-center">
                                <InformationCircleIcon className="w-5 h-5 mr-2 text-muted-foreground flex-shrink-0" />
                                <span className="text-muted-foreground">No tasks yet.</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Footer Actions */}
                <div className="border-t border-border p-3 bg-secondary/30 flex justify-end space-x-2 mt-auto">
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => onEditWedding(e, wedding.id)}
                        className="!p-1.5 text-muted-foreground hover:text-primary"
                        title="Edit Client Details"
                        aria-label={`Edit details for ${wedding.weddingDetails.coupleNames || 'Untitled Wedding'}`}
                    >
                        <PencilIcon className="w-4 h-4" />
                    </Button>
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => onDeleteWedding(e, wedding.id)}
                        className="!p-1.5 text-muted-foreground hover:text-destructive"
                        title="Delete Wedding"
                        aria-label={`Delete wedding for ${wedding.weddingDetails.coupleNames || 'Untitled Wedding'}`}
                    >
                        <TrashIcon className="w-4 h-4" />
                    </Button>
                </div>
            </Card>
        </div>
    );
};

export default WeddingCard; 