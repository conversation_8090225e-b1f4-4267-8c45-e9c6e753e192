"use client";

import { PREDEFINED_AGENTS } from '@/src/constants';
import { useToast } from '@/src/contexts/ToastContext';
import { AgentConfig, AIAgentLogEntry, AppMode, BudgetItem, ChatMessage, DataSource, EllaAutonomyLevel, ManagedWedding, PlannerContextType, PlannerProfile, Task } from '@/src/types/index';
import { generateClientSideId } from '@/src/utils/idUtils';
import React, { createContext, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';

export const PlannerContext = createContext<PlannerContextType | undefined>(undefined);

const DEFAULT_YEARS_EXPERIENCE = '0-2 years';
const DEFAULT_PLANNER_PROFILE_BASE: PlannerProfile = {
  name: '',
  email: '',
  companyName: '',
  website: '',
  yearsExperience: DEFAULT_YEARS_EXPERIENCE,
  specialties: '',
  serviceArea: '',
  tagline: '',
  bio: '',
  servicesOffered: [],
  pricingStructure: '',
  typicalClientBudget: '',
  socialMediaLinks: [],
};


export const PlannerProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { addToast } = useToast();

  // Helper function to safely access localStorage
  const getFromLocalStorage = (key: string, fallback: any = null) => {
    if (typeof window === 'undefined') return fallback;
    try {
      const item = window.localStorage.getItem(key);
      if (!item) return fallback;

      // Handle special cases for simple string values that shouldn't be JSON parsed
      if (key === 'sayyes_app_mode' || key === 'sayyes_ella_autonomy_level' || key === 'sayyes_gemini_api_key') {
        // These are simple string values, return as-is if they match expected values
        if (key === 'sayyes_app_mode' && (item === 'dev' || item === 'prod')) return item;
        if (key === 'sayyes_ella_autonomy_level' && (item === 'conservative' || item === 'balanced' || item === 'aggressive')) return item;
        if (key === 'sayyes_gemini_api_key') return item; // API keys are strings
      }

      // For other values, try JSON parsing
      return JSON.parse(item);
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
      // If JSON parsing fails, return the raw string for simple values
      const item = window.localStorage.getItem(key);
      if (item && (key === 'sayyes_app_mode' || key === 'sayyes_ella_autonomy_level' || key === 'sayyes_gemini_api_key')) {
        return item;
      }
      return fallback;
    }
  };

  // Helper function to safely set localStorage
  const setToLocalStorage = (key: string, value: any) => {
    if (typeof window === 'undefined') return;
    try {
      // Handle special cases for simple string values
      if (key === 'sayyes_app_mode' || key === 'sayyes_ella_autonomy_level' || key === 'sayyes_gemini_api_key') {
        window.localStorage.setItem(key, value);
      } else {
        window.localStorage.setItem(key, JSON.stringify(value));
      }
    } catch (error) {
      console.error(`Error setting ${key} to localStorage:`, error);
    }
  };

  const [isPlannerOnboarded, setIsPlannerOnboardedState] = useState<boolean>(() => {
    return getFromLocalStorage('sayyes_planner_onboarded', false);
  });

  const [plannerProfile, setPlannerProfileState] = useState<PlannerProfile | null>(() => {
    const stored = getFromLocalStorage('sayyes_planner_profile', null);
    if (stored && typeof stored === 'object' && stored !== null) {
      const loadedProfile = { ...DEFAULT_PLANNER_PROFILE_BASE, ...stored };
      loadedProfile.servicesOffered = Array.isArray(stored.servicesOffered) ? stored.servicesOffered.filter((s: any) => typeof s === 'string') : [];
      loadedProfile.socialMediaLinks = Array.isArray(stored.socialMediaLinks) ? stored.socialMediaLinks.filter((link: any) => link && typeof link.platform === 'string' && typeof link.url === 'string') : [];
      return loadedProfile as PlannerProfile;
    }
    return null;
  });

  const [managedWeddings, setManagedWeddingsState] = useState<ManagedWedding[]>(() => {
    const stored = getFromLocalStorage('sayyes_managed_weddings', []);
    if (Array.isArray(stored)) {
      return stored.map((w: any): ManagedWedding => {
        const weddingDetails = w.weddingDetails || {};
        const defaultDate = new Date().toISOString();

        const loadedBudgetItems = Array.isArray(w.budgetItems) ? w.budgetItems : [];
        const sanitizedBudgetItems: BudgetItem[] = loadedBudgetItems.map((bi: any): BudgetItem => ({
          id: typeof bi.id === 'string' ? bi.id : generateClientSideId(),
          category: typeof bi.category === 'string' ? bi.category : 'Unknown Category',
          estimatedCost: typeof bi.estimatedCost === 'number' ? bi.estimatedCost : 0,
          actualCost: typeof bi.actualCost === 'number' ? bi.actualCost : 0,
          vendor: typeof bi.vendor === 'string' ? bi.vendor : '',
          paymentDueDate: typeof bi.paymentDueDate === 'string' ? bi.paymentDueDate : '',
          isPaid: typeof bi.isPaid === 'boolean' ? bi.isPaid : false,
        }));

        return {
          id: w.id || generateClientSideId(),
          weddingDetails: {
            coupleNames: typeof weddingDetails.coupleNames === 'string' ? weddingDetails.coupleNames : 'Unnamed Couple',
            weddingDate: typeof weddingDetails.weddingDate === 'string' && weddingDetails.weddingDate ? new Date(weddingDetails.weddingDate).toISOString().split('T')[0] : '',
            guestCount: typeof weddingDetails.guestCount === 'number' ? weddingDetails.guestCount : 0,
            vibe: typeof weddingDetails.vibe === 'string' ? weddingDetails.vibe : 'Not set',
            location: typeof weddingDetails.location === 'string' ? weddingDetails.location : 'Not set',
            initialBudget: typeof weddingDetails.initialBudget === 'number' ? weddingDetails.initialBudget : 0,
          },
          chatHistory: Array.isArray(w.chatHistory) ? w.chatHistory.map((msg: any) => ({ ...msg, timestamp: new Date(msg.timestamp || defaultDate) })) : [],
          budgetItems: sanitizedBudgetItems,
          tasks: Array.isArray(w.tasks) ? w.tasks : [],
          guests: Array.isArray(w.guests) ? w.guests : [],
          vendors: Array.isArray(w.vendors) ? w.vendors : [],
          visionBoardItems: Array.isArray(w.visionBoardItems) ? w.visionBoardItems : [],
          agentLogs: Array.isArray(w.agentLogs) ? w.agentLogs.map((log: any) => ({ ...log, timestamp: new Date(log.timestamp || defaultDate) })) : [],
          isAIPopulated: typeof w.isAIPopulated === 'boolean' ? w.isAIPopulated : false,
          createdAt: typeof w.createdAt === 'string' ? w.createdAt : defaultDate,
        };
      });
    }
    return [];
  });

  const [currentWeddingId, setCurrentWeddingIdState] = useState<string | null>(() => {
    return getFromLocalStorage('sayyes_current_wedding_id', null);
  });

  const [appMode, setAppModeState] = useState<AppMode>(() => {
    return getFromLocalStorage('sayyes_app_mode', 'dev');
  });

  const [ellaAutonomyLevel, setEllaAutonomyLevelState] = useState<EllaAutonomyLevel>(() => {
    return getFromLocalStorage('sayyes_ella_autonomy_level', 'balanced');
  });

  const [geminiApiKey, setGeminiApiKeyState] = useState<string>(() => {
    return getFromLocalStorage('sayyes_gemini_api_key', '');
  });

  const [agentConfigurations, setAgentConfigurationsState] = useState<AgentConfig[]>(() => {
    const storedConfigs = getFromLocalStorage('sayyes_agent_configurations', {});
    return PREDEFINED_AGENTS.map(predefinedAgent => {
      const storedAgentConfig = storedConfigs[predefinedAgent.id] || {};
      return {
        ...predefinedAgent,
        isActiveGlobal: storedAgentConfig.isActiveGlobal === undefined ? true : storedAgentConfig.isActiveGlobal,
        llmModel: storedAgentConfig.llmModel || predefinedAgent.defaultLlmModel,
        defaultSystemInstruction: storedAgentConfig.defaultSystemInstruction || predefinedAgent.defaultSystemInstruction,
        weddingOverrides: storedAgentConfig.weddingOverrides || {},
        connectedDataSourceIds: Array.isArray(storedAgentConfig.connectedDataSourceIds) ? storedAgentConfig.connectedDataSourceIds : [],
      };
    });
  });

  const [dataSources, setDataSourcesState] = useState<DataSource[]>(() => {
    const stored = getFromLocalStorage('sayyes_data_sources', []);
    return Array.isArray(stored) ? stored : [];
  });

  // Update localStorage effects to use the safe helper
  useEffect(() => { setToLocalStorage('sayyes_planner_onboarded', isPlannerOnboarded); }, [isPlannerOnboarded]);
  useEffect(() => {
    if (plannerProfile === null) {
      setToLocalStorage('sayyes_planner_profile', null);
    } else {
      const profileToSave: PlannerProfile = {
        ...DEFAULT_PLANNER_PROFILE_BASE,
        ...plannerProfile,
        servicesOffered: Array.isArray(plannerProfile.servicesOffered) ? plannerProfile.servicesOffered.filter(s => typeof s === 'string') : [],
        socialMediaLinks: Array.isArray(plannerProfile.socialMediaLinks) ? plannerProfile.socialMediaLinks.filter(link => link && typeof link.platform === 'string' && typeof link.url === 'string') : [],
      };
      setToLocalStorage('sayyes_planner_profile', profileToSave);
    }
  }, [plannerProfile]);
  useEffect(() => { setToLocalStorage('sayyes_managed_weddings', managedWeddings); }, [managedWeddings]);
  useEffect(() => {
    if (currentWeddingId) {
      setToLocalStorage('sayyes_current_wedding_id', currentWeddingId);
    } else if (typeof window !== 'undefined') {
      window.localStorage.removeItem('sayyes_current_wedding_id');
    }
  }, [currentWeddingId]);
  useEffect(() => { setToLocalStorage('sayyes_app_mode', appMode); }, [appMode]);
  useEffect(() => { setToLocalStorage('sayyes_ella_autonomy_level', ellaAutonomyLevel); }, [ellaAutonomyLevel]);
  useEffect(() => {
    if (geminiApiKey) {
      setToLocalStorage('sayyes_gemini_api_key', geminiApiKey);
    } else if (typeof window !== 'undefined') {
      window.localStorage.removeItem('sayyes_gemini_api_key');
    }
  }, [geminiApiKey]);

  useEffect(() => {
    const configsToStore = agentConfigurations.reduce((acc, config) => {
      acc[config.id] = {
        isActiveGlobal: config.isActiveGlobal,
        llmModel: config.llmModel,
        defaultSystemInstruction: config.defaultSystemInstruction,
        weddingOverrides: config.weddingOverrides,
        connectedDataSourceIds: Array.isArray(config.connectedDataSourceIds) ? config.connectedDataSourceIds : [],
      };
      return acc;
    }, {} as { [agentId: string]: Partial<Omit<AgentConfig, 'id' | 'name' | 'description' | 'icon' | 'agentType' | 'parentId' | 'defaultLlmModel'>> & { connectedDataSourceIds?: string[] } });
    setToLocalStorage('sayyes_agent_configurations', configsToStore);
  }, [agentConfigurations]);

  useEffect(() => {
    setToLocalStorage('sayyes_data_sources', dataSources);
  }, [dataSources]);


  const setIsPlannerOnboarded = useCallback((status: boolean) => setIsPlannerOnboardedState(status), []);

  const setPlannerProfile = useCallback((profile: PlannerProfile | null) => {
    if (profile === null) {
      setPlannerProfileState(null);
    } else {
      setPlannerProfileState(prev => ({
        ...DEFAULT_PLANNER_PROFILE_BASE,
        ...prev,
        ...profile,
        servicesOffered: Array.isArray(profile.servicesOffered) ? profile.servicesOffered.filter(s => typeof s === 'string') : [],
        socialMediaLinks: Array.isArray(profile.socialMediaLinks) ? profile.socialMediaLinks.filter(link => link && typeof link.platform === 'string' && typeof link.url === 'string') : [],
      }));
    }
  }, []);

  const setCurrentWeddingId = useCallback((weddingId: string | null) => setCurrentWeddingIdState(weddingId), []);

  const setEllaAutonomyLevel = useCallback((level: EllaAutonomyLevel) => {
    setEllaAutonomyLevelState(level);
    addToast(`Ella's autonomy level set to: ${level}.`, "info");
  }, [addToast]);

  const setGeminiApiKey = useCallback((key: string) => {
    setGeminiApiKeyState(key);
    if (key) {
      addToast("Gemini API key configured successfully. AI features will now use your personal key.", "success");
    } else {
      addToast("Gemini API key removed. AI features will fall back to environment variables.", "info");
    }
  }, [addToast]);

  const setAppMode = useCallback((mode: AppMode) => {
    setAppModeState(mode);
    addToast(`App mode switched to ${mode.toUpperCase()}. AI data will refresh.`, "info");
    setManagedWeddingsState(prev => prev.map(w => ({ ...w, isAIPopulated: false, budgetItems: w.budgetItems || [], tasks: w.tasks || [] })));
  }, [addToast]);

  const addManagedWedding = useCallback((newWeddingClientData: Pick<ManagedWedding, 'weddingDetails'>): ManagedWedding => {
    const newWedding: ManagedWedding = {
      id: generateClientSideId(),
      weddingDetails: newWeddingClientData.weddingDetails,
      chatHistory: [],
      budgetItems: [],
      tasks: [],
      guests: [],
      vendors: [],
      visionBoardItems: [],
      agentLogs: [],
      isAIPopulated: false,
      createdAt: new Date().toISOString(),
    };
    setManagedWeddingsState(prev => [...prev, newWedding]);
    return newWedding;
  }, []);

  const updateManagedWedding = useCallback((weddingId: string, updates: Partial<ManagedWedding>) => {
    let weddingName = 'Client Wedding';
    setManagedWeddingsState(prev =>
      prev.map(w => {
        if (w.id === weddingId) {
          weddingName = w.weddingDetails.coupleNames || weddingName;
          const shouldResetAIPopulation = updates.weddingDetails && JSON.stringify(updates.weddingDetails) !== JSON.stringify(w.weddingDetails);
          if (shouldResetAIPopulation) {
            addToast(`AI data will be refreshed for ${weddingName} due to detail changes.`, "info", 7000);
          }
          return {
            ...w,
            ...updates,
            isAIPopulated: shouldResetAIPopulation ? false : w.isAIPopulated
          };
        }
        return w;
      })
    );
  }, [addToast]);

  const deleteManagedWedding = useCallback((weddingId: string) => {
    console.log("[PlannerContext] deleteManagedWedding called for ID:", weddingId);
    console.log("[PlannerContext] current managedWeddings (from closure):", managedWeddings.map(w => ({ id: w.id, name: w.weddingDetails.coupleNames })));

    const weddingToDelete = managedWeddings.find(w => w.id === weddingId);
    const weddingName = weddingToDelete?.weddingDetails.coupleNames || 'Client Wedding';
    console.log("[PlannerContext] Wedding name for toast:", weddingName);

    setManagedWeddingsState(prev => {
      console.log("[PlannerContext] setManagedWeddingsState: prev IDs:", prev.map(w => ({ id: w.id, name: w.weddingDetails.coupleNames })));
      const filtered = prev.filter(w => w.id !== weddingId);
      console.log("[PlannerContext] setManagedWeddingsState: filtered IDs:", filtered.map(w => ({ id: w.id, name: w.weddingDetails.coupleNames })));
      if (prev.length === filtered.length && prev.some(w => w.id === weddingId)) {
        console.warn("[PlannerContext] Filter did not remove any items. WeddingId to delete:", weddingId, "Items present:", prev.map(w => w.id));
      } else if (prev.length !== filtered.length) {
        console.log("[PlannerContext] Item successfully filtered.");
      }
      return filtered;
    });

    if (currentWeddingId === weddingId) {
      console.log("[PlannerContext] Clearing currentWeddingId as it was deleted.");
      setCurrentWeddingIdState(null);
    }
    addToast(`${weddingName} deleted successfully.`, "success");
  }, [currentWeddingId, addToast, managedWeddings]);

  const currentWedding = useMemo(() => managedWeddings.find(w => w.id === currentWeddingId) || null, [managedWeddings, currentWeddingId]);

  const updateDataForCurrentWedding = useCallback(<K extends keyof ManagedWedding>(key: K, data: ManagedWedding[K]) => {
    if (currentWeddingId) {
      setManagedWeddingsState(prev =>
        prev.map(w => {
          if (w.id === currentWeddingId) {
            let finalAIPopulatedStatus = w.isAIPopulated; // Default to current value
            let finalData = data;

            // Clean up chat history when it's being updated - for now, skip cleanup to avoid require()
            // TODO: Fix this to use proper ES6 imports at the module level
            if (key === 'chatHistory' && Array.isArray(data)) {
              finalData = data; // Skip cleanup for now
            }

            if (key === 'weddingDetails' && JSON.stringify(finalData) !== JSON.stringify(w.weddingDetails)) {
              addToast(`AI data will be refreshed for ${w.weddingDetails.coupleNames} due to detail changes.`, "info", 7000);
              finalAIPopulatedStatus = false; // Reset if weddingDetails change significantly
            } else if (key === 'isAIPopulated') {
              finalAIPopulatedStatus = finalData as boolean; // Directly use the new status for isAIPopulated
            }
            // For other keys, isAIPopulated status is maintained unless weddingDetails changed.

            return {
              ...w,
              [key]: finalData, // Set the primary key-value pair
              isAIPopulated: finalAIPopulatedStatus // Correctly set isAIPopulated based on above logic
            };
          }
          return w;
        })
      );
    }
  }, [currentWeddingId, addToast]);

  const addChatMessageToCurrentWedding = useCallback((message: ChatMessage) => {
    if (currentWeddingId) {
      const wedding = managedWeddings.find(w => w.id === currentWeddingId);
      if (wedding) {
        // TODO: Fix this to use proper ES6 imports at the module level
        // For now, just add the message directly without deduplication
        const currentChatHistory = wedding.chatHistory || [];
        const existingMessage = currentChatHistory.find(msg => msg.id === message.id);

        if (!existingMessage) {
          setManagedWeddingsState(prevWeddings => prevWeddings.map(w =>
            w.id === currentWeddingId ? { ...w, chatHistory: [...currentChatHistory, message] } : w
          ));
        }
      }
    }
  }, [currentWeddingId, managedWeddings]);

  const clearChatHistoryForCurrentWedding = useCallback(() => {
    if (currentWeddingId) {
      updateDataForCurrentWedding('chatHistory', []);
      addToast("Chat history cleared for current wedding.", "info");
    }
  }, [currentWeddingId, updateDataForCurrentWedding, addToast]);

  const setInitialAIBudgetForCurrentWedding = useCallback((items: BudgetItem[]) => {
    if (currentWeddingId) {
      updateDataForCurrentWedding('budgetItems', items);
    }
  }, [currentWeddingId, updateDataForCurrentWedding]);

  const setInitialAITimelineForCurrentWedding = useCallback((items: Task[]) => {
    if (currentWeddingId) {
      updateDataForCurrentWedding('tasks', items);
    }
  }, [currentWeddingId, updateDataForCurrentWedding]);

  const updateAIPopulatedStatusForCurrentWedding = useCallback((status: boolean) => {
    if (currentWeddingId) {
      updateDataForCurrentWedding('isAIPopulated', status);
    }
  }, [currentWeddingId, updateDataForCurrentWedding]);

  const addAIAgentLogToCurrentWedding = useCallback((logEntry: AIAgentLogEntry) => {
    if (currentWeddingId) {
      const wedding = managedWeddings.find(w => w.id === currentWeddingId);
      if (wedding) {
        setManagedWeddingsState(prevWeddings => prevWeddings.map(w =>
          w.id === currentWeddingId ? { ...w, agentLogs: [logEntry, ...(w.agentLogs || []).slice(0, 19)] } : w
        ));
      }
    }
  }, [currentWeddingId, managedWeddings]);

  const updateAgentConfiguration = useCallback((agentId: string, configUpdate: Partial<Omit<AgentConfig, 'id' | 'name' | 'description' | 'icon' | 'agentType' | 'parentId' | 'defaultSystemInstruction' | 'defaultLlmModel' | 'connectedDataSourceIds'>> & { connectedDataSourceIds?: string[] }) => {
    setAgentConfigurationsState(prevConfigs =>
      prevConfigs.map(config =>
        config.id === agentId
          ? { ...config, ...configUpdate }
          : config
      )
    );
    const agentName = PREDEFINED_AGENTS.find(a => a.id === agentId)?.name || 'Agent';
    addToast(`${agentName} configuration updated.`, 'success');
  }, [addToast]);

  const getAgentConfig = useCallback((agentId: string): AgentConfig | undefined => {
    return agentConfigurations.find(config => config.id === agentId);
  }, [agentConfigurations]);

  const addDataSource = useCallback(async (dataSourceInput: Omit<DataSource, 'id' | 'uploadDate' | 'status'>): Promise<DataSource> => {
    const newDataSource: DataSource = {
      ...dataSourceInput,
      id: generateClientSideId(),
      uploadDate: new Date().toISOString(),
      status: 'Ready',
    };

    if (dataSourceInput.type === 'txt' && dataSourceInput.fullContent) {
      newDataSource.contentPreview = dataSourceInput.fullContent.substring(0, 200) + (dataSourceInput.fullContent.length > 200 ? '...' : '');
    } else if (dataSourceInput.type === 'text_input' && dataSourceInput.fullContent) {
      newDataSource.contentPreview = dataSourceInput.fullContent.substring(0, 200) + (dataSourceInput.fullContent.length > 200 ? '...' : '');
    }
    setDataSourcesState(prev => [...prev, newDataSource]);
    addToast(`Data source "${newDataSource.name}" added.`, 'success');
    return newDataSource;
  }, [addToast]);

  const deleteDataSource = useCallback((dataSourceId: string) => {
    const sourceName = dataSources.find(ds => ds.id === dataSourceId)?.name || "Data source";
    setDataSourcesState(prev => prev.filter(ds => ds.id !== dataSourceId));
    setAgentConfigurationsState(prevConfigs =>
      prevConfigs.map(agent => ({
        ...agent,
        connectedDataSourceIds: agent.connectedDataSourceIds.filter(id => id !== dataSourceId)
      }))
    );
    addToast(`"${sourceName}" deleted.`, 'success');
  }, [addToast, dataSources]);


  const contextValue = useMemo(() => ({
    isPlannerOnboarded, setIsPlannerOnboarded,
    plannerProfile, setPlannerProfile,
    managedWeddings, addManagedWedding, updateManagedWedding, deleteManagedWedding,
    currentWeddingId, setCurrentWeddingId,
    currentWedding,
    appMode, setAppMode,
    ellaAutonomyLevel, setEllaAutonomyLevel,
    geminiApiKey, setGeminiApiKey,
    addChatMessageToCurrentWedding,
    clearChatHistoryForCurrentWedding,
    setInitialAIBudgetForCurrentWedding,
    setInitialAITimelineForCurrentWedding,
    updateAIPopulatedStatusForCurrentWedding,
    addAIAgentLogToCurrentWedding,
    updateDataForCurrentWedding,
    agentConfigurations, updateAgentConfiguration, getAgentConfig,
    dataSources, addDataSource, deleteDataSource,
    // Workforce-related placeholders (to be implemented)
    workforceTemplates: [],
    addWorkforceTemplate: () => ({ id: '', name: '', description: '', category: 'basic' as const, defaultAgents: [], defaultGlobalSettings: { autonomyLevel: 'balanced' as const, performanceThresholds: { minSuccessRate: 0.9, maxResponseTime: 5000 } }, recommendedFor: [], estimatedMonthlyCost: 0, isPublic: false }),
    updateWorkforceTemplate: () => { },
    deleteWorkforceTemplate: () => { },
    updateWorkforceConfigForCurrentWedding: () => { },
    getWorkforceConfigForWedding: () => null,
    updateAgentPerformanceMetrics: () => { },
    getPerformanceMetricsForWedding: () => ({}),
  }), [
    isPlannerOnboarded, setIsPlannerOnboarded,
    plannerProfile, setPlannerProfile,
    managedWeddings, addManagedWedding, updateManagedWedding, deleteManagedWedding,
    currentWeddingId, setCurrentWeddingId,
    currentWedding,
    appMode, setAppMode,
    ellaAutonomyLevel, setEllaAutonomyLevel,
    geminiApiKey, setGeminiApiKey,
    addChatMessageToCurrentWedding,
    clearChatHistoryForCurrentWedding,
    setInitialAIBudgetForCurrentWedding,
    setInitialAITimelineForCurrentWedding,
    updateAIPopulatedStatusForCurrentWedding,
    addAIAgentLogToCurrentWedding,
    updateDataForCurrentWedding,
    agentConfigurations, updateAgentConfiguration, getAgentConfig,
    dataSources, addDataSource, deleteDataSource,
  ]);

  return (
    <PlannerContext.Provider value={contextValue}>
      {children}
    </PlannerContext.Provider>
  );
};

// Hook to use the wedding/planner context
export const useWedding = () => {
  const context = React.useContext(PlannerContext);
  if (context === undefined) {
    throw new Error('useWedding must be used within a PlannerProvider');
  }

  // Create a simplified interface for the agent system
  return {
    wedding: context.currentWedding ? {
      id: context.currentWedding.id,
      details: context.currentWedding.weddingDetails,
      budgetItems: context.currentWedding.budgetItems,
      tasks: context.currentWedding.tasks,
      guests: context.currentWedding.guests,
      vendors: context.currentWedding.vendors
    } : null,
    user: context.plannerProfile ? {
      id: 'planner-user', // Simplified user ID for agent system
      name: context.plannerProfile.name,
      email: context.plannerProfile.email
    } : null,
    // Expose the full context for backward compatibility
    ...context
  };
};
