import {
  createBudgetItemFromSuggestion,
  handleAISuggestionDismiss,
  handleAISuggestionSuccess,
} from "@/src/utils/budgetPageUtils"
import { BudgetFormState } from "@/src/utils/budgetUtils"
import { useState } from "react"

export const useBudgetAISuggestion = (
  setBudgetItems: (updater: (prev: any[]) => any[]) => void,
  addToast: (message: string, type: any) => void
) => {
  const [showAISuggestionConfirmModal, setShowAISuggestionConfirmModal] =
    useState(false)
  const [aiSuggestedItemForReview, setAISuggestedItemForReview] =
    useState<BudgetFormState | null>(null)

  const handleReviewSuggestion = (item: BudgetFormState) => {
    setAISuggestedItemForReview(item)
    setShowAISuggestionConfirmModal(true)
  }

  const handleConfirmAISuggestion = () => {
    if (aiSuggestedItemForReview) {
      const newItemToAdd = createBudgetItemFromSuggestion(
        aiSuggestedItemForReview
      )
      setBudgetItems((prev) => [...prev, newItemToAdd])
      handleAISuggestionSuccess(
        aiSuggestedItemForReview,
        newItemToAdd.category,
        addToast
      )
    }
    setShowAISuggestionConfirmModal(false)
    setAISuggestedItemForReview(null)
  }

  const handleDismissAISuggestion = () => {
    setShowAISuggestionConfirmModal(false)
    setAISuggestedItemForReview(null)
    handleAISuggestionDismiss(addToast)
  }

  const closeAISuggestionModal = () => {
    setShowAISuggestionConfirmModal(false)
  }

  return {
    showAISuggestionConfirmModal,
    aiSuggestedItemForReview,
    handleReviewSuggestion,
    handleConfirmAISuggestion,
    handleDismissAISuggestion,
    closeAISuggestionModal,
  }
}
