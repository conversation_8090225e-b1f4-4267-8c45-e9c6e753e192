import { PlannerContext } from "@/src/contexts/WeddingContext"
import { AppMode } from "@/src/types/index"
import { useContext } from "react"

/**
 * Hook to get the current app mode for AI generation
 * In production, this would check for API keys and other conditions
 * For now, it defaults to 'dev' mode which uses fallback generation
 */
export const useAppMode = (): AppMode => {
  const plannerCtx = useContext(PlannerContext)

  // For now, always return 'dev' mode since we're using fallback generation
  // In the future, this could check for:
  // - API key availability
  // - User subscription level
  // - Environment variables
  // - Feature flags

  return "dev"
}

export default useAppMode
