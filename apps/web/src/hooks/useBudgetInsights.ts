import { ellaSuggestBudgetSolutions } from "@/src/services/ai/index"
import { AppMode, BudgetItem, WeddingDetails } from "@/src/types/index"
import { BudgetItemType } from "@/src/utils/budgetUtils"
import { useState } from "react"

export const useBudgetInsights = (
  addToast: (
    message: string,
    type: "success" | "error" | "info" | "warning",
    duration?: number
  ) => void
) => {
  const [showBudgetSolutionModal, setShowBudgetSolutionModal] = useState(false)
  const [budgetSolutionFor, setBudgetSolutionFor] = useState<BudgetItem | null>(
    null
  )
  const [budgetAISolutions, setBudgetAISolutions] = useState<string | null>(
    null
  )
  const [isFetchingBudgetSolutions, setIsFetchingBudgetSolutions] =
    useState(false)

  const handleAskEllaForSolutions = async (
    item: BudgetItem,
    allItems: BudgetItemType[],
    weddingDetails: WeddingDetails,
    appMode: AppMode
  ) => {
    setBudgetSolutionFor(item)
    setIsFetchingBudgetSolutions(true)
    setShowBudgetSolutionModal(true)
    addToast(`Ella is analyzing ${item.category} for solutions...`, "info")

    try {
      const solutions = await ellaSuggestBudgetSolutions(
        item,
        allItems,
        weddingDetails,
        appMode
      )
      setBudgetAISolutions(solutions)
      addToast(`Ella has some advice for ${item.category}.`, "success")
    } catch (error) {
      console.error("Error fetching budget solutions:", error)
      setBudgetAISolutions(
        "Ella had a little trouble finding solutions. Please try again in a moment."
      )
      addToast(`Error getting advice for ${item.category}.`, "error")
    } finally {
      setIsFetchingBudgetSolutions(false)
    }
  }

  const closeBudgetSolutionModal = () => {
    setShowBudgetSolutionModal(false)
    setBudgetAISolutions(null)
    setBudgetSolutionFor(null)
  }

  return {
    // State
    showBudgetSolutionModal,
    budgetSolutionFor,
    budgetAISolutions,
    isFetchingBudgetSolutions,

    // Actions
    handleAskEllaForSolutions,
    closeBudgetSolutionModal,
  }
}
