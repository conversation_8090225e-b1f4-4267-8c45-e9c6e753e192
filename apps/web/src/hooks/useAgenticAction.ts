// src/hooks/useAgenticAction.ts
import { CrewAgenticService } from "@/src/services/crewAgenticService"
import { useState } from "react"
import toast from "react-hot-toast"

export function useAgenticAction() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [result, setResult] = useState<any>(null)

  async function runAgenticAction(
    action: string,
    domain: string,
    payload: any
  ) {
    setLoading(true)
    setError(null)
    setResult(null)
    const toastId = toast.loading("Ella is working on your request...")

    try {
      const response = await CrewAgenticService.executeAgenticAction({
        action,
        domain,
        payload,
      })

      setResult(response.result)
      toast.success("Done! Ella completed your request.", { id: toastId })
      return response.result
    } catch (e: any) {
      setError(e.message)
      toast.error("Oops! Something went wrong.", { id: toastId })
    } finally {
      setLoading(false)
    }
  }

  async function getCrewStatus() {
    try {
      return await CrewAgenticService.getCrewStatus()
    } catch (e: any) {
      console.error("Failed to get crew status:", e)
      return null
    }
  }

  async function getDomainCapabilities(domain: string) {
    try {
      return await CrewAgenticService.getDomainCapabilities(domain)
    } catch (e: any) {
      console.error(`Failed to get capabilities for ${domain}:`, e)
      return null
    }
  }

  return {
    runAgenticAction,
    getCrewStatus,
    getDomainCapabilities,
    loading,
    error,
    result,
  }
}
