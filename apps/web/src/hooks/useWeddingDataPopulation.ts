import {
  ellaGenerateInitialTimeline,
  ellaSuggestInitialBudget,
} from "@/src/services/ai/index"
import {
  AppMode,
  BudgetItem,
  ChatMessage,
  ManagedWedding,
  Task,
} from "@/src/types/index"
import {
  createMockVisionBoardItems,
  mockEllaGenerateInitialTimelineDev,
  mockEllaSuggestInitialBudgetDev,
} from "@/src/utils/mockDataUtils"
import { useEffect, useRef, useState } from "react"

interface UseWeddingDataPopulationProps {
  currentWedding: ManagedWedding | null
  appMode: AppMode
  managedWeddings: ManagedWedding[]
  updateDataForCurrentWedding: <K extends keyof ManagedWedding>(
    key: K,
    data: ManagedWedding[K]
  ) => void
  updateAIPopulatedStatusForCurrentWedding: (status: boolean) => void
  addChatMessageToCurrentWedding: (message: ChatMessage) => void
  addToast: (message: string, type: string, duration?: number) => void
}

export const useWeddingDataPopulation = ({
  currentWedding,
  appMode,
  managedWeddings,
  updateDataForCurrentWedding,
  updateAIPopulatedStatusForCurrentWedding,
  addChatMessageToCurrentWedding,
  addToast,
}: UseWeddingDataPopulationProps) => {
  const isPopulatingRef = useRef(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true)

  useEffect(() => {
    if (!currentWedding) {
      setIsInitialLoading(true)
      return
    }

    const currentWeddingIdSnapshot = currentWedding.id

    if (currentWedding.isAIPopulated || isPopulatingRef.current) {
      setIsInitialLoading(false)
      return
    }

    const populateInitialData = async () => {
      isPopulatingRef.current = true
      setIsInitialLoading(true)

      let populatedItems = false

      try {
        if (appMode === "prod") {
          await populateProductionData(
            currentWedding,
            updateDataForCurrentWedding,
            addToast
          )
          populatedItems = true
        } else {
          populatedItems = await populateDevData(
            currentWedding,
            updateDataForCurrentWedding
          )
        }

        if (populatedItems) {
          await finalizeDataPopulation(
            currentWeddingIdSnapshot,
            managedWeddings,
            updateAIPopulatedStatusForCurrentWedding,
            addChatMessageToCurrentWedding,
            appMode,
            addToast
          )
        }
      } catch (error) {
        await handlePopulationError(
          error,
          currentWeddingIdSnapshot,
          managedWeddings,
          updateAIPopulatedStatusForCurrentWedding,
          addChatMessageToCurrentWedding,
          appMode,
          addToast
        )
      } finally {
        setIsInitialLoading(false)
        isPopulatingRef.current = false
      }
    }

    populateInitialData()
  }, [
    currentWedding,
    appMode,
    managedWeddings,
    updateDataForCurrentWedding,
    updateAIPopulatedStatusForCurrentWedding,
    addChatMessageToCurrentWedding,
    addToast,
  ])

  return { isInitialLoading }
}

const populateProductionData = async (
  currentWedding: ManagedWedding,
  updateDataForCurrentWedding: <K extends keyof ManagedWedding>(
    key: K,
    data: ManagedWedding[K]
  ) => void,
  addToast: (message: string, type: string, duration?: number) => void
) => {
  addToast("Ella is preparing initial data for this client...", "info")

  let budgetItemsResponse: BudgetItem[]
  let timelineTasksResponse: Task[]

  try {
    ;[budgetItemsResponse, timelineTasksResponse] = await Promise.all([
      ellaSuggestInitialBudget(currentWedding.weddingDetails, "prod"),
      ellaGenerateInitialTimeline(currentWedding.weddingDetails, "prod"),
    ])
  } catch (prodPopulationError) {
    console.error(
      "PRODUCTION MODE: Error during AI data population. Falling back to emergency mocks:",
      prodPopulationError
    )
    addToast(
      "Ella had trouble preparing initial data with AI. Using placeholder data.",
      "error",
      7000
    )
    budgetItemsResponse = await mockEllaSuggestInitialBudgetDev(
      currentWedding.weddingDetails
    )
    timelineTasksResponse = await mockEllaGenerateInitialTimelineDev(
      currentWedding.weddingDetails
    )
  }

  updateDataForCurrentWedding("budgetItems", budgetItemsResponse)
  updateDataForCurrentWedding("tasks", timelineTasksResponse)
}

const populateDevData = async (
  currentWedding: ManagedWedding,
  updateDataForCurrentWedding: <K extends keyof ManagedWedding>(
    key: K,
    data: ManagedWedding[K]
  ) => void
): Promise<boolean> => {
  let devDataUpdated = false

  if (currentWedding.visionBoardItems.length === 0) {
    const mockVisionItems = createMockVisionBoardItems(
      currentWedding.weddingDetails.vibe
    )
    updateDataForCurrentWedding("visionBoardItems", mockVisionItems)
    devDataUpdated = true
  }

  if (currentWedding.tasks.length === 0) {
    const devMockTasks = await mockEllaGenerateInitialTimelineDev(
      currentWedding.weddingDetails
    )
    updateDataForCurrentWedding("tasks", devMockTasks)
    devDataUpdated = true
  }

  if (currentWedding.budgetItems.length === 0) {
    const devMockBudget = await mockEllaSuggestInitialBudgetDev(
      currentWedding.weddingDetails
    )
    updateDataForCurrentWedding("budgetItems", devMockBudget)
    devDataUpdated = true
  }

  if (devDataUpdated) {
    console.log(
      "Dev mode: Populated initial mock data for vision board, tasks, and/or budget."
    )
  }

  return devDataUpdated
}

const finalizeDataPopulation = async (
  currentWeddingIdSnapshot: string,
  managedWeddings: ManagedWedding[],
  updateAIPopulatedStatusForCurrentWedding: (status: boolean) => void,
  addChatMessageToCurrentWedding: (message: ChatMessage) => void,
  appMode: AppMode,
  addToast: (message: string, type: string, duration?: number) => void
) => {
  const finalWeddingCheck = managedWeddings.find(
    (w) => w.id === currentWeddingIdSnapshot
  )
  if (finalWeddingCheck && !finalWeddingCheck.isAIPopulated) {
    updateAIPopulatedStatusForCurrentWedding(true)

    const uniqueMessageIdPrefix = `system-initial-data-${currentWeddingIdSnapshot}`
    const finalWeddingForChatCheck = managedWeddings.find(
      (w) => w.id === currentWeddingIdSnapshot
    )
    const hasAIMessageAlready = finalWeddingForChatCheck?.chatHistory.some(
      (msg) => msg.id.startsWith(uniqueMessageIdPrefix)
    )

    if (!hasAIMessageAlready) {
      let systemText =
        "Initial data has been set up for this wedding client! Check Budget and Timeline sections."
      if (appMode === "prod") {
        systemText =
          "Ella has prepared an initial budget and a comprehensive timeline for this client! You can find them in the Budget and Timeline sections."
        addToast(
          "Ella finished preparing initial budget & comprehensive timeline!",
          "success"
        )
      } else {
        addToast("Dev mode: Initial comprehensive data populated.", "info")
      }

      const aiSystemMessage: ChatMessage = {
        id: `${uniqueMessageIdPrefix}-${Date.now()}`,
        text: systemText,
        sender: "system",
        timestamp: new Date(),
      }
      addChatMessageToCurrentWedding(aiSystemMessage)
    }
  }
}

const handlePopulationError = async (
  error: any,
  currentWeddingIdSnapshot: string,
  managedWeddings: ManagedWedding[],
  updateAIPopulatedStatusForCurrentWedding: (status: boolean) => void,
  addChatMessageToCurrentWedding: (message: ChatMessage) => void,
  appMode: AppMode,
  addToast: (message: string, type: string, duration?: number) => void
) => {
  console.error(
    `Failed to populate initial data for wedding ${currentWeddingIdSnapshot} in ${appMode} mode:`,
    error
  )
  if (appMode === "prod")
    addToast("Ella had critical trouble preparing initial data.", "error")

  const errorSystemMessage: ChatMessage = {
    id: `system-initial-data-error-${currentWeddingIdSnapshot}-${Date.now()}`,
    text: "There was an issue preparing the initial data for this wedding. You can try adding items manually for now.",
    sender: "system",
    timestamp: new Date(),
  }
  addChatMessageToCurrentWedding(errorSystemMessage)

  const finalWeddingCheck = managedWeddings.find(
    (w) => w.id === currentWeddingIdSnapshot
  )
  if (finalWeddingCheck && !finalWeddingCheck.isAIPopulated) {
    updateAIPopulatedStatusForCurrentWedding(true)
  }
}
