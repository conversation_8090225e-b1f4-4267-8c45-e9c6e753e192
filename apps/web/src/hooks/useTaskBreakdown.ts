import { initialTaskFormState } from "@/src/hooks/useTaskManager"
import { ellaBreakdownTask } from "@/src/services/ai/index"
import { AppMode, Task, WeddingDetails } from "@/src/types/index"
import { generateClientSideId } from "@/src/utils/idUtils"
import { TaskItem } from "@/src/utils/taskUtils"
import { useState } from "react"

type SubTaskSuggestion = Omit<Task, "id" | "isCompleted" | "parentId">

export const useTaskBreakdown = (
  setContextTasks: (
    newItems: TaskItem[] | ((prevItems: TaskItem[]) => TaskItem[])
  ) => void,
  addToast: (
    message: string,
    type: "success" | "error" | "info" | "warning",
    duration?: number
  ) => void
) => {
  const [showBreakdownModal, setShowBreakdownModal] = useState(false)
  const [selectedTaskForBreakdown, setSelectedTaskForBreakdown] =
    useState<TaskItem | null>(null)
  const [suggestedSubTasks, setSuggestedSubTasks] = useState<
    SubTaskSuggestion[]
  >([])
  const [selectedSubTasksToAdd, setSelectedSubTasksToAdd] = useState<boolean[]>(
    []
  )
  const [parentTaskUpdateSuggestion, setParentTaskUpdateSuggestion] =
    useState<Partial<Omit<Task, "id" | "isCompleted" | "parentId">> | null>(
      null
    )
  const [isBreakingDownTask, setIsBreakingDownTask] = useState(false)

  const handleOpenBreakdownModal = async (
    task: TaskItem,
    weddingDetails: WeddingDetails,
    appMode: AppMode
  ) => {
    setSelectedTaskForBreakdown(task)
    setIsBreakingDownTask(true)
    setSuggestedSubTasks([])
    setParentTaskUpdateSuggestion(null)
    setShowBreakdownModal(true)
    addToast(`Ella is analyzing "${task.title}" for breakdown...`, "info")

    try {
      const { subTasks, parentTaskUpdate } = await ellaBreakdownTask(
        task,
        weddingDetails,
        appMode
      )
      setSuggestedSubTasks(subTasks)
      setParentTaskUpdateSuggestion(parentTaskUpdate || null)
      setSelectedSubTasksToAdd(new Array(subTasks.length).fill(true))
      if (subTasks.length > 0) {
        addToast(`Ella suggested ${subTasks.length} sub-tasks.`, "success")
      } else {
        addToast(`Ella couldn't break down "${task.title}" further.`, "info")
      }
    } catch (error) {
      console.error("Error breaking down task:", error)
      addToast(`Error breaking down "${task.title}".`, "error")
    } finally {
      setIsBreakingDownTask(false)
    }
  }

  const handleSubTaskCheckboxChange = (index: number) => {
    setSelectedSubTasksToAdd((prev) => {
      const newSelection = [...prev]
      newSelection[index] = !newSelection[index]
      return newSelection
    })
  }

  const handleAddSelectedSubTasks = () => {
    if (!selectedTaskForBreakdown) return
    const newSubTasks: TaskItem[] = suggestedSubTasks
      .filter((_, index) => selectedSubTasksToAdd[index])
      .map((subTask) => ({
        ...initialTaskFormState,
        ...subTask,
        id: generateClientSideId(),
        isCompleted: false,
        parentId: selectedTaskForBreakdown.id,
        isPlannerInternal: selectedTaskForBreakdown.isPlannerInternal,
      }))

    if (newSubTasks.length > 0) {
      setContextTasks((prevTasks) => [...prevTasks, ...newSubTasks])
      addToast(
        `Added ${newSubTasks.length} sub-task(s) for "${selectedTaskForBreakdown.title}".`,
        "success"
      )
    } else {
      addToast("No sub-tasks selected to add.", "info")
    }
  }

  const handleUpdateParentTask = () => {
    if (selectedTaskForBreakdown && parentTaskUpdateSuggestion) {
      setContextTasks((prevTasks) =>
        prevTasks.map((task) =>
          task.id === selectedTaskForBreakdown.id
            ? { ...task, ...parentTaskUpdateSuggestion }
            : task
        )
      )
      addToast(
        `Parent task "${selectedTaskForBreakdown.title}" updated.`,
        "success"
      )
    }
  }

  const closeBreakdownModal = () => {
    setShowBreakdownModal(false)
    setSelectedTaskForBreakdown(null)
    setSuggestedSubTasks([])
    setParentTaskUpdateSuggestion(null)
  }

  return {
    // State
    showBreakdownModal,
    selectedTaskForBreakdown,
    suggestedSubTasks,
    selectedSubTasksToAdd,
    parentTaskUpdateSuggestion,
    isBreakingDownTask,

    // Actions
    handleOpenBreakdownModal,
    handleSubTaskCheckboxChange,
    handleAddSelectedSubTasks,
    handleUpdateParentTask,
    closeBreakdownModal,
  }
}
