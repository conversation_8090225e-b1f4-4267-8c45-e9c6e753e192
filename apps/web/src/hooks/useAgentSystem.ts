import { useState, useCallback, useRef } from 'react';
import { useToast } from '@/src/contexts/ToastContext';

interface AgentMessage {
  type: 'start' | 'response' | 'error' | 'complete';
  content?: string;
  agent?: string;
  runId?: string;
}

interface AgentResponse {
  content: string;
  agent: string;
  runId: string;
}

interface FeedbackData {
  runId: string;
  userId: string;
  weddingId: string;
  feedbackType: 'rating' | 'thumbs' | 'text' | 'correction';
  rating?: number;
  thumbsDirection?: 'up' | 'down';
  feedbackText?: string;
  correctionData?: Record<string, any>;
}

export function useAgentSystem() {
  const [isLoading, setIsLoading] = useState(false);
  const [currentRunId, setCurrentRunId] = useState<string | null>(null);
  const { addToast } = useToast();
  const abortControllerRef = useRef<AbortController | null>(null);

  const sendMessage = useCallback(async (
    message: string,
    weddingId: string,
    userId: string,
    onResponse?: (response: AgentResponse) => void,
    onError?: (error: string) => void
  ): Promise<AgentResponse | null> => {
    if (isLoading) {
      addToast('Please wait for the current request to complete.', 'warning');
      return null;
    }

    setIsLoading(true);
    
    // Cancel any existing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    abortControllerRef.current = new AbortController();

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          weddingId,
          userId
        }),
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      const decoder = new TextDecoder();
      let agentResponse: AgentResponse | null = null;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data: AgentMessage = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'start':
                  if (data.runId) {
                    setCurrentRunId(data.runId);
                  }
                  break;
                  
                case 'response':
                  if (data.content && data.agent && currentRunId) {
                    agentResponse = {
                      content: data.content,
                      agent: data.agent,
                      runId: currentRunId
                    };
                    onResponse?.(agentResponse);
                  }
                  break;
                  
                case 'error':
                  const errorMsg = data.content || 'An error occurred';
                  onError?.(errorMsg);
                  addToast(errorMsg, 'error');
                  break;
                  
                case 'complete':
                  // Request completed
                  break;
              }
            } catch (parseError) {
              console.error('Error parsing agent response:', parseError);
            }
          }
        }
      }

      return agentResponse;

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('Request was aborted');
        return null;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'Failed to communicate with agent';
      console.error('Error sending message to agent:', error);
      onError?.(errorMessage);
      addToast(errorMessage, 'error');
      return null;
    } finally {
      setIsLoading(false);
      setCurrentRunId(null);
      abortControllerRef.current = null;
    }
  }, [isLoading, currentRunId, addToast]);

  const submitFeedback = useCallback(async (feedbackData: FeedbackData): Promise<boolean> => {
    try {
      const response = await fetch('/api/agent/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(feedbackData)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        addToast('Thank you for your feedback!', 'success');
        return true;
      } else {
        throw new Error('Failed to submit feedback');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      addToast('Failed to submit feedback. Please try again.', 'error');
      return false;
    }
  }, [addToast]);

  const getAnalytics = useCallback(async (
    type: 'dashboard' | 'agent-metrics' | 'recommendations',
    params: {
      startDate: string;
      endDate: string;
      agentType?: string;
      timePeriod?: 'hour' | 'day' | 'week' | 'month';
    }
  ) => {
    try {
      const searchParams = new URLSearchParams({
        type,
        startDate: params.startDate,
        endDate: params.endDate,
        ...(params.agentType && { agentType: params.agentType }),
        ...(params.timePeriod && { timePeriod: params.timePeriod })
      });

      const response = await fetch(`/api/agent/analytics?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting analytics:', error);
      addToast('Failed to load analytics data.', 'error');
      return null;
    }
  }, [addToast]);

  const executeWorkflow = useCallback(async (
    workflowData: {
      workflowName: string;
      description: string;
      weddingId: string;
      userId: string;
      steps: Array<{
        stepName: string;
        stepOrder: number;
        agentType: string;
        inputData?: Record<string, any>;
      }>;
      inputData?: Record<string, any>;
    }
  ) => {
    try {
      const response = await fetch('/api/agent/workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'workflow',
          data: workflowData
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        addToast('Workflow started successfully!', 'success');
        return result.workflowId;
      } else {
        throw new Error('Failed to start workflow');
      }
    } catch (error) {
      console.error('Error executing workflow:', error);
      addToast('Failed to start workflow. Please try again.', 'error');
      return null;
    }
  }, [addToast]);

  const cancelRequest = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      setIsLoading(false);
      setCurrentRunId(null);
      addToast('Request cancelled.', 'info');
    }
  }, [addToast]);

  return {
    sendMessage,
    submitFeedback,
    getAnalytics,
    executeWorkflow,
    cancelRequest,
    isLoading,
    currentRunId
  };
}
