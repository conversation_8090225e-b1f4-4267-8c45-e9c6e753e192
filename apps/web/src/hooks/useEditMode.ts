import { ManagedWedding } from "@/src/types/index"
import {
  AddWeddingFormData,
  convertWeddingDetailsToFormData,
} from "@/src/utils/onboardingUtils"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"

interface PlannerContextType {
  managedWeddings: ManagedWedding[]
}

export const useEditMode = (
  editWeddingId: string | null,
  plannerCtx: PlannerContextType | null,
  addToast: (message: string, type: string) => void,
  onFormDataLoaded?: (formData: AddWeddingFormData) => void
) => {
  const router = useRouter()
  const [isEditMode] = useState(!!editWeddingId)
  const [editError, setEditError] = useState<string | null>(null)
  const [isLoadingEdit, setIsLoadingEdit] = useState(false)

  useEffect(() => {
    if (isEditMode && editWeddingId && plannerCtx) {
      setIsLoadingEdit(true)

      const weddingToEdit = plannerCtx.managedWeddings.find(
        (w: ManagedWedding) => w.id === editWeddingId
      )

      if (weddingToEdit) {
        const formData = convertWeddingDetailsToFormData(
          weddingToEdit.weddingDetails
        )
        setEditError(null)
        if (onFormDataLoaded) {
          onFormDataLoaded(formData)
        }
      } else {
        console.error("Wedding to edit not found:", editWeddingId)
        addToast(`Error: Client (ID: ${editWeddingId}) not found.`, "error")
        setEditError(
          `The wedding client (ID: ${editWeddingId}) you are trying to edit could not be found. You will be redirected to the dashboard shortly.`
        )

        setTimeout(() => router.replace("/dashboard"), 5000)
      }
      setIsLoadingEdit(false)
    }
  }, [
    isEditMode,
    editWeddingId,
    plannerCtx,
    router,
    addToast,
    onFormDataLoaded,
  ])

  const validateEditWeddingExists = (editWeddingId: string): boolean => {
    if (!plannerCtx) return false

    const weddingExists = plannerCtx.managedWeddings.some(
      (w: ManagedWedding) => w.id === editWeddingId
    )

    if (!weddingExists) {
      setEditError(
        `The wedding client (ID: ${editWeddingId}) could not be found for update. It might have been deleted. Redirecting...`
      )
      addToast(
        `Error: Client (ID: ${editWeddingId}) not found for update.`,
        "error"
      )
      setTimeout(() => router.replace("/dashboard"), 5000)
      return false
    }

    return true
  }

  return {
    isEditMode,
    editError,
    isLoadingEdit,
    validateEditWeddingExists,
  }
}
