import { useToast } from "@/src/contexts/ToastContext"
import { PlannerContext } from "@/src/contexts/WeddingContext"
import { ellaSuggestBudgetItem } from "@/src/services/ai/index"
import { BudgetItem, EllaAutonomyLevel } from "@/src/types/index"
import {
  BudgetFormState,
  BudgetItemType,
  calculateBudgetTotals,
  createBudgetItemFromSuggestion,
  findOverBudgetItems,
  formatCurrency,
  initialBudgetFormState,
  isAISuggestionMeaningful,
} from "@/src/utils/budgetUtils"
import { generateClientSideId } from "@/src/utils/idUtils"
import { useContext, useEffect, useMemo, useState } from "react"

export const useBudgetManager = () => {
  const plannerCtx = useContext(PlannerContext)
  const { addToast } = useToast()
  const [isAISuggesting, setIsAISuggesting] = useState(false)
  const [overBudgetItems, setOverBudgetItems] = useState<BudgetItem[]>([])

  const budgetItemsContext = useMemo(() => {
    return plannerCtx?.currentWedding?.budgetItems || []
  }, [plannerCtx?.currentWedding?.budgetItems])

  const setBudgetItemsContext = (
    newItems: BudgetItem[] | ((prevItems: BudgetItem[]) => BudgetItem[])
  ) => {
    if (plannerCtx && plannerCtx.currentWeddingId) {
      const finalItems =
        typeof newItems === "function"
          ? newItems(plannerCtx.currentWedding?.budgetItems || [])
          : newItems
      plannerCtx.updateDataForCurrentWedding(
        "budgetItems",
        finalItems.map((item) => item as BudgetItem)
      )
    }
  }

  // Load dev mock data
  useEffect(() => {
    if (
      plannerCtx &&
      plannerCtx.currentWedding &&
      !plannerCtx.currentWedding.isAIPopulated &&
      plannerCtx.appMode === "dev" &&
      plannerCtx.currentWedding.budgetItems.length === 0
    ) {
      const devMockItems: BudgetItem[] = [
        {
          id: "b-devmock-1",
          category: "Venue",
          estimatedCost: 5000,
          actualCost: 0,
          isPaid: false,
          vendor: "",
          paymentDueDate: "",
        },
        {
          id: "b-devmock-2",
          category: "Catering",
          estimatedCost: 7500,
          actualCost: 0,
          isPaid: false,
          vendor: "",
          paymentDueDate: "",
        },
      ]
      if (plannerCtx.currentWeddingId) {
        plannerCtx.updateDataForCurrentWedding("budgetItems", devMockItems)
        addToast("Loaded placeholder budget items for client.", "info", 3000)
      }
    }
  }, [plannerCtx?.currentWedding?.id, plannerCtx, addToast])

  // Check for over-budget items
  useEffect(() => {
    const checkOverBudget = () => {
      if (!plannerCtx?.currentWedding?.budgetItems) {
        setOverBudgetItems([])
        return
      }
      const significantlyOver = findOverBudgetItems(
        plannerCtx.currentWedding.budgetItems
      )
      setOverBudgetItems(significantlyOver)
      if (significantlyOver.length > 0) {
        addToast(
          `Ella noticed ${significantlyOver.length} item(s) significantly over budget for your client. Check 'Ella's Budget Insights'.`,
          "warning",
          7000
        )
      }
    }
    checkOverBudget()
  }, [plannerCtx?.currentWedding?.budgetItems, addToast])

  const budgetTotals = useMemo(() => {
    return calculateBudgetTotals(budgetItemsContext as BudgetItemType[])
  }, [budgetItemsContext])

  const suggestBudgetItemByAI = async (
    onProactiveAdd: (item: BudgetItemType) => void,
    onBalancedSuggestion: (item: BudgetFormState) => void,
    onReviewSuggestion: (item: BudgetFormState) => void
  ) => {
    if (
      !plannerCtx ||
      !plannerCtx.currentWedding ||
      !plannerCtx.currentWedding.weddingDetails ||
      !plannerCtx.ellaAutonomyLevel
    ) {
      addToast(
        "Required client or planner context is not available for AI suggestion.",
        "error"
      )
      return
    }

    setIsAISuggesting(true)
    addToast("Ella is thinking of a budget suggestion...", "info")

    try {
      const suggestion = await ellaSuggestBudgetItem(
        budgetItemsContext as BudgetItemType[],
        plannerCtx.currentWedding.weddingDetails,
        plannerCtx.appMode,
        plannerCtx.ellaAutonomyLevel
      )

      if (isAISuggestionMeaningful(suggestion)) {
        const newItemBase = createBudgetItemFromSuggestion(
          suggestion,
          initialBudgetFormState
        )

        if (plannerCtx.ellaAutonomyLevel === "proactive") {
          const fullNewItem: BudgetItemType = {
            id: generateClientSideId(),
            ...newItemBase,
          }
          setBudgetItemsContext((prev) => [...prev, fullNewItem])
          addToast(
            `Ella proactively added for client: ${
              fullNewItem.category
            } (Est: ${formatCurrency(fullNewItem.estimatedCost)})`,
            "success"
          )
          onProactiveAdd(fullNewItem)
        } else if (plannerCtx.ellaAutonomyLevel === "balanced") {
          onBalancedSuggestion(newItemBase)
          addToast(
            `Ella suggested an item for client. Review & save in form.`,
            "info"
          )
        } else {
          // review_all
          onReviewSuggestion(newItemBase)
        }
      } else {
        addToast(
          "Ella couldn't find a specific new suggestion for this client right now.",
          "info"
        )
      }
    } catch (error) {
      console.error("Error suggesting budget item:", error)
      addToast(
        "Ella had trouble with that suggestion. Please try again!",
        "error"
      )
    } finally {
      setIsAISuggesting(false)
    }
  }

  const getAutonomyButtonText = (autonomyLevel?: EllaAutonomyLevel): string => {
    return autonomyLevel === "proactive"
      ? "Ella Adds Item"
      : autonomyLevel === "balanced"
      ? "Ella Drafts Item"
      : "Ella Suggests Item"
  }

  return {
    // State
    budgetItemsContext,
    overBudgetItems,
    isAISuggesting,
    budgetTotals,

    // Actions
    setBudgetItemsContext,
    suggestBudgetItemByAI,
    getAutonomyButtonText,

    // Context
    plannerCtx,
    addToast,
  }
}
