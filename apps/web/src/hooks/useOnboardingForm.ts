import {
  AddWeddingFormData,
  createInitialFormData,
  processFormInputValue,
  validateOnboardingForm,
} from "@/src/utils/onboardingUtils"
import { useCallback, useState } from "react"

export const useOnboardingForm = (isEditMode: boolean = false) => {
  const [formData, setFormData] = useState<AddWeddingFormData>(
    createInitialFormData()
  )
  const [formErrors, setFormErrors] = useState<
    Partial<Record<keyof AddWeddingFormData, string>>
  >({})

  const handleInputChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
      >
    ) => {
      const { name, value, type } = e.target
      const processedValue = processFormInputValue(name, value, type)

      setFormData((prev) => ({ ...prev, [name]: processedValue }))

      // Clear error for this field if it exists
      if (formErrors[name as keyof AddWeddingFormData]) {
        setFormErrors((prev) => ({ ...prev, [name]: undefined }))
      }
    },
    [formErrors]
  )

  const validateForm = useCallback(
    (addToast?: (message: string, type: string) => void): boolean => {
      const { isValid, errors } = validateOnboardingForm(formData, isEditMode)

      setFormErrors(errors)

      if (!isValid && addToast) {
        addToast("Please correct the form errors.", "error")
      }

      return isValid
    },
    [formData, isEditMode]
  )

  const resetForm = useCallback(() => {
    setFormData(createInitialFormData())
    setFormErrors({})
  }, [])

  const setFormDataFromWeddingDetails = useCallback(
    (data: AddWeddingFormData) => {
      setFormData(data)
      setFormErrors({})
    },
    []
  )

  return {
    formData,
    formErrors,
    handleInputChange,
    validateForm,
    resetForm,
    setFormData: setFormDataFromWeddingDetails,
  }
}
