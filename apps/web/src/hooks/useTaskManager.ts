import { useToast } from "@/src/contexts/ToastContext"
import { PlannerContext } from "@/src/contexts/WeddingContext"
import { ellaSuggestTimelineTask } from "@/src/services/ai/index"
import { parseDateStringToLocalMidnight } from "@/src/utils/dateUtils"
import { generateClientSideId } from "@/src/utils/idUtils"
import {
  TaskFilterOption,
  TaskItem,
  extractCoupleNames,
  filterTasks,
  getGroupOrder,
  groupTasksByCategory,
  sortTasks,
} from "@/src/utils/taskUtils"
import { useContext, useMemo, useState } from "react"

export interface TaskFormState {
  title: string
  description?: string
  dueDate?: string
  assignedTo?: string
  isPlannerInternal?: boolean
}

export const initialTaskFormState: TaskFormState = {
  title: "",
  description: "",
  dueDate: "",
  assignedTo: "",
  isPlannerInternal: false,
}

export const useTaskManager = () => {
  const plannerCtx = useContext(PlannerContext)
  const { addToast } = useToast()
  const [isAISuggesting, setIsAISuggesting] = useState(false)
  const [taskFilter, setTaskFilter] = useState<TaskFilterOption>("all")

  const contextTasks = useMemo(() => {
    return plannerCtx?.currentWedding?.tasks || []
  }, [plannerCtx?.currentWedding?.tasks])

  const setContextTasks = (
    newItems: TaskItem[] | ((prevItems: TaskItem[]) => TaskItem[])
  ) => {
    if (plannerCtx && plannerCtx.currentWeddingId) {
      const finalItems =
        typeof newItems === "function"
          ? newItems(plannerCtx.currentWedding?.tasks || [])
          : newItems
      plannerCtx.updateDataForCurrentWedding("tasks", finalItems)
    }
  }

  const weddingDateObj = plannerCtx?.currentWedding?.weddingDetails?.weddingDate
    ? parseDateStringToLocalMidnight(
        plannerCtx.currentWedding.weddingDetails.weddingDate
      )
    : null

  const todayForComparison = parseDateStringToLocalMidnight(
    new Date().toISOString().split("T")[0]
  )

  const coupleNamesArray = useMemo(() => {
    return extractCoupleNames(
      plannerCtx?.currentWedding?.weddingDetails?.coupleNames
    )
  }, [plannerCtx?.currentWedding?.weddingDetails?.coupleNames])

  const filteredContextTasks = useMemo(() => {
    return filterTasks(contextTasks, taskFilter)
  }, [contextTasks, taskFilter])

  const sortedContextTasks = useMemo(() => {
    return sortTasks(filteredContextTasks)
  }, [filteredContextTasks])

  const topLevelTasks = useMemo(
    () => sortedContextTasks.filter((task) => !task.parentId),
    [sortedContextTasks]
  )

  const groupedTopLevelTasks = useMemo(() => {
    return groupTasksByCategory(
      topLevelTasks,
      weddingDateObj,
      todayForComparison
    )
  }, [topLevelTasks, weddingDateObj, todayForComparison])

  const groupOrder = useMemo(() => {
    return getGroupOrder(weddingDateObj)
  }, [weddingDateObj])

  const toggleTaskCompletion = (taskId: string) => {
    let taskTitle = "Task"
    setContextTasks((prevTasks) =>
      prevTasks.map((task) => {
        if (task.id === taskId) {
          taskTitle = task.title
          return { ...task, isCompleted: !task.isCompleted }
        }
        return task
      })
    )
    const task = contextTasks.find((t) => t.id === taskId)
    if (task) {
      addToast(
        `${
          task.isCompleted ? "Marked as incomplete:" : "Marked as complete:"
        } "${taskTitle}"`,
        "success"
      )
    }
  }

  const suggestTaskByAI = async () => {
    if (
      !plannerCtx ||
      !plannerCtx.currentWedding ||
      !plannerCtx.currentWedding.weddingDetails
    ) {
      addToast(
        "Client's wedding details are not available for AI suggestion.",
        "error"
      )
      return
    }
    setIsAISuggesting(true)
    addToast("Ella is thinking of a timeline suggestion...", "info")
    try {
      const suggestion = await ellaSuggestTimelineTask(
        plannerCtx.currentWedding.tasks,
        plannerCtx.currentWedding.weddingDetails,
        plannerCtx.appMode
      )
      if (suggestion.title) {
        const newTask: TaskItem = {
          ...initialTaskFormState,
          id: generateClientSideId(),
          title: suggestion.title,
          description: suggestion.description || "",
          dueDate: suggestion.dueDate,
          assignedTo: suggestion.assignedTo || "Planner",
          isCompleted: false,
        }
        setContextTasks((prevTasks) => [...prevTasks, newTask])
        addToast(
          `Ella suggested adding task for client: ${suggestion.title}`,
          "success"
        )
      } else {
        addToast(
          "Ella couldn't pinpoint a new task suggestion for this client right now.",
          "info"
        )
      }
    } catch (error) {
      console.error("Error suggesting timeline task:", error)
      addToast(
        "Ella had trouble helping with the timeline. Please try again!",
        "error"
      )
    } finally {
      setIsAISuggesting(false)
    }
  }

  return {
    // State
    contextTasks,
    filteredContextTasks,
    sortedContextTasks,
    topLevelTasks,
    groupedTopLevelTasks,
    groupOrder,
    taskFilter,
    isAISuggesting,
    coupleNamesArray,
    weddingDateObj,
    todayForComparison,

    // Actions
    setContextTasks,
    setTaskFilter,
    toggleTaskCompletion,
    suggestTaskByAI,

    // Context
    plannerCtx,
    addToast,
  }
}
