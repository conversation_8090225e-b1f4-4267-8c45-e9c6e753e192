import { generateVisionSnapshot } from "@/src/services/ai/index"
import { AppMode, ChatMessage, WeddingDetails } from "@/src/types/index"
import { useState } from "react"

export const useVisionSnapshot = () => {
  const [visionSnapshotMessage, setVisionSnapshotMessage] =
    useState<ChatMessage | null>(null)
  const [showPortalButton, setShowPortalButton] = useState(false)

  const generateSnapshot = async (
    weddingDetails: WeddingDetails,
    appMode: AppMode,
    addToast: (message: string, type: string) => void
  ): Promise<void> => {
    try {
      addToast("<PERSON> is generating a vision snapshot...", "info")
      const snapshotMsg = await generateVisionSnapshot(weddingDetails, appMode)
      setVisionSnapshotMessage(snapshotMsg)
      setShowPortalButton(true)
    } catch (error) {
      console.error("Error generating vision snapshot:", error)
      const errorMessage: ChatMessage = {
        id: "error-submission",
        text: `Sorry, <PERSON> had a little trouble processing the client's details. Please try adjusting them or try again in a moment.`,
        sender: "system",
        timestamp: new Date(),
      }
      setVisionSnapshotMessage(errorMessage)
      setShowPortalButton(false)
      throw error
    }
  }

  const createUpdateConfirmationMessage = (
    coupleNames: string
  ): ChatMessage => {
    const confirmationMessage: ChatMessage = {
      id: `update-confirm-${Date.now()}`,
      text: `${coupleNames}'s wedding details have been successfully updated!`,
      sender: "system",
      timestamp: new Date(),
    }
    return confirmationMessage
  }

  const clearVisionSnapshot = () => {
    setVisionSnapshotMessage(null)
    setShowPortalButton(false)
  }

  return {
    visionSnapshotMessage,
    showPortalButton,
    generateSnapshot,
    createUpdateConfirmationMessage,
    clearVisionSnapshot,
  }
}
