import { useToast } from "@/src/contexts/ToastContext"
import { generateClientSideId } from "@/src/utils/idUtils"
import { useCallback, useEffect, useState } from "react"

export interface CrudItem {
  id: string
  [key: string]: any
}

interface UseCrudManagerOptions<TItem extends CrudItem, TFormState> {
  initialItems: TItem[]
  initialFormState: TFormState
  itemValidator?: (
    formState: TFormState,
    items: TItem[],
    editingItemId?: string | null
  ) => { isValid: boolean; errors: Record<string, string> }
  generateId?: () => string
  crudSetItems?: (newItems: TItem[] | ((prevItems: TItem[]) => TItem[])) => void
  itemTypeForToast?: string // Optional: for more specific toast messages e.g. "Budget item"
}

export const useCrudManager = <
  TItem extends CrudItem,
  TFormState extends object
>({
  initialItems,
  initialFormState,
  itemValidator,
  generateId = generateClientSideId,
  crudSetItems,
  itemTypeForToast = "Item", // Default item type for toast messages
}: UseCrudManagerOptions<TItem, TFormState>) => {
  const { addToast } = useToast()
  const [internalItems, setInternalItems] = useState<TItem[]>(initialItems)
  const items = crudSetItems ? initialItems : internalItems

  const setItems = crudSetItems || setInternalItems

  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState<TItem | null>(null)
  const [formState, setFormState] = useState<TFormState>(initialFormState)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (crudSetItems && initialItems !== internalItems) {
      setInternalItems(initialItems)
    }
  }, [initialItems, crudSetItems, internalItems])

  const handleInputChange = useCallback(
    (
      e: React.ChangeEvent<
        HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
      >
    ) => {
      const { name, value, type } = e.target
      const val =
        type === "checkbox"
          ? (e.target as HTMLInputElement).checked
          : type === "number"
          ? value === ""
            ? undefined
            : parseFloat(value)
          : value

      setFormState((prev) => ({ ...prev, [name]: val }))
      if (formErrors[name]) {
        setFormErrors((prev) => ({ ...prev, [name]: "" }))
      }
    },
    [formErrors]
  )

  const resetFormAndHide = useCallback(() => {
    setFormState(initialFormState)
    setEditingItem(null)
    setShowForm(false)
    setFormErrors({})
  }, [initialFormState])

  const handleAddNew = useCallback(() => {
    setFormState(initialFormState)
    setEditingItem(null)
    setShowForm(true)
    setFormErrors({})
  }, [initialFormState])

  const handleEdit = useCallback(
    (itemToEdit: TItem) => {
      const populatedFormState = { ...initialFormState }
      for (const key in populatedFormState) {
        if (
          Object.prototype.hasOwnProperty.call(itemToEdit, key) &&
          (itemToEdit as any)[key] !== undefined
        ) {
          ;(populatedFormState as any)[key] = (itemToEdit as any)[key]
        }
      }
      Object.keys(itemToEdit).forEach((key) => {
        if (
          key in populatedFormState &&
          (itemToEdit as any)[key] !== undefined
        ) {
          ;(populatedFormState as any)[key] = (itemToEdit as any)[key]
        }
      })

      setFormState(populatedFormState as TFormState)
      setEditingItem(itemToEdit)
      setShowForm(true)
      setFormErrors({})
    },
    [initialFormState]
  )

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      setFormErrors({})

      const validationItems = crudSetItems ? internalItems : items

      if (itemValidator) {
        const validationResult = itemValidator(
          formState,
          validationItems,
          editingItem?.id
        )
        if (!validationResult.isValid) {
          setFormErrors(validationResult.errors)
          addToast(
            `Please correct the form errors for the ${itemTypeForToast.toLowerCase()}.`,
            "error"
          )
          return
        }
      }

      if (editingItem) {
        setItems((prevItems) =>
          prevItems.map((item) =>
            item.id === editingItem.id
              ? ({ ...item, ...formState, id: editingItem.id } as TItem)
              : item
          )
        )
        addToast(`${itemTypeForToast} updated successfully!`, "success")
      } else {
        const newItemId = generateId()
        const completeNewItem = {
          ...initialFormState,
          ...formState,
          id: newItemId,
        } as unknown as TItem
        setItems((prevItems) => [...prevItems, completeNewItem])
        addToast(`${itemTypeForToast} added successfully!`, "success")
      }
      resetFormAndHide()
    },
    [
      formState,
      editingItem,
      items,
      internalItems,
      itemValidator,
      resetFormAndHide,
      generateId,
      initialFormState,
      setItems,
      crudSetItems,
      addToast,
      itemTypeForToast,
    ]
  )

  const handleDelete = useCallback(
    (itemId: string, confirmMessage?: string) => {
      const finalConfirmMessage =
        confirmMessage ||
        `Are you sure you want to delete this ${itemTypeForToast.toLowerCase()}?`
      if (window.confirm(finalConfirmMessage)) {
        setItems((prevItems) => prevItems.filter((item) => item.id !== itemId))
        addToast(`${itemTypeForToast} deleted.`, "success")
        if (editingItem?.id === itemId) {
          resetFormAndHide()
        }
      }
    },
    [editingItem, resetFormAndHide, setItems, addToast, itemTypeForToast]
  )

  return {
    items,
    setItems,
    showForm,
    setShowForm,
    editingItem,
    formState,
    setFormState,
    formErrors,
    handleInputChange,
    handleSubmit,
    handleEdit,
    handleDelete,
    handleAddNew,
    handleCancel: resetFormAndHide,
  }
}
