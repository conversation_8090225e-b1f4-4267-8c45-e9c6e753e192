export const TEXT_MODEL = "gemini-2.5-flash-preview-04-17"
export const IMAGE_MODEL = "imagen-3.0-generate-002"

// Updated onboarding responses for Planner context
export const plannerOnboardingEllaResponses: { [key: number]: string } = {
  0: "Hello there! I'm <PERSON>, your personal AI wedding assistant. I'm excited to help you manage your client's big day! To start, could you tell me the names of the happy couple for this new wedding entry?",
  1: "That's wonderful! And when are they thinking of having the wedding? A specific date, or perhaps a season and year?",
  2: "Lovely! Roughly how many guests are they planning to invite?",
  3: "Great! How would you describe the vibe or style they're dreaming of for their wedding? (e.g., romantic, modern, rustic, boho, classic)",
  4: "Sounds amazing! Do they have a specific location or city in mind, or are you still exploring options with them?",
  5: "Perfect! One last thing for now - do they have an initial budget idea in mind? Even a rough range is helpful, but no worries if not!",
  6: "Thank you so much! I have a clearer picture for this client. I'm preparing a 'Vision Snapshot' for them. It'll be ready in a moment! In the meantime, I'm setting up their Wedding Portal within your planner dashboard. Get ready for a seamless planning experience for this client! ✨",
}

export const mockDelay = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms))
