// Environment configuration for dev/prod separation
export type Environment = "development" | "production"

export const getEnvironment = (): Environment => {
  // Check if we're in Vercel deployment
  if (
    typeof window !== "undefined" &&
    window.location.hostname.includes("vercel.app")
  ) {
    return "production"
  }
  // Check environment variables using Next.js standard
  if (process.env.NODE_ENV === "production") {
    return "production"
  }
  return "development"
}

export const ENV_CONFIG = {
  development: {
    defaultAppMode: "dev" as const,
    supabaseProject: "gxlucamlneoombifnirp", // vl_wedding_planner (dev branch)
    enableDebugLogs: true,
    enableDevTools: true,
    mockDataEnabled: true,
  },
  production: {
    defaultAppMode: "prod" as const,
    supabaseProject: "gxlucamlneoombifnirp", // vl_wedding_planner (main branch - production)
    enableDebugLogs: false,
    enableDevTools: false,
    mockDataEnabled: false,
  },
} as const

export const getCurrentEnvConfig = () => {
  const env = getEnvironment()
  return ENV_CONFIG[env]
}

// Validation helpers
export const validateEnvironment = () => {
  const config = getCurrentEnvConfig()
  const requiredEnvVars = []
  if (config.defaultAppMode === "prod") {
    // Use the utility from apiKeyUtils which correctly checks localStorage and process.env
    const geminiKey = process.env.GEMINI_API_KEY
    if (!geminiKey) {
      requiredEnvVars.push("GEMINI_API_KEY")
    }
  }
  return {
    isValid: requiredEnvVars.length === 0,
    missingVars: requiredEnvVars,
    environment: getEnvironment(),
    config,
  }
}
