export const VIBE_OPTIONS = [
  "Romantic",
  "Modern",
  "Rustic",
  "Boho",
  "Classic",
  "Elegant",
  "Fun",
  "Intimate",
  "Lavish",
  "Other",
]

export const GUEST_COUNT_OPTIONS = [
  { label: "Up to 50 guests", value: 50 },
  { label: "51 - 100 guests", value: 100 },
  { label: "101 - 150 guests", value: 150 },
  { label: "151 - 200 guests", value: 200 },
  { label: "201 - 300 guests", value: 300 },
  { label: "300+ guests", value: 400 }, // Representative value for 300+
]

export const BUDGET_OPTIONS = [
  { label: "Under $10,000", value: 10000 },
  { label: "$10,000 - $25,000", value: 25000 },
  { label: "$25,001 - $50,000", value: 50000 },
  { label: "$50,001 - $100,000", value: 100000 },
  { label: "$100,001 - $200,000", value: 200000 },
  { label: "Over $200,000", value: 300000 }, // Representative value
]

export const DEFAULT_FORM_VALUES = {
  guestCount: GUEST_COUNT_OPTIONS[1].value, // Default to 100 guests
  vibe: VIBE_OPTIONS[0], // Default to "Romantic"
  initialBudget: BUDGET_OPTIONS[1].value, // Default to $25,000
  weddingDate: () =>
    new Date(new Date().setFullYear(new Date().getFullYear() + 1))
      .toISOString()
      .split("T")[0],
}
