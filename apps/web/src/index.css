@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Color scheme variables */
  --color-background: 0 0% 4%;
  --color-foreground: 0 0% 95%;
  --color-card: 0 0% 7%;
  --color-card-foreground: 0 0% 95%;
  --color-popover: 0 0% 7%;
  --color-popover-foreground: 0 0% 95%;
  --color-primary: 271 76% 53%;
  --color-primary-foreground: 0 0% 98%;
  --color-secondary: 0 0% 14%;
  --color-secondary-foreground: 0 0% 95%;
  --color-muted: 0 0% 14%;
  --color-muted-foreground: 0 0% 63%;
  --color-accent: 0 0% 14%;
  --color-accent-foreground: 0 0% 95%;
  --color-destructive: 0 62% 50%;
  --color-destructive-foreground: 0 0% 98%;
  --color-positive: 120 100% 25%;
  --color-warning: 38 92% 50%;
  --color-border: 0 0% 14%;
  --color-input: 0 0% 14%;
  --color-input-border: 0 0% 20%;
  --color-ring: 271 76% 53%;
  --color-sidebar: 0 0% 4%;
  --color-sidebar-foreground: 0 0% 95%;
  --color-sidebar-primary: 271 76% 53%;
  --color-sidebar-primary-foreground: 0 0% 98%;
  --color-sidebar-accent: 0 0% 14%;
  --color-sidebar-border: 0 0% 14%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }
}

/* Custom scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--color-muted)) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--color-muted));
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--color-muted-foreground));
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slideInUp {
  animation: slideInUp 0.3s ease-out;
}

.ai-pulse-dot {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Button animations */
.button-pulse-primary {
  animation: pulse 2s infinite;
}

/* Gradient backgrounds */
.bg-animated-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--color-background)) 0%,
    hsl(var(--color-card)) 50%,
    hsl(var(--color-background)) 100%
  );
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Shadow effects */
.shadow-glow-primary {
  box-shadow: 0 0 20px hsla(var(--color-primary), 0.3);
}
