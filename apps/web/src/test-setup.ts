// Set up DOM environment using happy-dom
import { Window } from 'happy-dom'

// Create a window instance and set up global DOM
const window = new Window()
global.window = window as any
global.document = window.document as any
global.HTMLElement = window.HTMLElement as any
global.HTMLAnchorElement = window.HTMLAnchorElement as any
global.HTMLButtonElement = window.HTMLButtonElement as any
global.HTMLFormElement = window.HTMLFormElement as any
global.HTMLInputElement = window.HTMLInputElement as any
global.HTMLSelectElement = window.HTMLSelectElement as any
global.HTMLTextAreaElement = window.HTMLTextAreaElement as any
global.SVGElement = window.SVGElement as any
global.Event = window.Event as any
global.CustomEvent = window.CustomEvent as any
global.MouseEvent = window.MouseEvent as any
global.KeyboardEvent = window.KeyboardEvent as any
global.FocusEvent = window.FocusEvent as any
global.Node = window.Node as any
global.NodeList = window.NodeList as any
global.Element = window.Element as any
global.Text = window.Text as any
global.Comment = window.Comment as any
global.DocumentFragment = window.DocumentFragment as any
global.DOMParser = window.DOMParser as any
global.XMLSerializer = window.XMLSerializer as any
global.FormData = window.FormData as any
global.Headers = window.Headers as any
global.Request = window.Request as any
global.Response = window.Response as any
global.URL = window.URL as any
global.URLSearchParams = window.URLSearchParams as any
global.AbortController = window.AbortController as any
global.AbortSignal = window.AbortSignal as any
global.fetch = window.fetch as any
global.navigator = window.navigator as any
global.location = window.location as any
global.history = window.history as any
global.screen = window.screen as any
global.sessionStorage = window.sessionStorage as any
global.localStorage = window.localStorage as any
global.console = window.console as any

// Import jest-dom matchers after setting up DOM
import '@testing-library/jest-dom'

// Set up environment variables for tests
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test'
}
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://mock-supabase-url.supabase.co'
}
if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'mock-anon-key'
}
if (!process.env.NEXT_PUBLIC_SUPABASE_DATA_URL) {
  process.env.NEXT_PUBLIC_SUPABASE_DATA_URL = 'https://mock-supabase-data-url.supabase.co'
}
if (!process.env.NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY) {
  process.env.NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY = 'mock-data-anon-key'
}
if (!process.env.GEMINI_API_KEY) {
  process.env.GEMINI_API_KEY = 'mock-gemini-key'
}
if (!process.env.OPENAI_API_KEY) {
  process.env.OPENAI_API_KEY = 'mock-openai-key'
}
if (!process.env.ANTHROPIC_API_KEY) {
  process.env.ANTHROPIC_API_KEY = 'mock-anthropic-key'
}
if (!process.env.GROQ_API_KEY) {
  process.env.GROQ_API_KEY = 'mock-groq-key'
}
