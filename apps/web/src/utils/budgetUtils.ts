import { BudgetItem } from "@/src/types/index"

export interface BudgetItemType extends BudgetItem {
  id: string
}

export type BudgetFormState = Omit<BudgetItemType, "id">

export const initialBudgetFormState: BudgetFormState = {
  category: "",
  estimatedCost: 0,
  actualCost: 0,
  vendor: "",
  paymentDueDate: "",
  isPaid: false,
}

export const formatCurrency = (amount: number | undefined): string => {
  if (amount === undefined || amount === null) return "$0.00"
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

export const budgetValidator = (formState: BudgetFormState) => {
  const errors: Record<string, string> = {}
  if (!formState.category.trim()) errors.category = "Category is required."
  if (formState.estimatedCost === undefined || formState.estimatedCost < 0)
    errors.estimatedCost = "Valid Estimated Cost is required."
  if (formState.actualCost !== undefined && formState.actualCost < 0)
    errors.actualCost = "Actual cost cannot be negative."
  return { isValid: Object.keys(errors).length === 0, errors }
}

export const calculateBudgetTotals = (items: BudgetItemType[]) => {
  const totalEstimated = items.reduce(
    (sum, item) => sum + (item.estimatedCost || 0),
    0
  )
  const totalActual = items.reduce(
    (sum, item) => sum + (item.actualCost || 0),
    0
  )
  const budgetDifference = totalEstimated - totalActual

  return {
    totalEstimated,
    totalActual,
    budgetDifference,
  }
}

export const findOverBudgetItems = (items: BudgetItem[]): BudgetItem[] => {
  return items.filter(
    (item) =>
      item.actualCost > 0 &&
      item.estimatedCost > 0 &&
      item.actualCost > item.estimatedCost * 1.2 && // More than 20% over
      item.actualCost > item.estimatedCost + 100 // And at least $100 over
  )
}

export const isAISuggestionMeaningful = (suggestion: any): boolean => {
  const unhelpfulPlaceholders = [
    "N/A",
    "None",
    "Placeholder",
    "Suggested Item",
    "Miscellaneous",
    "Other Costs",
  ]

  return (
    suggestion &&
    typeof suggestion.category === "string" &&
    suggestion.category.trim() !== "" &&
    !unhelpfulPlaceholders.some((placeholder) =>
      suggestion.category!.toLowerCase().includes(placeholder.toLowerCase())
    ) &&
    typeof suggestion.estimatedCost === "number" &&
    suggestion.estimatedCost > 0
  )
}

export const createBudgetItemFromSuggestion = (
  suggestion: any,
  initialFormState: BudgetFormState
): Omit<BudgetItemType, "id"> => {
  return {
    category: suggestion.category!.trim(),
    estimatedCost: suggestion.estimatedCost!,
    actualCost: suggestion.actualCost ?? initialFormState.actualCost,
    isPaid: suggestion.isPaid ?? initialFormState.isPaid,
    vendor: suggestion.vendor ?? initialFormState.vendor,
    paymentDueDate:
      suggestion.paymentDueDate ?? initialFormState.paymentDueDate,
  }
}
