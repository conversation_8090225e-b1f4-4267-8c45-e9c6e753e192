import { Task } from "@/src/types/index"
import { parseDateStringToLocalMidnight } from "@/src/utils/dateUtils"

export interface TaskItem extends Task {
  id: string
}

export type TaskFilterOption = "all" | "planner" | "internal"

export const getTaskGroup = (
  task: TaskItem,
  weddingDateObj: Date | null,
  todayForComparison: Date | null
): string => {
  if (task.isCompleted) return "Completed"

  const taskDueDateObj = parseDateStringToLocalMidnight(task.dueDate)
  if (!taskDueDateObj) return "Anytime / Unscheduled"

  if (weddingDateObj) {
    if (taskDueDateObj < todayForComparison! && !task.isCompleted)
      return "Past Due"
    const diffTime = weddingDateObj.getTime() - taskDueDateObj.getTime()
    const diffDaysFromWedding = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    if (diffDaysFromWedding > 365) return "12+ Months Before"
    if (diffDaysFromWedding > 270 && diffDaysFromWedding <= 365)
      return "9-12 Months Before"
    if (diffDaysFromWedding > 180 && diffDaysFromWedding <= 270)
      return "6-9 Months Before"
    if (diffDaysFromWedding > 90 && diffDaysFromWedding <= 180)
      return "3-6 Months Before"
    if (diffDaysFromWedding > 30 && diffDaysFromWedding <= 90)
      return "1-3 Months Before"
    if (diffDaysFromWedding >= 0 && diffDaysFromWedding <= 30)
      return "Final Month"
    if (diffDaysFromWedding < 0) return "Post-Wedding Tasks"
  } else {
    // No wedding date set, group by relation to today
    if (taskDueDateObj < todayForComparison! && !task.isCompleted)
      return "Past Due"
    if (taskDueDateObj.getTime() === todayForComparison!.getTime())
      return "Due Today"
    if (taskDueDateObj > todayForComparison!) return "Upcoming"
  }
  return "Uncategorized"
}

export const getGroupOrder = (weddingDateObj: Date | null): string[] => {
  return weddingDateObj
    ? [
        "Past Due",
        "Final Month",
        "1-3 Months Before",
        "3-6 Months Before",
        "6-9 Months Before",
        "9-12 Months Before",
        "12+ Months Before",
        "Anytime / Unscheduled",
        "Post-Wedding Tasks",
        "Uncategorized",
        "Completed",
      ]
    : [
        "Past Due",
        "Due Today",
        "Upcoming",
        "Anytime / Unscheduled",
        "Uncategorized",
        "Completed",
      ]
}

export const filterTasks = (
  tasks: TaskItem[],
  filter: TaskFilterOption
): TaskItem[] => {
  if (filter === "all") {
    return tasks.filter((task) => !task.isPlannerInternal)
  }
  if (filter === "planner") {
    return tasks.filter(
      (task) =>
        task.assignedTo?.toLowerCase() === "planner" && !task.isPlannerInternal
    )
  }
  if (filter === "internal") {
    return tasks.filter((task) => task.isPlannerInternal === true)
  }
  return tasks.filter((task) => !task.isPlannerInternal)
}

export const sortTasks = (tasks: TaskItem[]): TaskItem[] => {
  return [...tasks].sort((a, b) => {
    const dateA = parseDateStringToLocalMidnight(a.dueDate)
    const dateB = parseDateStringToLocalMidnight(b.dueDate)
    if (dateA && dateB) return dateA.getTime() - dateB.getTime()
    if (dateA) return -1
    if (dateB) return 1
    return a.title.localeCompare(b.title)
  })
}

export const groupTasksByCategory = (
  tasks: TaskItem[],
  weddingDateObj: Date | null,
  todayForComparison: Date | null
): Record<string, TaskItem[]> => {
  return tasks.reduce((acc, task) => {
    const group = getTaskGroup(task, weddingDateObj, todayForComparison)
    if (!acc[group]) acc[group] = []
    acc[group].push(task)
    return acc
  }, {} as Record<string, TaskItem[]>)
}

export const isTaskOverdue = (
  task: TaskItem,
  todayForComparison: Date | null
): boolean => {
  if (task.isCompleted) return false
  const taskDueDateObj = parseDateStringToLocalMidnight(task.dueDate)
  return !!(
    taskDueDateObj &&
    todayForComparison &&
    taskDueDateObj < todayForComparison
  )
}

export const extractCoupleNames = (coupleNamesString?: string): string[] => {
  if (!coupleNamesString) return []
  return coupleNamesString
    .split("&")
    .map((name) => name.trim())
    .filter((name) => name)
}

export const taskValidator = (formState: any) => {
  const errors: Record<string, string> = {}
  if (!formState.title?.trim()) errors.title = "Task title is required."
  return { isValid: Object.keys(errors).length === 0, errors }
}
