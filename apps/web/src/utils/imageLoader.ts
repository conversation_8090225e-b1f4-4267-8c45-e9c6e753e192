'use client';

export default function customImageLoader({ src, width, quality }: { src: string, width: number, quality?: number }) {
  // Check if src is a valid URL or a base64 data URL
  if (src.startsWith('data:')) {
    // Return the data URL as is
    return src;
  }
  
  try {
    // Try to create a URL object to validate the URL
    const url = new URL(src);
    // Add width and quality parameters if it's a valid URL
    url.searchParams.set('width', width.toString());
    url.searchParams.set('quality', (quality || 75).toString());
    return url.href;
  } catch (error) {
    // If URL is invalid, return a placeholder or the original src
    console.warn('Invalid image URL:', src);
    return src;
  }
}