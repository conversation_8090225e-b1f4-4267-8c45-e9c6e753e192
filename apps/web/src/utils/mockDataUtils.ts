import {
  BudgetItem,
  Task,
  VisionBoardItem,
  WeddingDetails,
} from "@/src/types/index"
import { parseDateStringToLocalMidnight } from "@/src/utils/dateUtils"
import { generateClientSideId } from "@/src/utils/idUtils"

export const mockEllaSuggestInitialBudgetDev = async (
  details: WeddingDetails
): Promise<BudgetItem[]> => {
  await new Promise((resolve) => setTimeout(resolve, 50))
  const baseBudget = details.initialBudget || 50000
  return [
    {
      id: generateClientSideId(),
      category: "Venue (Mock)",
      estimatedCost: baseBudget * 0.3,
      actualCost: 0,
      isPaid: false,
      vendor: "",
      paymentDueDate: "",
    },
    {
      id: generateClientSideId(),
      category: "Catering (Mock)",
      estimatedCost: baseBudget * 0.25,
      actualCost: 0,
      isPaid: false,
      vendor: "",
      paymentDueDate: "",
    },
  ]
}

export const mockEllaGenerateInitialTimelineDev = async (
  details: WeddingDetails
): Promise<Task[]> => {
  await new Promise((resolve) => setTimeout(resolve, 50))
  const weddingDateObj =
    parseDateStringToLocalMidnight(details.weddingDate) ||
    new Date(new Date().setFullYear(new Date().getFullYear() + 1))

  const calculateDueDate = (
    monthsBefore: number,
    daysBefore: number = 0
  ): string => {
    const dueDate = new Date(weddingDateObj.getTime())
    dueDate.setUTCMonth(dueDate.getUTCMonth() - monthsBefore)
    dueDate.setUTCDate(dueDate.getUTCDate() - daysBefore)
    return dueDate.toISOString().split("T")[0]
  }

  const tasks: Task[] = [
    // 12+ Months Out
    {
      id: generateClientSideId(),
      title: "Define Wedding Vision & Style with Client (Dev)",
      description: "Discuss overall theme, colors, and desired atmosphere.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Establish Client's Preliminary Budget (Dev)",
      description: "Work with client to set a realistic initial budget.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Draft Initial Guest List (Client Task) (Dev)",
      description: "Client to provide a rough estimate of guest numbers.",
      dueDate: calculateDueDate(12),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Select Potential Wedding Dates/Season with Client (Dev)",
      description: "Narrow down preferred dates or season.",
      dueDate: calculateDueDate(11),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Research & Scout Potential Venues (Dev)",
      description:
        "Identify venues matching client's style, capacity, and budget.",
      dueDate: calculateDueDate(11),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 9-12 Months Out
    {
      id: generateClientSideId(),
      title: "Book Wedding Venue with Client (Dev)",
      description: "Finalize and book the chosen wedding venue.",
      dueDate: calculateDueDate(10),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Photographer (Dev)",
      description: "Research, interview, and book photographer.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Videographer (Dev)",
      description: "Research, interview, and book videographer.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Caterer (if separate) (Dev)",
      description: "Select and book catering services.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Book Officiant (Dev)",
      description: "Secure an officiant for the ceremony.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Start Wedding Website (Client Task) (Dev)",
      description: "Client to set up basic wedding website with key details.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Assemble Client's Vision Board (Dev)",
      description: "Collect visual inspiration for decor, attire, etc.",
      dueDate: calculateDueDate(9),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Choose Wedding Party (Client Task) (Dev)",
      description: "Client to decide on bridesmaids, groomsmen, etc.",
      dueDate: calculateDueDate(8),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 6-9 Months Out
    {
      id: generateClientSideId(),
      title: "Hire Florist (Dev)",
      description: "Select and book a florist.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Hire Band/DJ (Dev)",
      description: "Book entertainment for reception.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Order Save-the-Dates (Client Task) (Dev)",
      description: "Design and order save-the-date cards.",
      dueDate: calculateDueDate(7),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Guest List (Client Task) (Dev)",
      description: "Client to confirm final guest list and addresses.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Shop for Wedding Attire (Client Task) (Dev)",
      description: "Client to begin looking for wedding dress, suits, etc.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Arrange Hotel Room Blocks for Guests (Dev)",
      description: "Reserve blocks of hotel rooms for out-of-town guests.",
      dueDate: calculateDueDate(6),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 4-6 Months Out
    {
      id: generateClientSideId(),
      title: "Send Save-the-Dates (Client Task) (Dev)",
      description: "Client to mail out save-the-date cards.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Order Wedding Invitations (Dev)",
      description: "Design and order wedding invitations and stationery.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Book Hair & Makeup Artists (Dev)",
      description: "Secure stylists for the wedding day.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Ceremony Details (Dev)",
      description: "Outline ceremony structure, readings, music with client.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Reception Details (Dev)",
      description: "Outline reception timeline, decor, layout with client.",
      dueDate: calculateDueDate(5),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Order Wedding Cake (Dev)",
      description: "Taste and order the wedding cake.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Arrange Transportation (Dev)",
      description: "Book transportation for wedding party and/or guests.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Purchase Wedding Rings (Client Task) (Dev)",
      description: "Client to select and purchase wedding bands.",
      dueDate: calculateDueDate(4),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // 2-3 Months Out
    {
      id: generateClientSideId(),
      title: "Mail Wedding Invitations (Client Task) (Dev)",
      description: "Client to send out wedding invitations.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Menu with Caterer (Dev)",
      description: "Confirm final food and beverage selections.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Schedule Attire Fittings (Client Task) (Dev)",
      description: "Client to schedule dress/suit fittings.",
      dueDate: calculateDueDate(3),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Floral Selections (Dev)",
      description: "Confirm all floral arrangements with florist.",
      dueDate: calculateDueDate(2, 15),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Plan Rehearsal Dinner (Dev)",
      description: "Organize details for the rehearsal dinner.",
      dueDate: calculateDueDate(2, 15),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },
    {
      id: generateClientSideId(),
      title: "Create Seating Chart (Dev)",
      description: "Develop the guest seating arrangement for reception.",
      dueDate: calculateDueDate(2),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 1 Month Out
    {
      id: generateClientSideId(),
      title: "Apply for Marriage License (Client Task) (Dev)",
      description: "Client to obtain marriage license.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Confirm Final Details with All Vendors (Dev)",
      description: "Reconfirm arrival times, services, and payments.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Finalize Wedding Day Timeline (Dev)",
      description: "Create a detailed schedule for the wedding day.",
      dueDate: calculateDueDate(1),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Confirm RSVP Count with Venue/Caterer (Dev)",
      description: "Provide final guest numbers.",
      dueDate: calculateDueDate(0, 21),
      isCompleted: false,
      assignedTo: "Planner",
    },

    // 1-2 Weeks Out
    {
      id: generateClientSideId(),
      title: "Review Wedding Day Timeline with Wedding Party (Dev)",
      description: "Ensure everyone knows the schedule.",
      dueDate: calculateDueDate(0, 7),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Pick Up Wedding Attire (Client Task) (Dev)",
      description: "Client to collect cleaned/altered attire.",
      dueDate: calculateDueDate(0, 3),
      isCompleted: false,
      assignedTo: "Couple",
    },

    // Wedding Week
    {
      id: generateClientSideId(),
      title: "Final Vendor Confirmations & Payments (Dev)",
      description: "Ensure all vendors are set and final payments made.",
      dueDate: calculateDueDate(0, 2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Prepare Tip Envelopes (Dev)",
      description: "Organize tips for vendors.",
      dueDate: calculateDueDate(0, 2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Rehearsal & Rehearsal Dinner (Dev)",
      description: "Conduct ceremony rehearsal and host dinner.",
      dueDate: calculateDueDate(0, 1),
      isCompleted: false,
      assignedTo: "Planner/Couple",
    },

    // Wedding Day
    {
      id: generateClientSideId(),
      title: "Oversee Venue Setup & Vendor Coordination (Dev)",
      description: "Manage all on-site logistics.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Manage Wedding Day Timeline Execution (Dev)",
      description: "Ensure everything runs smoothly and on schedule.",
      dueDate: calculateDueDate(0, 0),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },

    // Post-Wedding
    {
      id: generateClientSideId(),
      title: "Oversee Venue Teardown/Cleanup (Dev)",
      description: "Manage post-event cleanup as per contract.",
      dueDate: calculateDueDate(-0, -1),
      isCompleted: false,
      assignedTo: "Planner",
      isPlannerInternal: true,
    },
    {
      id: generateClientSideId(),
      title: "Return Rented Items (Dev)",
      description: "Ensure all rentals are returned on time.",
      dueDate: calculateDueDate(-0, -2),
      isCompleted: false,
      assignedTo: "Planner",
    },
    {
      id: generateClientSideId(),
      title: "Send Thank You Notes (Client Task) (Dev)",
      description: "Client to write and send thank you cards to guests.",
      dueDate: calculateDueDate(-1, 0),
      isCompleted: false,
      assignedTo: "Couple",
    },
    {
      id: generateClientSideId(),
      title: "Follow Up with Photographer/Videographer for Deliverables (Dev)",
      description: "Check on photo/video delivery timeline.",
      dueDate: calculateDueDate(-1, -15),
      isCompleted: false,
      assignedTo: "Planner",
    },
  ]
  return tasks.slice(0, 75)
}

export const createMockVisionBoardItems = (vibe: string): VisionBoardItem[] => {
  const vibeKeyword = vibe?.toLowerCase().replace(/\s+/g, "-") || "wedding"

  const ceremonyIcon = `data:image/svg+xml;base64,${btoa(`
    <svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="300" fill="#FEF7ED"/>
      <circle cx="200" cy="150" r="60" fill="#FED7AA" stroke="#FB923C" stroke-width="2"/>
      <path d="M170 130 L190 150 L230 110" stroke="#FB923C" stroke-width="2" fill="none"/>
      <text x="200" y="240" text-anchor="middle" fill="#9A3412" font-family="Arial" font-size="16">Ceremony Decor</text>
      <text x="200" y="260" text-anchor="middle" fill="#C2410C" font-family="Arial" font-size="14">${vibe} Style</text>
    </svg>
  `)}`

  const tablescapeIcon = `data:image/svg+xml;base64,${btoa(`
    <svg width="400" height="300" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="300" fill="#F0FDF4"/>
      <circle cx="200" cy="150" r="60" fill="#DCFCE7" stroke="#22C55E" stroke-width="2"/>
      <path d="M170 130 L190 150 L230 110" stroke="#22C55E" stroke-width="2" fill="none"/>
      <text x="200" y="240" text-anchor="middle" fill="#15803D" font-family="Arial" font-size="16">Reception Tablescape</text>
      <text x="200" y="260" text-anchor="middle" fill="#16A34A" font-family="Arial" font-size="14">${vibe} Style</text>
    </svg>
  `)}`

  return [
    {
      id: generateClientSideId(),
      imageUrl: ceremonyIcon,
      caption: `Beautiful ${vibeKeyword} ceremony decorations.`,
      tags: ["ceremony", "decor", vibeKeyword],
    },
    {
      id: generateClientSideId(),
      imageUrl: tablescapeIcon,
      caption: `Elegant ${vibeKeyword} reception tablescape.`,
      tags: ["reception", "tablescape", vibeKeyword],
    },
  ]
}
