/**
 * Utility functions for managing Gemini API keys from multiple sources
 */

/**
 * Get Gemini API key from multiple sources with priority order
 * 1. User-provided key from localStorage (with error handling)
 * 2. Environment variables
 *
 * @returns The API key string or undefined if not found
 */
export const getGeminiApiKey = (): string | undefined => {
  // 1. First check user-provided key from localStorage with error handling
  try {
    const userApiKey = localStorage.getItem("sayyes_gemini_api_key")
    if (userApiKey && userApiKey.trim()) {
      return userApiKey.trim()
    }
  } catch (error) {
    // Handle cases where localStorage is not available (SSR, incognito mode, etc.)
    console.warn("Failed to access localStorage for API key:", error)
  }

  // 2. Fall back to environment variables
  const envApiKey =
    typeof process !== "undefined" && process.env && process.env.API_KEY
      ? process.env.API_KEY
      : undefined

  return envApiKey
}

/**
 * Get the source of the current API key for debugging purposes
 *
 * @returns "user" | "environment" | "none"
 */
export const getGeminiApiKeySource = (): "user" | "environment" | "none" => {
  // Check user-provided key from localStorage with error handling
  try {
    const userApiKey = localStorage.getItem("sayyes_gemini_api_key")
    if (userApiKey && userApiKey.trim()) {
      return "user"
    }
  } catch (error) {
    // Handle cases where localStorage is not available
    console.warn(
      "Failed to access localStorage for API key source check:",
      error
    )
  }

  // Check environment variables
  const envApiKey =
    typeof process !== "undefined" && process.env && process.env.API_KEY
      ? process.env.API_KEY
      : undefined

  return envApiKey ? "environment" : "none"
}
