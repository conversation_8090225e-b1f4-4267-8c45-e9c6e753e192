import { DEFAULT_WEDDING_PHOTO_URL } from "@/src/constants"
import { Task, VisionBoardItem, WeddingDetails } from "@/src/types/index"
import { parseDateStringToLocalMidnight } from "./dateUtils"

/**
 * Utility functions for overview page statistics and calculations
 */

export interface VisionBoardSummary {
  imageUrl: string
  itemCount: number
}

export interface PriorityTasksData {
  tasks: Task[]
  totalCount: number
}

export const getVisionBoardSummary = (
  visionBoardItems: VisionBoardItem[] | undefined,
  weddingDetails: WeddingDetails
): VisionBoardSummary => {
  const imageUrl =
    visionBoardItems && visionBoardItems.length > 0
      ? visionBoardItems[0].imageUrl
      : weddingDetails?.vibe
      ? `data:image/svg+xml;base64,${btoa(`
          <svg width="400" height="200" viewBox="0 0 400 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#FDF2F8;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#F3E8FF;stop-opacity:1" />
              </linearGradient>
              <linearGradient id="flowerGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#EC4899;stop-opacity:0.8" />
                <stop offset="100%" style="stop-color:#A855F7;stop-opacity:0.8" />
              </linearGradient>
            </defs>
            <rect width="400" height="200" fill="url(#bg)"/>
            
            <!-- Decorative flowers -->
            <g transform="translate(80, 60)">
              <circle cx="0" cy="0" r="12" fill="url(#flowerGrad)" opacity="0.7"/>
              <circle cx="-8" cy="-8" r="8" fill="#F472B6" opacity="0.6"/>
              <circle cx="8" cy="-8" r="8" fill="#F472B6" opacity="0.6"/>
              <circle cx="-8" cy="8" r="8" fill="#F472B6" opacity="0.6"/>
              <circle cx="8" cy="8" r="8" fill="#F472B6" opacity="0.6"/>
              <circle cx="0" cy="0" r="4" fill="#FBBF24"/>
            </g>
            
            <g transform="translate(320, 140)">
              <circle cx="0" cy="0" r="10" fill="url(#flowerGrad)" opacity="0.7"/>
              <circle cx="-6" cy="-6" r="6" fill="#F472B6" opacity="0.6"/>
              <circle cx="6" cy="-6" r="6" fill="#F472B6" opacity="0.6"/>
              <circle cx="-6" cy="6" r="6" fill="#F472B6" opacity="0.6"/>
              <circle cx="6" cy="6" r="6" fill="#F472B6" opacity="0.6"/>
              <circle cx="0" cy="0" r="3" fill="#FBBF24"/>
            </g>
            
            <!-- Wedding rings -->
            <g transform="translate(200, 100)">
              <circle cx="-15" cy="0" r="18" fill="none" stroke="#D97706" stroke-width="3" opacity="0.8"/>
              <circle cx="15" cy="0" r="18" fill="none" stroke="#D97706" stroke-width="3" opacity="0.8"/>
              <circle cx="-15" cy="0" r="12" fill="none" stroke="#F59E0B" stroke-width="2"/>
              <circle cx="15" cy="0" r="12" fill="none" stroke="#F59E0B" stroke-width="2"/>
            </g>
            
            <!-- Decorative hearts -->
            <g transform="translate(150, 40)">
              <path d="M0,8 C0,3 4,0 8,0 C12,0 16,3 16,8 C16,13 8,20 8,20 C8,20 0,13 0,8 Z" fill="#EC4899" opacity="0.6"/>
            </g>
            
            <g transform="translate(240, 160)">
              <path d="M0,6 C0,2 3,0 6,0 C9,0 12,2 12,6 C12,10 6,15 6,15 C6,15 0,10 0,6 Z" fill="#EC4899" opacity="0.6"/>
            </g>
            
            <text x="200" y="180" text-anchor="middle" fill="#BE185D" font-family="serif" font-size="16" font-weight="600">${weddingDetails.vibe} Wedding Vision</text>
          </svg>
        `)}`
      : DEFAULT_WEDDING_PHOTO_URL

  return {
    imageUrl,
    itemCount: visionBoardItems ? visionBoardItems.length : 0,
  }
}

export const getPriorityTasks = (
  tasks: Task[] | undefined
): PriorityTasksData => {
  const todayForComparison = parseDateStringToLocalMidnight(
    new Date().toISOString().split("T")[0]
  )

  const filteredTasks = (tasks || [])
    .filter((task) => !task.isCompleted && !task.isPlannerInternal)
    .sort((a, b) => {
      const dateA = parseDateStringToLocalMidnight(a.dueDate)
      const dateB = parseDateStringToLocalMidnight(b.dueDate)
      if (dateA && dateB) return dateA.getTime() - dateB.getTime()
      if (dateA) return -1
      if (dateB) return 1
      return 0
    })

  return {
    tasks: filteredTasks,
    totalCount: filteredTasks.length,
  }
}

export const isTaskOverdue = (dueDate: string): boolean => {
  const todayForComparison = parseDateStringToLocalMidnight(
    new Date().toISOString().split("T")[0]
  )
  const taskDueDateObj = parseDateStringToLocalMidnight(dueDate)
  return (
    taskDueDateObj !== null &&
    todayForComparison !== null &&
    taskDueDateObj < todayForComparison
  )
}
