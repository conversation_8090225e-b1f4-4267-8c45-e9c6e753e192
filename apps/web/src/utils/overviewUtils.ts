import { DEFAULT_WEDDING_PHOTO_URL } from "@/src/constants"
import { Task, VisionBoardItem, WeddingDetails } from "@/src/types/index"
import { parseDateStringToLocalMidnight } from "./dateUtils"

/**
 * Utility functions for overview page statistics and calculations
 */

export interface VisionBoardSummary {
  imageUrl: string
  itemCount: number
}

export interface PriorityTasksData {
  tasks: Task[]
  totalCount: number
}

export const getVisionBoardSummary = (
  visionBoardItems: VisionBoardItem[] | undefined,
  weddingDetails: WeddingDetails
): VisionBoardSummary => {
  const imageUrl =
    visionBoardItems && visionBoardItems.length > 0
      ? visionBoardItems[0].imageUrl
      : weddingDetails?.vibe
      ? `data:image/svg+xml;base64,${btoa(`
          <svg width="400" height="200" viewBox="0 0 400 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="400" height="200" fill="#F0F9FF"/>
            <circle cx="200" cy="100" r="50" fill="#DBEAFE" stroke="#3B82F6" stroke-width="2"/>
            <path d="M175 85 L190 100 L225 65" stroke="#3B82F6" stroke-width="3" fill="none"/>
            <text x="200" y="170" text-anchor="middle" fill="#1E40AF" font-family="Arial" font-size="16">${weddingDetails.vibe} Wedding</text>
          </svg>
        `)}`
      : DEFAULT_WEDDING_PHOTO_URL

  return {
    imageUrl,
    itemCount: visionBoardItems ? visionBoardItems.length : 0,
  }
}

export const getPriorityTasks = (
  tasks: Task[] | undefined
): PriorityTasksData => {
  const todayForComparison = parseDateStringToLocalMidnight(
    new Date().toISOString().split("T")[0]
  )

  const filteredTasks = (tasks || [])
    .filter((task) => !task.isCompleted && !task.isPlannerInternal)
    .sort((a, b) => {
      const dateA = parseDateStringToLocalMidnight(a.dueDate)
      const dateB = parseDateStringToLocalMidnight(b.dueDate)
      if (dateA && dateB) return dateA.getTime() - dateB.getTime()
      if (dateA) return -1
      if (dateB) return 1
      return 0
    })

  return {
    tasks: filteredTasks,
    totalCount: filteredTasks.length,
  }
}

export const isTaskOverdue = (dueDate: string): boolean => {
  const todayForComparison = parseDateStringToLocalMidnight(
    new Date().toISOString().split("T")[0]
  )
  const taskDueDateObj = parseDateStringToLocalMidnight(dueDate)
  return (
    taskDueDateObj !== null &&
    todayForComparison !== null &&
    taskDueDateObj < todayForComparison
  )
}
