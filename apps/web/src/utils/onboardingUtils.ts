import {
  BUDGET_OPTIONS,
  DEFAULT_FORM_VALUES,
  GUEST_COUNT_OPTIONS,
  VIBE_OPTIONS,
} from "@/src/constants/onboardingConstants"
import { WeddingDetails } from "@/src/types/index"

export interface AddWeddingFormData {
  coupleNames: string
  weddingDate: string
  guestCount: number
  vibe: string
  customVibe: string
  location: string
  initialBudget: number
}

export const createInitialFormData = (): AddWeddingFormData => ({
  coupleNames: "",
  weddingDate: DEFAULT_FORM_VALUES.weddingDate(),
  guestCount: DEFAULT_FORM_VALUES.guestCount,
  vibe: DEFAULT_FORM_VALUES.vibe,
  customVibe: "",
  location: "",
  initialBudget: DEFAULT_FORM_VALUES.initialBudget,
})

export const validateOnboardingForm = (
  formData: AddWeddingFormData,
  isEditMode: boolean = false
): {
  isValid: boolean
  errors: Partial<Record<keyof AddWeddingFormData, string>>
} => {
  const errors: Partial<Record<keyof AddWeddingFormData, string>> = {}

  if (!formData.coupleNames.trim()) {
    errors.coupleNames = "Client's couple names are required."
  }

  if (!formData.weddingDate) {
    errors.weddingDate = "Client's wedding date is required."
  } else if (new Date(formData.weddingDate) <= new Date() && !isEditMode) {
    errors.weddingDate = "Wedding date must be in the future for new clients."
  }

  if (!formData.location.trim()) {
    errors.location = "Client's location is required."
  }

  if (formData.vibe === "Other" && !formData.customVibe.trim()) {
    errors.customVibe = "Please describe client's custom vibe."
  }

  if (formData.guestCount <= 0) {
    errors.guestCount = "Please select a valid guest count range."
  }

  if (
    formData.initialBudget <= 0 &&
    formData.initialBudget !== BUDGET_OPTIONS[0].value
  ) {
    errors.initialBudget = "Please select a valid budget range."
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  }
}

export const convertFormDataToWeddingDetails = (
  formData: AddWeddingFormData
): WeddingDetails => {
  const finalVibe =
    formData.vibe === "Other" ? formData.customVibe : formData.vibe

  return {
    coupleNames: formData.coupleNames,
    weddingDate: formData.weddingDate,
    guestCount: formData.guestCount,
    vibe: finalVibe,
    location: formData.location,
    initialBudget: formData.initialBudget,
  }
}

export const convertWeddingDetailsToFormData = (
  details: WeddingDetails
): AddWeddingFormData => {
  let formVibe = details.vibe
  let formCustomVibe = ""

  if (!VIBE_OPTIONS.includes(details.vibe)) {
    formVibe = "Other"
    formCustomVibe = details.vibe
  }

  return {
    coupleNames: details.coupleNames,
    weddingDate: details.weddingDate
      ? new Date(details.weddingDate).toISOString().split("T")[0]
      : "",
    guestCount:
      GUEST_COUNT_OPTIONS.find((opt) => opt.value >= details.guestCount)
        ?.value ||
      details.guestCount ||
      DEFAULT_FORM_VALUES.guestCount,
    vibe: formVibe,
    customVibe: formCustomVibe,
    location: details.location,
    initialBudget:
      BUDGET_OPTIONS.find((opt) => opt.value >= (details.initialBudget || 0))
        ?.value ||
      details.initialBudget ||
      DEFAULT_FORM_VALUES.initialBudget,
  }
}

export const processFormInputValue = (
  name: string,
  value: string,
  type?: string
): string | number => {
  if (name === "guestCount" || name === "initialBudget") {
    return parseInt(value, 10)
  } else if (type === "number") {
    const numValue = parseInt(value, 10)
    return isNaN(numValue) ? 0 : numValue
  }
  return value
}
