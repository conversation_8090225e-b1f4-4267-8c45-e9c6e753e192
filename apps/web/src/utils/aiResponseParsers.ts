export const parseJsonResponse = <T>(text: string, fallback: T): T => {
  let jsonStr = text.trim()

  // Attempt to extract from markdown fence if present
  // More flexible regex for fences: ```json ... ``` or ``` ... ```
  const fenceRegex = /```(?:\w*\s*\n)?([\s\S]*?)\n?\s*```/
  const fenceMatch = jsonStr.match(fenceRegex)

  if (fenceMatch && fenceMatch[1]) {
    jsonStr = fenceMatch[1].trim()
  } else {
    // If no markdown fence, try to find the first '{' or '[' and its corresponding '}' or ']'.
    // This helps if the JSON is embedded within other text without proper fences.
    let firstChar = ""
    let lastChar = ""
    let startIndex = -1
    let endIndex = -1

    const firstBracket = jsonStr.indexOf("[")
    const firstBrace = jsonStr.indexOf("{")

    // Determine if an array or object is the likely outermost structure
    if (
      firstBracket !== -1 &&
      (firstBrace === -1 || firstBracket < firstBrace)
    ) {
      startIndex = firstBracket
      firstChar = "["
      lastChar = "]"
    } else if (
      firstBrace !== -1 &&
      (firstBracket === -1 || firstBrace < firstBracket)
    ) {
      startIndex = firstBrace
      firstChar = "{"
      lastChar = "}"
    }

    if (startIndex !== -1) {
      let balance = 0
      let inString = false
      for (let i = startIndex; i < jsonStr.length; i++) {
        const char = jsonStr[i]
        if (char === "\\" && i + 1 < jsonStr.length) {
          // Handle escaped characters
          i++ // Skip next character
          continue
        }
        if (char === '"') {
          inString = !inString
        }
        if (!inString) {
          // Only adjust balance if not inside a string
          if (char === firstChar) {
            balance++
          } else if (char === lastChar) {
            balance--
          }
        }
        if (balance === 0 && i >= startIndex) {
          // Found the end of the outermost structure
          endIndex = i
          break
        }
      }
      if (endIndex !== -1) {
        jsonStr = jsonStr.substring(startIndex, endIndex + 1)
      }
      // If endIndex is not found (imbalanced structure), jsonStr remains as is,
      // and parsing will likely fail, which is handled by the catch block.
    }
  }

  try {
    return JSON.parse(jsonStr) as T
  } catch (e) {
    const originalTextSample =
      text.length > 500 ? text.substring(0, 497) + "..." : text
    const attemptedParseSample =
      jsonStr.length > 500 ? jsonStr.substring(0, 497) + "..." : jsonStr
    console.error(
      "Failed to parse JSON response. Error:",
      e,
      "\nAttempted to parse:",
      attemptedParseSample,
      "\nOriginal full text (sample):",
      originalTextSample
    )
    return fallback
  }
}
