// src/utils/dateUtils.ts

/**
 * Parses a "YYYY-MM-DD" string into a Date object representing midnight in the local timezone.
 * @param dateString The date string to parse.
 * @returns A Date object or null if the string is invalid.
 */
export const parseDateStringToLocalMidnight = (dateString: string | undefined | null): Date | null => {
  if (!dateString || !/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return null;
  }
  const [year, month, day] = dateString.split('-').map(Number);
  // JavaScript's Date month is 0-indexed (0 for January, 11 for December)
  return new Date(year, month - 1, day);
};

/**
 * Formats a Date object (assumed to be local midnight) into a display string.
 * @param date The Date object to format.
 * @returns A localized date string or 'Invalid Date' if input is null.
 */
export const formatLocalDateToDisplayString = (date: Date | null): string => {
  if (!date) {
    return 'No Date';
  }
  // Options to ensure a consistent format, can be adjusted as needed
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'numeric', day: 'numeric' };
  return date.toLocaleDateString(undefined, options);
};

/**
 * Parses a "YYYY-MM-DD" string and formats it directly for display.
 * @param dateString The date string to parse and format.
 * @returns A localized date string or 'Invalid Date'.
 */
export const parseAndFormatDateStringForDisplay = (dateString: string | undefined | null): string => {
  const dateObj = parseDateStringToLocalMidnight(dateString);
  return formatLocalDateToDisplayString(dateObj);
};
