import { ChatMessage } from "@/src/types/index"

/**
 * Utility functions for chat message handling and management
 */

export const cleanupDuplicateWelcomeMessages = (
  chatHistory: ChatMessage[]
): ChatMessage[] => {
  const welcomeMessages = chatHistory.filter(
    (msg) =>
      msg.sender === "ella" &&
      (msg.text.includes("Welcome to the portal") ||
        msg.id.includes("ella-welcome"))
  )

  if (welcomeMessages.length <= 1) {
    return chatHistory // No duplicates to clean
  }

  // Keep the first welcome message and remove duplicates
  const firstWelcomeId = welcomeMessages[0].id
  return chatHistory.filter((msg) => {
    if (
      msg.sender === "ella" &&
      (msg.text.includes("Welcome to the portal") ||
        msg.id.includes("ella-welcome"))
    ) {
      return msg.id === firstWelcomeId // Keep only the first one
    }
    return true // Keep all non-welcome messages
  })
}

export const hasWelcomeMessage = (chatHistory: ChatMessage[]): boolean => {
  return chatHistory.some(
    (msg) =>
      msg.sender === "ella" &&
      (msg.text.includes("Welcome to the portal") ||
        msg.id.includes("ella-welcome"))
  )
}

export const addMessageIfNotExists = (
  chatHistory: ChatMessage[],
  newMessage: ChatMessage
): { updated: boolean; history: ChatMessage[] } => {
  // For welcome messages, check if one already exists
  if (
    newMessage.sender === "ella" &&
    (newMessage.text.includes("Welcome to the portal") ||
      newMessage.id.includes("ella-welcome"))
  ) {
    if (hasWelcomeMessage(chatHistory)) {
      return { updated: false, history: chatHistory }
    }
  }

  // For other messages, check for exact duplicates by content and sender
  const isDuplicate = chatHistory.some(
    (msg) =>
      msg.text === newMessage.text &&
      msg.sender === newMessage.sender &&
      Math.abs(
        new Date(msg.timestamp).getTime() -
          new Date(newMessage.timestamp).getTime()
      ) < 5000 // within 5 seconds
  )

  if (isDuplicate) {
    return { updated: false, history: chatHistory }
  }

  return { updated: true, history: [...chatHistory, newMessage] }
}

export const createWelcomeMessage = (
  weddingId: string,
  coupleNames: string
): ChatMessage => {
  return {
    id: `ella-welcome-${weddingId}-${Date.now()}`,
    text: `Welcome to the portal for ${coupleNames}! I'm Ella, ready to assist you, the planner. What can I help you with for this wedding?`,
    sender: "ella",
    timestamp: new Date(),
  }
}

export const createUserMessage = (
  messageText: string,
  prefix: string = "user"
): ChatMessage => {
  return {
    id: `${prefix}-${Date.now()}`,
    text: messageText,
    sender: "user",
    timestamp: new Date(),
  }
}

export const createEllaMessage = (
  responseText: string,
  prefix: string = "ella"
): ChatMessage => {
  return {
    id: `${prefix}-${Date.now()}`,
    text: responseText,
    sender: "ella",
    timestamp: new Date(),
  }
}

export const createErrorMessage = (prefix: string = "error"): ChatMessage => {
  return {
    id: `${prefix}-${Date.now()}`,
    text: "Sorry, I encountered an issue. Please try again.",
    sender: "system",
    timestamp: new Date(),
  }
}
