/**
 * Utility functions for vendor data formatting and manipulation
 */

export const formatRating = (score?: number) => {
  if (!score) return "No rating"
  return `${score.toFixed(1)} ⭐`
}

export const formatWebsite = (url?: string): string | undefined => {
  if (!url) return undefined
  // Ensure URL has protocol
  const formattedUrl = url.startsWith("http") ? url : `https://${url}`
  return formattedUrl
}

export const truncateDescription = (
  description: string,
  maxLength: number = 150
) => {
  if (description.length <= maxLength) return description
  return description.substring(0, maxLength) + "..."
}
