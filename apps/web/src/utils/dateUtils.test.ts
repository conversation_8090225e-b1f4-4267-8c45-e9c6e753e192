import { describe, expect, it } from "bun:test"
import {
  formatLocalDateToDisplayString,
  parseAndFormatDateStringForDisplay,
  parseDateStringToLocalMidnight,
} from "./dateUtils"

describe("dateUtils", () => {
  describe("parseDateStringToLocalMidnight", () => {
    it("should parse valid date string correctly", () => {
      const result = parseDateStringToLocalMidnight("2024-01-15")
      expect(result).toBeInstanceOf(Date)
      expect(result?.getFullYear()).toBe(2024)
      expect(result?.getMonth()).toBe(0) // January is 0-indexed
      expect(result?.getDate()).toBe(15)
    })

    it("should return null for invalid date string", () => {
      expect(parseDateStringToLocalMidnight("invalid-date")).toBeNull()
      expect(parseDateStringToLocalMidnight("2024-13-01")).not.toBeNull() // Let Date constructor handle invalid dates
      expect(parseDateStringToLocalMidnight("")).toBeNull()
    })

    it("should return null for null or undefined input", () => {
      expect(parseDateStringToLocalMidnight(null)).toBeNull()
      expect(parseDateStringToLocalMidnight(undefined)).toBeNull()
    })

    it("should return null for incorrectly formatted strings", () => {
      expect(parseDateStringToLocalMidnight("2024/01/15")).toBeNull()
      expect(parseDateStringToLocalMidnight("24-01-15")).toBeNull()
      expect(parseDateStringToLocalMidnight("2024-1-15")).toBeNull()
    })
  })

  describe("formatLocalDateToDisplayString", () => {
    it("should format valid date correctly", () => {
      const date = new Date(2024, 0, 15) // January 15, 2024
      const result = formatLocalDateToDisplayString(date)
      expect(result).toMatch(/\d+\/\d+\/\d+/) // Should match localized date format
      expect(result).toContain("2024")
    })

    it('should return "No Date" for null input', () => {
      expect(formatLocalDateToDisplayString(null)).toBe("No Date")
    })
  })

  describe("parseAndFormatDateStringForDisplay", () => {
    it("should parse and format valid date string", () => {
      const result = parseAndFormatDateStringForDisplay("2024-01-15")
      expect(result).toMatch(/\d+\/\d+\/\d+/)
      expect(result).toContain("2024")
    })

    it('should return "No Date" for invalid input', () => {
      expect(parseAndFormatDateStringForDisplay("invalid-date")).toBe("No Date")
      expect(parseAndFormatDateStringForDisplay(null)).toBe("No Date")
      expect(parseAndFormatDateStringForDisplay(undefined)).toBe("No Date")
    })
  })
})
