import { BudgetFormState, BudgetItemType } from "@/src/utils/budgetUtils"
import { generateClientSideId } from "@/src/utils/idUtils"

/**
 * Utility functions for budget page operations and calculations
 */

export interface BudgetAISuggestionCallbacks {
  onProactiveAdd: () => void
  onBalancedSuggestion: (item: BudgetFormState) => void
  onReviewSuggestion: (item: BudgetFormState) => void
}

export const createBudgetItemFromSuggestion = (
  suggestion: BudgetFormState
): BudgetItemType => {
  return {
    id: generateClientSideId(),
    ...suggestion,
  }
}

export const getAutonomyButtonText = (autonomyLevel: string): string => {
  switch (autonomyLevel?.toLowerCase()) {
    case "proactive":
      return "Get Proactive AI Budget Suggestions"
    case "balanced":
      return "Get Balanced AI Budget Suggestions"
    case "conservative":
      return "Get Conservative AI Budget Suggestions"
    default:
      return "Get AI Budget Suggestions"
  }
}

export const handleAISuggestionSuccess = (
  item: BudgetFormState,
  category: string,
  addToast: (message: string, type: string) => void
) => {
  addToast(`Added Ella&apos;s suggestion for client: ${category}`, "success")
}

export const handleAISuggestionDismiss = (
  addToast: (message: string, type: string) => void
) => {
  addToast("AI suggestion dismissed.", "info")
}

export const validateBudgetSolutionRequest = (
  weddingDetails: any,
  addToast: (message: string, type: string) => void
): boolean => {
  if (!weddingDetails) {
    addToast(
      "Client&apos;s wedding details are not available for AI suggestion.",
      "error"
    )
    return false
  }
  return true
}
