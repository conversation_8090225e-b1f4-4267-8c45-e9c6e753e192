import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
} from "@/src/components/icons/HeroIcons"
import { BudgetItem, ManagedWedding, Task } from "@/src/types/index"

export type SortOption =
  | "createdAt-desc"
  | "createdAt-asc"
  | "weddingDate-asc"
  | "weddingDate-desc"
  | "coupleName-asc"
  | "coupleName-desc"
  | "budget-desc"
  | "budget-asc"

export interface BudgetStatus {
  status: string
  Icon: typeof InformationCircleIcon
  color: string
  text: string
}

export interface TaskProgress {
  text: string
  completed: number
  total: number
  percentage: number
  allDone?: boolean
}

export const calculateTotalEstimatedBudget = (
  budgetItems: BudgetItem[],
  initialBudget?: number
): number => {
  if (budgetItems && budgetItems.length > 0) {
    return budgetItems.reduce((sum, item) => sum + (item.estimatedCost || 0), 0)
  }
  return initialBudget || 0
}

export const getBudgetStatus = (wedding: ManagedWedding): BudgetStatus => {
  const totalEstimatedFromItems = wedding.budgetItems.reduce(
    (sum, item) => sum + (item.estimatedCost || 0),
    0
  )
  const totalActual = wedding.budgetItems.reduce(
    (sum, item) => sum + (item.actualCost || 0),
    0
  )
  const initialClientBudget = wedding.weddingDetails.initialBudget || 0

  // Consider the greater of itemized estimated budget or initial client idea for "total estimated"
  const totalEstimated = Math.max(totalEstimatedFromItems, initialClientBudget)

  if (
    totalEstimated === 0 &&
    totalActual === 0 &&
    wedding.budgetItems.length === 0
  ) {
    return {
      status: "Needs Setup",
      Icon: InformationCircleIcon,
      color: "text-amber-500",
      text: "Budget not set up",
    }
  }
  if (
    totalEstimated > 0 &&
    wedding.budgetItems.length === 0 &&
    initialClientBudget > 0 &&
    totalActual === 0
  ) {
    return {
      status: "Budget Set, No Items",
      Icon: InformationCircleIcon,
      color: "text-amber-500",
      text: `Initial Idea: $${initialClientBudget.toLocaleString()}`,
    }
  }
  if (totalEstimated > 0 && totalActual === 0) {
    // Has items, but no actuals yet
    return {
      status: "Attention",
      Icon: InformationCircleIcon,
      color: "text-amber-500",
      text: `Est: $${totalEstimated.toLocaleString()}`,
    }
  }
  if (totalActual > totalEstimated && totalEstimated > 0) {
    return {
      status: "Over Budget",
      Icon: ExclamationTriangleIcon,
      color: "text-destructive",
      text: `Over by $${(totalActual - totalEstimated).toLocaleString()}`,
    }
  }
  if (totalActual > 0 && totalActual <= totalEstimated) {
    return {
      status: "On Track",
      Icon: CheckCircleIcon,
      color: "text-positive",
      text: `Actual: $${totalActual.toLocaleString()} / Est: $${totalEstimated.toLocaleString()}`,
    }
  }
  return {
    status: "Info",
    Icon: InformationCircleIcon,
    color: "text-muted-foreground",
    text: `Est: $${totalEstimated.toLocaleString()}, Actual: $${totalActual.toLocaleString()}`,
  }
}

export const getTaskProgress = (wedding: ManagedWedding): TaskProgress => {
  const totalTasks = wedding.tasks.length
  if (totalTasks === 0)
    return { text: "No tasks defined", completed: 0, total: 0, percentage: 0 }
  const completedTasks = wedding.tasks.filter((task) => task.isCompleted).length
  const percentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0
  if (completedTasks === totalTasks) {
    return {
      text: "All tasks complete!",
      completed: completedTasks,
      total: totalTasks,
      percentage: 100,
      allDone: true,
    }
  }
  return {
    text: `${completedTasks} / ${totalTasks} tasks complete`,
    completed: completedTasks,
    total: totalTasks,
    percentage,
    allDone: false,
  }
}

export const getNextCriticalTask = (wedding: ManagedWedding): Task | null => {
  const upcomingTasks = wedding.tasks
    .filter((task) => !task.isCompleted && task.dueDate)
    .sort(
      (a, b) => new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime()
    )
  return upcomingTasks.length > 0 ? upcomingTasks[0] : null
}

export const sortWeddings = (
  weddings: ManagedWedding[],
  sortOption: SortOption
): ManagedWedding[] => {
  return [...weddings].sort((a, b) => {
    switch (sortOption) {
      case "createdAt-asc":
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
      case "weddingDate-asc": {
        const dateA = a.weddingDetails.weddingDate
          ? new Date(a.weddingDetails.weddingDate).getTime()
          : Infinity
        const dateB = b.weddingDetails.weddingDate
          ? new Date(b.weddingDetails.weddingDate).getTime()
          : Infinity
        return dateA - dateB
      }
      case "weddingDate-desc": {
        const dateA = a.weddingDetails.weddingDate
          ? new Date(a.weddingDetails.weddingDate).getTime()
          : -Infinity
        const dateB = b.weddingDetails.weddingDate
          ? new Date(b.weddingDetails.weddingDate).getTime()
          : -Infinity
        return dateB - dateA
      }
      case "coupleName-asc":
        return (a.weddingDetails.coupleNames || "").localeCompare(
          b.weddingDetails.coupleNames || ""
        )
      case "coupleName-desc":
        return (b.weddingDetails.coupleNames || "").localeCompare(
          a.weddingDetails.coupleNames || ""
        )
      case "budget-asc": {
        const budgetA = calculateTotalEstimatedBudget(
          a.budgetItems,
          a.weddingDetails.initialBudget
        )
        const budgetB = calculateTotalEstimatedBudget(
          b.budgetItems,
          b.weddingDetails.initialBudget
        )
        return budgetA - budgetB
      }
      case "budget-desc": {
        const budgetA = calculateTotalEstimatedBudget(
          a.budgetItems,
          a.weddingDetails.initialBudget
        )
        const budgetB = calculateTotalEstimatedBudget(
          b.budgetItems,
          b.weddingDetails.initialBudget
        )
        return budgetB - budgetA
      }
      case "createdAt-desc":
      default:
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    }
  })
}
