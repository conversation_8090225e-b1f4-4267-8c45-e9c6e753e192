# Environment Configuration
NODE_ENV=development

# ===================================
# AI/LLM Provider Configuration
# ===================================
OPENAI_API_KEY=""
ANTHROPIC_API_KEY=""
GEMINI_API_KEY=""
GROQ_API_KEY=""

# ===================================
# Supabase - Main Project (vl_wedding_planner)
# Project ID: gxlucamlneoombifnirp
# ===================================

# PostgreSQL
POSTGRES_URL=""
POSTGRES_URL_NON_POOLING=""
POSTGRES_USER=""
POSTGRES_HOST=""
POSTGRES_PRISMA_URL=""
POSTGRES_PASSWORD=""
POSTGRES_DATABASE=""

# Client-side variables (accessible in browser)
SUPABASE_URL=""
NEXT_PUBLIC_SUPABASE_URL=""
NEXT_PUBLIC_SUPABASE_ANON_KEY=""

# Server-side variables (not accessible in browser)
SUPABASE_SERVICE_ROLE_KEY=""
SUPABASE_JWT_SECRET=""

# Database connection (if direct Postgres access is needed)
POSTGRES_URL=""
POSTGRES_URL_NON_POOLING=""

# ===================================
# Supabase - Vendor Data Project (sayyes_data)
# Project ID: tuhzrxmrdghprrspftwe
# ===================================
# Client-side variables (accessible in browser)
NEXT_PUBLIC_SUPABASE_DATA_URL=""
NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY=""

# Server-side variables (not accessible in browser)
SUPABASE_DATA_SERVICE_ROLE_KEY=""