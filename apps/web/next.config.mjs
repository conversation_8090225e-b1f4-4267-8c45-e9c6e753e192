/** @type {import('next').NextConfig} */
const nextConfig = {
  // Turbopack is now stable in Next.js 15
  turbopack: {
    rules: {
      // Add any custom Turbopack rules if needed
    },
  },
  // Experimental features
  experimental: {
    // Add experimental features here when needed
  },
  // Image configuration for external domains
  images: {
    remotePatterns: [
      // Add external image domains here as needed
      // Example:
      // {
      //   protocol: 'https',
      //   hostname: 'example.com',
      //   port: '',
      //   pathname: '/**',
      // },
    ],
  },
  // Ensure proper transpilation in monorepo
  transpilePackages: ["agent-system"],
  // Optimize for development
  poweredByHeader: false,
}

export default nextConfig
