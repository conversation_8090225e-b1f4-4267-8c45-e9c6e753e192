{"name": "web", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev --turbo", "dev:no-turbo": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "bun tsc --noEmit", "test": "bun test", "test:watch": "bun test --watch", "test:coverage": "bun test --coverage"}, "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@google/genai": "^1.0.0", "@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@langchain/anthropic": "^0.3.22", "@langchain/core": "^0.3.58", "@langchain/google-genai": "^0.2.12", "@langchain/langgraph": "^0.3.3", "@langchain/openai": "^0.5.13", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.49.10", "eventsource-parser": "^3.0.2", "framer-motion": "^12.16.0", "graceful-fs": "^4.2.11", "next": "15.3.3", "react": "^19.1.0", "react-dom": "19.0.0-rc.1", "react-hot-toast": "^2.5.2", "zod": "^3.25.64"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.14.0", "@types/react": "^19.1.7", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "15.3.3", "happy-dom": "^17.6.3", "postcss": "^8.5.4", "tailwindcss": "^3.4.0", "typescript": "~5.7.2"}}