{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "isolatedModules": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "forceConsistentCasingInFileNames": true, "incremental": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}