{"name": "agent-system", "private": true, "version": "0.1.0", "type": "module", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "bun test --env-file=.env.test", "test:watch": "bun test --watch --env-file=.env.test", "lint": "eslint src", "typecheck": "tsc --noEmit"}, "dependencies": {"@langchain/langgraph": "^0.2.20", "@langchain/core": "^0.3.0", "@langchain/openai": "^0.3.0", "@langchain/anthropic": "^0.3.0", "@langchain/google-genai": "^0.1.0", "@copilotkit/react-core": "^0.37.0", "@copilotkit/backend": "^0.37.0", "@supabase/supabase-js": "^2.49.10", "openai": "^4.0.0", "@anthropic-ai/sdk": "^0.27.0", "@google/generative-ai": "^0.21.0", "zod": "^3.22.0", "eventsource-parser": "^1.1.0"}, "devDependencies": {"@types/node": "^20.14.0", "typescript": "~5.7.2", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^8.34.0", "@typescript-eslint/parser": "^8.34.0", "happy-dom": "^17.6.3"}}