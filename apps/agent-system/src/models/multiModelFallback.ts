import { ChatOpenAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { BaseMessage } from '@langchain/core/messages';

export type ModelProvider = 'openai' | 'anthropic' | 'google';

export interface ModelConfig {
  provider: ModelProvider;
  modelName: string;
  temperature?: number;
  maxTokens?: number;
  apiKey?: string;
}

export interface FallbackConfig {
  primary: ModelConfig;
  secondary: ModelConfig;
  tertiary: ModelConfig;
  maxRetries?: number;
  retryDelay?: number;
}

export class MultiModelFallback {
  private config: FallbackConfig;
  private models: Map<ModelProvider, BaseChatModel> = new Map();

  constructor(config?: Partial<FallbackConfig>) {
    this.config = {
      primary: {
        provider: 'openai',
        modelName: 'gpt-4o',
        temperature: 0.7,
        maxTokens: 4000,
        apiKey: process.env.OPENAI_API_KEY
      },
      secondary: {
        provider: 'anthropic',
        modelName: 'claude-3-5-sonnet-20241022',
        temperature: 0.7,
        maxTokens: 4000,
        apiKey: process.env.ANTHROPIC_API_KEY
      },
      tertiary: {
        provider: 'google',
        modelName: 'gemini-1.5-pro',
        temperature: 0.7,
        maxTokens: 4000,
        apiKey: process.env.GEMINI_API_KEY
      },
      maxRetries: 3,
      retryDelay: 1000,
      ...config
    };

    this.initializeModels();
  }

  private initializeModels(): void {
    // Initialize OpenAI model
    if (this.config.primary.apiKey || this.config.secondary.provider === 'openai' || this.config.tertiary.provider === 'openai') {
      this.models.set('openai', new ChatOpenAI({
        modelName: this.config.primary.provider === 'openai' ? this.config.primary.modelName : 'gpt-4o',
        temperature: this.config.primary.provider === 'openai' ? this.config.primary.temperature : 0.7,
        maxTokens: this.config.primary.provider === 'openai' ? this.config.primary.maxTokens : 4000,
        openAIApiKey: process.env.OPENAI_API_KEY
      }));
    }

    // Initialize Anthropic model
    if (this.config.secondary.apiKey || this.config.primary.provider === 'anthropic' || this.config.tertiary.provider === 'anthropic') {
      this.models.set('anthropic', new ChatAnthropic({
        modelName: this.config.secondary.provider === 'anthropic' ? this.config.secondary.modelName : 'claude-3-5-sonnet-20241022',
        temperature: this.config.secondary.provider === 'anthropic' ? this.config.secondary.temperature : 0.7,
        maxTokens: this.config.secondary.provider === 'anthropic' ? this.config.secondary.maxTokens : 4000,
        anthropicApiKey: process.env.ANTHROPIC_API_KEY
      }));
    }

    // Initialize Google model
    if (this.config.tertiary.apiKey || this.config.primary.provider === 'google' || this.config.secondary.provider === 'google') {
      this.models.set('google', new ChatGoogleGenerativeAI({
        modelName: this.config.tertiary.provider === 'google' ? this.config.tertiary.modelName : 'gemini-1.5-pro',
        temperature: this.config.tertiary.provider === 'google' ? this.config.tertiary.temperature : 0.7,
        maxOutputTokens: this.config.tertiary.provider === 'google' ? this.config.tertiary.maxTokens : 4000,
        apiKey: process.env.GEMINI_API_KEY
      }));
    }
  }

  async invoke(messages: BaseMessage[], options?: { preferredProvider?: ModelProvider }): Promise<BaseMessage> {
    const providers = this.getProviderOrder(options?.preferredProvider);
    
    for (const provider of providers) {
      const model = this.models.get(provider);
      if (!model) continue;

      try {
        console.log(`Attempting to use ${provider} model...`);
        const response = await model.invoke(messages);
        console.log(`Successfully used ${provider} model`);
        return response;
      } catch (error) {
        console.warn(`${provider} model failed:`, error);
        
        // Check if it's a rate limit error and wait before trying next model
        if (this.isRateLimitError(error)) {
          await this.delay(this.config.retryDelay || 1000);
        }
        
        // Continue to next provider
        continue;
      }
    }

    throw new Error('All model providers failed. Please check your API keys and try again.');
  }

  private getProviderOrder(preferredProvider?: ModelProvider): ModelProvider[] {
    const defaultOrder: ModelProvider[] = [
      this.config.primary.provider,
      this.config.secondary.provider,
      this.config.tertiary.provider
    ];

    if (preferredProvider && this.models.has(preferredProvider)) {
      // Move preferred provider to front
      const filtered = defaultOrder.filter(p => p !== preferredProvider);
      return [preferredProvider, ...filtered];
    }

    return defaultOrder;
  }

  private isRateLimitError(error: any): boolean {
    const errorMessage = error?.message?.toLowerCase() || '';
    const errorCode = error?.code || error?.status;
    
    return (
      errorMessage.includes('rate limit') ||
      errorMessage.includes('quota') ||
      errorMessage.includes('too many requests') ||
      errorCode === 429
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  getAvailableProviders(): ModelProvider[] {
    return Array.from(this.models.keys());
  }

  updateConfig(newConfig: Partial<FallbackConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.models.clear();
    this.initializeModels();
  }
}

// Singleton instance for global use
export const multiModelFallback = new MultiModelFallback();
