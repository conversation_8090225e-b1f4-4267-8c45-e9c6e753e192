import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { multiModelFallback } from '../models/multiModelFallback';
import { memoryManager } from '../memory/memoryManager';
import { OrchestratorStateType } from '../orchestratorGraph';
import { getBudgetStatus, updateBudgetItem, addBudgetCategory, generateBudgetReport } from '../tools/budgetTools';

/**
 * Budget Manager Agent - Handles all budget-related operations
 */
export async function budgetManagerNode(state: OrchestratorStateType): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1];
  const userMessage = lastMessage.content as string;
  const { weddingId, context } = state;

  // Get current budget status for context
  const budgetStatus = await getBudgetStatus(weddingId);
  
  // Get relevant memory context
  const memoryContext = await memoryManager.getAgentContext(
    weddingId,
    `budget ${userMessage}`,
    { limit: 3 }
  );

  const systemPrompt = `
You are the Budget Manager for <PERSON>'s wedding planning system. You specialize in budget management, cost tracking, and financial planning for weddings.

Current Budget Status:
${JSON.stringify(budgetStatus, null, 2)}

Memory Context:
${memoryContext}

Available Tools:
- getBudgetStatus(weddingId): Get current budget breakdown and spending
- updateBudgetItem(itemId, newAmount): Update budget allocation for a specific item
- addBudgetCategory(weddingId, category, estimatedCost): Add new budget category
- generateBudgetReport(weddingId): Generate comprehensive budget analysis

IMPORTANT GUIDELINES:
1. Always request user confirmation before making budget changes over $500
2. Provide clear explanations of budget implications
3. Suggest cost-saving alternatives when appropriate
4. Track all budget decisions in memory for future reference
5. Be transparent about spending vs. budget limits

User Request: "${userMessage}"

Analyze the request and determine what budget actions are needed. If you need to make significant changes (>$500), set requiresConfirmation to true and provide confirmation details.
`;

  try {
    // Determine what budget actions are needed
    const response = await multiModelFallback.invoke([
      new HumanMessage(systemPrompt)
    ]);

    let responseContent = response.content as string;
    let requiresConfirmation = false;
    let confirmationData = {};

    // Check if the request involves significant budget changes
    const budgetChangeMatch = userMessage.match(/(\$[\d,]+|\d+\s*dollars?)/i);
    if (budgetChangeMatch) {
      const amount = parseFloat(budgetChangeMatch[0].replace(/[$,]/g, ''));
      if (amount > 500) {
        requiresConfirmation = true;
        confirmationData = {
          action: 'budget_change',
          amount: amount,
          description: `Budget change of $${amount.toLocaleString()}`,
          userRequest: userMessage
        };
        
        responseContent = `I found that you want to make a budget change of $${amount.toLocaleString()}. This is a significant amount, so I'd like to confirm this with you first.\n\n${responseContent}\n\nWould you like me to proceed with this budget change?`;
      }
    }

    // Store the budget interaction in memory
    await memoryManager.storeDecision(
      weddingId,
      `Budget analysis for: ${userMessage}. Response: ${responseContent}`,
      'budget_manager',
      {
        budgetStatus: budgetStatus,
        requiresConfirmation,
        amount: budgetChangeMatch ? parseFloat(budgetChangeMatch[0].replace(/[$,]/g, '')) : null
      }
    );

    return {
      messages: [new AIMessage({ content: responseContent })],
      currentAgent: 'orchestrator',
      requiresConfirmation,
      confirmationData
    };

  } catch (error) {
    console.error('Budget Manager error:', error);
    
    const errorMessage = "I'm sorry, I encountered an issue while processing your budget request. Please try again or contact support if the problem persists.";
    
    return {
      messages: [new AIMessage({ content: errorMessage })],
      currentAgent: 'orchestrator'
    };
  }
}

/**
 * Budget analysis helper function
 */
export async function analyzeBudgetRequest(
  userMessage: string,
  budgetStatus: any,
  weddingId: string
): Promise<{
  action: string;
  requiresConfirmation: boolean;
  suggestedResponse: string;
  toolCalls?: Array<{ tool: string; params: any }>;
}> {
  const analysisPrompt = `
Analyze this budget-related request and determine the appropriate action:

User Request: "${userMessage}"
Current Budget Status: ${JSON.stringify(budgetStatus, null, 2)}

Determine:
1. What specific action is needed (view, update, add, analyze, report)
2. Whether user confirmation is required (for changes >$500)
3. What tools should be called
4. An appropriate response

Respond with JSON:
{
  "action": "view|update|add|analyze|report",
  "requiresConfirmation": boolean,
  "suggestedResponse": "string",
  "toolCalls": [{"tool": "toolName", "params": {...}}]
}
`;

  try {
    const response = await multiModelFallback.invoke([
      new HumanMessage(analysisPrompt)
    ]);

    const content = response.content as string;
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
  } catch (error) {
    console.error('Error analyzing budget request:', error);
  }

  // Fallback analysis
  const lowerMessage = userMessage.toLowerCase();
  
  if (lowerMessage.includes('status') || lowerMessage.includes('how much') || lowerMessage.includes('spent')) {
    return {
      action: 'view',
      requiresConfirmation: false,
      suggestedResponse: 'Let me get your current budget status for you.',
      toolCalls: [{ tool: 'getBudgetStatus', params: { weddingId } }]
    };
  }
  
  if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('increase')) {
    return {
      action: 'update',
      requiresConfirmation: true,
      suggestedResponse: 'I can help you update your budget. What specific changes would you like to make?'
    };
  }
  
  if (lowerMessage.includes('add') || lowerMessage.includes('new category')) {
    return {
      action: 'add',
      requiresConfirmation: false,
      suggestedResponse: 'I can help you add a new budget category. What category would you like to add and what\'s the estimated cost?'
    };
  }
  
  if (lowerMessage.includes('report') || lowerMessage.includes('analysis')) {
    return {
      action: 'report',
      requiresConfirmation: false,
      suggestedResponse: 'Let me generate a comprehensive budget report for you.',
      toolCalls: [{ tool: 'generateBudgetReport', params: { weddingId } }]
    };
  }

  return {
    action: 'view',
    requiresConfirmation: false,
    suggestedResponse: 'I can help you with budget management. Would you like to see your current budget status, make changes, or get a detailed report?'
  };
}
