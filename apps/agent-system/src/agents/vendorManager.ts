import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { multiModelFallback } from '../models/multiModelFallback';
import { memoryManager } from '../memory/memoryManager';
import { OrchestratorStateType } from '../orchestratorGraph';
import { searchVendors, getVendorDetails, checkVendorAvailability, bookVendor } from '../tools/vendorTools';

/**
 * Vendor Manager Agent - Handles vendor research, booking, and communication
 */
export async function vendorManagerNode(state: OrchestratorStateType): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1];
  const userMessage = lastMessage.content as string;
  const { weddingId, context } = state;

  // Get relevant memory context
  const memoryContext = await memoryManager.getAgentContext(
    weddingId,
    `vendor ${userMessage}`,
    { limit: 3 }
  );

  const systemPrompt = `
You are the Vendor Manager for <PERSON>'s wedding planning system. You specialize in vendor research, booking, availability checking, and vendor communication.

Memory Context:
${memoryContext}

Available Tools:
- searchVendors(category, location, maxPrice, weddingDate): Search vendor directory
- getVendorDetails(vendorId): Get detailed vendor information
- checkVendorAvailability(vendorId, weddingDate): Check if vendor is available
- bookVendor(vendorId, weddingId, details): Create vendor booking

IMPORTANT GUIDELINES:
1. Always request user confirmation before booking vendors
2. Check vendor availability before suggesting bookings
3. Provide multiple vendor options when possible
4. Include pricing information and reviews in recommendations
5. Store all vendor interactions in memory for future reference
6. Be transparent about vendor booking terms and conditions

User Request: "${userMessage}"

Analyze the request and determine what vendor actions are needed. If you need to book a vendor, set requiresConfirmation to true and provide booking details.
`;

  try {
    // Analyze the vendor request
    const analysis = await analyzeVendorRequest(userMessage, weddingId);
    
    let responseContent = '';
    let requiresConfirmation = false;
    let confirmationData = {};

    switch (analysis.action) {
      case 'search':
        const searchResults = await handleVendorSearch(userMessage, weddingId);
        responseContent = searchResults.response;
        break;
        
      case 'book':
        requiresConfirmation = true;
        confirmationData = {
          action: 'vendor_booking',
          vendorId: analysis.vendorId,
          description: `Book ${analysis.vendorType} vendor`,
          userRequest: userMessage
        };
        responseContent = `I found a ${analysis.vendorType} vendor that matches your requirements. Before I proceed with the booking, let me confirm the details with you.\n\nWould you like me to book this vendor?`;
        break;
        
      case 'details':
        if (analysis.vendorId) {
          const details = await getVendorDetails(analysis.vendorId);
          responseContent = formatVendorDetails(details);
        } else {
          responseContent = "I need a specific vendor ID to get detailed information. Could you specify which vendor you'd like to know more about?";
        }
        break;
        
      case 'availability':
        if (analysis.vendorId) {
          const availability = await checkVendorAvailability(analysis.vendorId, analysis.weddingDate || '');
          responseContent = `Vendor availability: ${availability.available ? 'Available' : 'Not available'} for your wedding date.`;
        } else {
          responseContent = "I need a specific vendor to check availability. Which vendor would you like me to check?";
        }
        break;
        
      default:
        const response = await multiModelFallback.invoke([
          new HumanMessage(systemPrompt)
        ]);
        responseContent = response.content as string;
    }

    // Store the vendor interaction in memory
    await memoryManager.storeDecision(
      weddingId,
      `Vendor request: ${userMessage}. Action: ${analysis.action}. Response: ${responseContent}`,
      'vendor_manager',
      {
        action: analysis.action,
        vendorType: analysis.vendorType,
        requiresConfirmation
      }
    );

    return {
      messages: [new AIMessage({ content: responseContent })],
      currentAgent: 'orchestrator',
      requiresConfirmation,
      confirmationData
    };

  } catch (error) {
    console.error('Vendor Manager error:', error);
    
    const errorMessage = "I'm sorry, I encountered an issue while processing your vendor request. Please try again or contact support if the problem persists.";
    
    return {
      messages: [new AIMessage({ content: errorMessage })],
      currentAgent: 'orchestrator'
    };
  }
}

/**
 * Analyze vendor request to determine action
 */
async function analyzeVendorRequest(
  userMessage: string,
  weddingId: string
): Promise<{
  action: 'search' | 'book' | 'details' | 'availability' | 'general';
  vendorType?: string;
  vendorId?: string;
  weddingDate?: string;
  location?: string;
  maxPrice?: number;
}> {
  const lowerMessage = userMessage.toLowerCase();
  
  // Extract vendor type
  const vendorTypes = ['photographer', 'florist', 'caterer', 'dj', 'band', 'venue', 'baker', 'videographer'];
  const vendorType = vendorTypes.find(type => lowerMessage.includes(type));
  
  // Extract price if mentioned
  const priceMatch = userMessage.match(/\$?([\d,]+)/);
  const maxPrice = priceMatch ? parseInt(priceMatch[1].replace(',', '')) : undefined;
  
  // Determine action based on keywords
  if (lowerMessage.includes('search') || lowerMessage.includes('find') || lowerMessage.includes('look for')) {
    return {
      action: 'search',
      vendorType,
      maxPrice
    };
  }
  
  if (lowerMessage.includes('book') || lowerMessage.includes('hire') || lowerMessage.includes('reserve')) {
    return {
      action: 'book',
      vendorType
    };
  }
  
  if (lowerMessage.includes('details') || lowerMessage.includes('information') || lowerMessage.includes('about')) {
    return {
      action: 'details',
      vendorType
    };
  }
  
  if (lowerMessage.includes('available') || lowerMessage.includes('availability')) {
    return {
      action: 'availability',
      vendorType
    };
  }

  return {
    action: 'search',
    vendorType
  };
}

/**
 * Handle vendor search requests
 */
async function handleVendorSearch(
  userMessage: string,
  weddingId: string
): Promise<{ response: string; vendors?: any[] }> {
  try {
    // Extract search parameters from the message
    const analysis = await analyzeVendorRequest(userMessage, weddingId);
    
    // Get wedding location for search
    // This would typically come from the wedding details
    const location = 'Austin, TX'; // Default for now
    
    const vendors = await searchVendors(
      analysis.vendorType || 'all',
      location,
      analysis.maxPrice,
      '' // Wedding date would come from wedding details
    );

    if (vendors.length === 0) {
      return {
        response: `I couldn't find any ${analysis.vendorType || 'vendors'} matching your criteria. Would you like me to expand the search or adjust the parameters?`
      };
    }

    let response = `I found ${vendors.length} ${analysis.vendorType || 'vendors'} for you:\n\n`;
    
    vendors.slice(0, 3).forEach((vendor, index) => {
      response += `${index + 1}. **${vendor.business_name}**\n`;
      response += `   📍 ${vendor.city}, ${vendor.state}\n`;
      if (vendor.review_score) {
        response += `   ⭐ ${vendor.review_score}/5 (${vendor.review_count} reviews)\n`;
      }
      if (vendor.pricing) {
        response += `   💰 Starting at $${vendor.pricing.starting_price || 'Contact for pricing'}\n`;
      }
      response += `   📞 ${vendor.phone}\n`;
      response += `   🌐 ${vendor.website}\n\n`;
    });

    if (vendors.length > 3) {
      response += `And ${vendors.length - 3} more options available. Would you like to see more details about any of these vendors or get additional options?`;
    } else {
      response += `Would you like more details about any of these vendors or help with booking?`;
    }

    return { response, vendors };

  } catch (error) {
    console.error('Error handling vendor search:', error);
    return {
      response: "I encountered an issue while searching for vendors. Please try again with more specific criteria."
    };
  }
}

/**
 * Format vendor details for display
 */
function formatVendorDetails(vendor: any): string {
  if (!vendor) {
    return "I couldn't find details for that vendor. Please check the vendor ID and try again.";
  }

  let details = `**${vendor.business_name}**\n\n`;
  details += `📍 **Location:** ${vendor.address}, ${vendor.city}, ${vendor.state} ${vendor.zip_code}\n`;
  details += `📞 **Phone:** ${vendor.phone}\n`;
  details += `📧 **Email:** ${vendor.email}\n`;
  details += `🌐 **Website:** ${vendor.website}\n\n`;
  
  if (vendor.description) {
    details += `**About:**\n${vendor.description}\n\n`;
  }
  
  if (vendor.review_score) {
    details += `⭐ **Rating:** ${vendor.review_score}/5 (${vendor.review_count} reviews)\n\n`;
  }
  
  if (vendor.pricing) {
    details += `💰 **Pricing:**\n`;
    if (vendor.pricing.starting_price) {
      details += `   Starting at: $${vendor.pricing.starting_price}\n`;
    }
    if (vendor.pricing.package_options) {
      details += `   Package options available\n`;
    }
    details += '\n';
  }
  
  if (vendor.photo_gallery && vendor.photo_gallery.length > 0) {
    details += `📸 **Portfolio:** ${vendor.photo_gallery.length} photos available\n\n`;
  }
  
  details += `Would you like me to check their availability for your wedding date or help you contact them?`;
  
  return details;
}
