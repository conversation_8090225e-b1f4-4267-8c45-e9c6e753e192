import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { multiModelFallback } from '../models/multiModelFallback';
import { memoryManager } from '../memory/memoryManager';
import { OrchestratorStateType } from '../orchestratorGraph';

/**
 * Guest Manager Agent - Handles guest list management, RSVP tracking, and invitations
 */
export async function guestManagerNode(state: OrchestratorStateType): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1];
  const userMessage = lastMessage.content as string;
  const { weddingId, context } = state;

  // Get relevant memory context
  const memoryContext = await memoryManager.getAgentContext(
    weddingId,
    `guest ${userMessage}`,
    { limit: 3 }
  );

  const systemPrompt = `
You are the Guest Manager for <PERSON>'s wedding planning system. You specialize in guest list management, RSVP tracking, invitation sending, and guest communication.

Memory Context:
${memoryContext}

Available Tools:
- addGuest(weddingId, guestInfo): Add new guest to the list
- updateRSVP(guestId, rsvpStatus): Update guest RSVP status
- getGuestList(weddingId): Get current guest list and statistics
- sendInvitations(weddingId, guestIds): Send invitations to specific guests
- generateGuestReport(weddingId): Generate guest list analysis

IMPORTANT GUIDELINES:
1. Always confirm guest details before adding to the list
2. Respect privacy and data protection for guest information
3. Provide clear RSVP tracking and statistics
4. Handle dietary restrictions and special accommodations
5. Store all guest interactions in memory for future reference

User Request: "${userMessage}"

Analyze the request and determine what guest management actions are needed.
`;

  try {
    const response = await multiModelFallback.invoke([
      new HumanMessage(systemPrompt)
    ]);

    const responseContent = response.content as string;

    // Store the guest interaction in memory
    await memoryManager.storeDecision(
      weddingId,
      `Guest management request: ${userMessage}. Response: ${responseContent}`,
      'guest_manager',
      {
        action: 'guest_management',
        userRequest: userMessage
      }
    );

    return {
      messages: [new AIMessage({ content: responseContent })],
      currentAgent: 'orchestrator'
    };

  } catch (error) {
    console.error('Guest Manager error:', error);
    
    const errorMessage = "I'm sorry, I encountered an issue while processing your guest management request. Please try again or contact support if the problem persists.";
    
    return {
      messages: [new AIMessage({ content: errorMessage })],
      currentAgent: 'orchestrator'
    };
  }
}
