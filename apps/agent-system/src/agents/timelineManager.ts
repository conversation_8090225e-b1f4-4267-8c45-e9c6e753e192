import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { multiModelFallback } from '../models/multiModelFallback';
import { memoryManager } from '../memory/memoryManager';
import { OrchestratorStateType } from '../orchestratorGraph';

/**
 * Timeline Manager Agent - Handles timeline creation, task management, and milestone tracking
 */
export async function timelineManagerNode(state: OrchestratorStateType): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1];
  const userMessage = lastMessage.content as string;
  const { weddingId, context } = state;

  // Get relevant memory context
  const memoryContext = await memoryManager.getAgentContext(
    weddingId,
    `timeline ${userMessage}`,
    { limit: 3 }
  );

  const systemPrompt = `
You are the Timeline Manager for <PERSON>'s wedding planning system. You specialize in timeline creation, task management, milestone tracking, and scheduling coordination.

Memory Context:
${memoryContext}

Available Tools:
- generateTimeline(weddingId, weddingDate): Create comprehensive wedding planning timeline
- addTask(weddingId, taskDetails): Add new task to timeline
- updateTask(taskId, updates): Update existing task
- getTimeline(weddingId): Get current timeline and task status
- checkConflicts(weddingId, newEvent): Check for scheduling conflicts

IMPORTANT GUIDELINES:
1. Create realistic timelines based on wedding date and complexity
2. Consider dependencies between tasks (e.g., venue before catering)
3. Include buffer time for unexpected delays
4. Coordinate with other domain managers for integrated planning
5. Store all timeline decisions in memory for future reference

User Request: "${userMessage}"

Analyze the request and determine what timeline management actions are needed.
`;

  try {
    const response = await multiModelFallback.invoke([
      new HumanMessage(systemPrompt)
    ]);

    const responseContent = response.content as string;

    // Store the timeline interaction in memory
    await memoryManager.storeDecision(
      weddingId,
      `Timeline management request: ${userMessage}. Response: ${responseContent}`,
      'timeline_manager',
      {
        action: 'timeline_management',
        userRequest: userMessage
      }
    );

    return {
      messages: [new AIMessage({ content: responseContent })],
      currentAgent: 'orchestrator'
    };

  } catch (error) {
    console.error('Timeline Manager error:', error);
    
    const errorMessage = "I'm sorry, I encountered an issue while processing your timeline request. Please try again or contact support if the problem persists.";
    
    return {
      messages: [new AIMessage({ content: errorMessage })],
      currentAgent: 'orchestrator'
    };
  }
}
