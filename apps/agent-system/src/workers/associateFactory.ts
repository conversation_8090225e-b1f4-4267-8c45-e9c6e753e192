import { z } from 'zod';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { multiModelFallback } from '../models/multiModelFallback';
import { supabaseService } from '../memory/supabaseClient';
import { memoryManager } from '../memory/memoryManager';

// Worker task schemas
export const WorkerTaskSchema = z.object({
  id: z.string().uuid(),
  type: z.string(),
  description: z.string(),
  inputData: z.record(z.any()),
  dependencies: z.array(z.string().uuid()).optional(),
  priority: z.number().min(0).max(10).default(5),
  timeout: z.number().positive().default(300000), // 5 minutes default
});

export const WorkerResultSchema = z.object({
  workerId: z.string().uuid(),
  status: z.enum(['completed', 'failed', 'cancelled']),
  outputData: z.record(z.any()).optional(),
  errorMessage: z.string().optional(),
  executionTime: z.number().positive(),
});

export type WorkerTask = z.infer<typeof WorkerTaskSchema>;
export type WorkerResult = z.infer<typeof WorkerResultSchema>;

// Worker types
export enum WorkerType {
  BUDGET_ANALYZER = 'budget_analyzer',
  VENDOR_RESEARCHER = 'vendor_researcher',
  GUEST_COORDINATOR = 'guest_coordinator',
  TIMELINE_OPTIMIZER = 'timeline_optimizer',
  DOCUMENT_GENERATOR = 'document_generator',
  DATA_AGGREGATOR = 'data_aggregator',
  CONFLICT_RESOLVER = 'conflict_resolver',
  RECOMMENDATION_ENGINE = 'recommendation_engine'
}

// Base Worker Interface
export interface AssociateWorker {
  id: string;
  type: WorkerType;
  status: 'created' | 'running' | 'completed' | 'failed' | 'cancelled';
  execute(task: WorkerTask): Promise<WorkerResult>;
  cleanup(): Promise<void>;
}

// Abstract Base Worker Class
export abstract class BaseAssociateWorker implements AssociateWorker {
  public readonly id: string;
  public readonly type: WorkerType;
  public status: 'created' | 'running' | 'completed' | 'failed' | 'cancelled' = 'created';
  protected startTime: number = 0;
  protected parentRunId: string;

  constructor(type: WorkerType, parentRunId: string) {
    this.id = crypto.randomUUID();
    this.type = type;
    this.parentRunId = parentRunId;
  }

  async execute(task: WorkerTask): Promise<WorkerResult> {
    this.startTime = Date.now();
    this.status = 'running';

    try {
      // Record worker start in database
      await this.recordWorkerStart(task);

      // Execute the specific worker logic
      const result = await this.executeTask(task);

      this.status = 'completed';
      await this.recordWorkerCompletion(result);

      return result;
    } catch (error) {
      this.status = 'failed';
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      const failedResult: WorkerResult = {
        workerId: this.id,
        status: 'failed',
        errorMessage,
        executionTime: Date.now() - this.startTime
      };

      await this.recordWorkerCompletion(failedResult);
      return failedResult;
    }
  }

  protected abstract executeTask(task: WorkerTask): Promise<WorkerResult>;

  async cleanup(): Promise<void> {
    // Override in subclasses if needed
  }

  private async recordWorkerStart(task: WorkerTask): Promise<void> {
    try {
      await supabaseService
        .from('agent_workers')
        .insert({
          id: this.id,
          parent_run_id: this.parentRunId,
          worker_type: this.type,
          task_description: task.description,
          status: 'running',
          input_data: task.inputData
        });
    } catch (error) {
      console.error('Error recording worker start:', error);
    }
  }

  private async recordWorkerCompletion(result: WorkerResult): Promise<void> {
    try {
      await supabaseService
        .from('agent_workers')
        .update({
          status: result.status,
          output_data: result.outputData || {},
          error_message: result.errorMessage,
          completed_at: new Date().toISOString()
        })
        .eq('id', this.id);
    } catch (error) {
      console.error('Error recording worker completion:', error);
    }
  }
}

// Budget Analyzer Worker
export class BudgetAnalyzerWorker extends BaseAssociateWorker {
  constructor(parentRunId: string) {
    super(WorkerType.BUDGET_ANALYZER, parentRunId);
  }

  protected async executeTask(task: WorkerTask): Promise<WorkerResult> {
    const { weddingId, analysisType, parameters } = task.inputData;

    const prompt = `
You are a specialized budget analysis worker. Analyze the wedding budget data and provide detailed insights.

Analysis Type: ${analysisType}
Parameters: ${JSON.stringify(parameters)}

Provide a comprehensive analysis including:
1. Current budget status and trends
2. Potential cost savings opportunities
3. Risk areas and recommendations
4. Projected final costs

Return your analysis in a structured JSON format.
`;

    const response = await multiModelFallback.invoke([
      new HumanMessage(prompt)
    ]);

    // Store analysis in memory for future reference
    await memoryManager.storeMemory({
      content: `Budget Analysis (${analysisType}): ${response.content}`,
      weddingId,
      metadata: {
        type: 'budget_analysis',
        worker_id: this.id,
        analysis_type: analysisType
      }
    });

    return {
      workerId: this.id,
      status: 'completed',
      outputData: {
        analysis: response.content,
        analysisType,
        timestamp: new Date().toISOString()
      },
      executionTime: Date.now() - this.startTime
    };
  }
}

// Vendor Researcher Worker
export class VendorResearcherWorker extends BaseAssociateWorker {
  constructor(parentRunId: string) {
    super(WorkerType.VENDOR_RESEARCHER, parentRunId);
  }

  protected async executeTask(task: WorkerTask): Promise<WorkerResult> {
    const { weddingId, vendorCategory, criteria, location } = task.inputData;

    const prompt = `
You are a specialized vendor research worker. Research and analyze vendors for wedding planning.

Category: ${vendorCategory}
Location: ${location}
Criteria: ${JSON.stringify(criteria)}

Provide detailed vendor research including:
1. Top vendor recommendations with rationale
2. Comparative analysis of options
3. Pricing insights and negotiation tips
4. Availability and booking recommendations

Return your research in a structured JSON format.
`;

    const response = await multiModelFallback.invoke([
      new HumanMessage(prompt)
    ]);

    // Store research in memory
    await memoryManager.storeMemory({
      content: `Vendor Research (${vendorCategory}): ${response.content}`,
      weddingId,
      metadata: {
        type: 'vendor_research',
        worker_id: this.id,
        category: vendorCategory,
        location
      }
    });

    return {
      workerId: this.id,
      status: 'completed',
      outputData: {
        research: response.content,
        vendorCategory,
        location,
        timestamp: new Date().toISOString()
      },
      executionTime: Date.now() - this.startTime
    };
  }
}

// Associate Worker Factory
export class AssociateWorkerFactory {
  private static activeWorkers: Map<string, AssociateWorker> = new Map();

  static async createWorker(type: WorkerType, parentRunId: string): Promise<AssociateWorker> {
    let worker: AssociateWorker;

    switch (type) {
      case WorkerType.BUDGET_ANALYZER:
        worker = new BudgetAnalyzerWorker(parentRunId);
        break;
      case WorkerType.VENDOR_RESEARCHER:
        worker = new VendorResearcherWorker(parentRunId);
        break;
      // Add more worker types as needed
      default:
        throw new Error(`Unsupported worker type: ${type}`);
    }

    this.activeWorkers.set(worker.id, worker);
    return worker;
  }

  static async executeTask(
    type: WorkerType,
    task: WorkerTask,
    parentRunId: string
  ): Promise<WorkerResult> {
    const worker = await this.createWorker(type, parentRunId);
    
    try {
      const result = await worker.execute(task);
      return result;
    } finally {
      await worker.cleanup();
      this.activeWorkers.delete(worker.id);
    }
  }

  static async executeParallelTasks(
    tasks: Array<{ type: WorkerType; task: WorkerTask }>,
    parentRunId: string
  ): Promise<WorkerResult[]> {
    const workers = await Promise.all(
      tasks.map(({ type }) => this.createWorker(type, parentRunId))
    );

    try {
      const results = await Promise.all(
        tasks.map(({ task }, index) => workers[index].execute(task))
      );
      return results;
    } finally {
      // Cleanup all workers
      await Promise.all(workers.map(worker => worker.cleanup()));
      workers.forEach(worker => this.activeWorkers.delete(worker.id));
    }
  }

  static async getActiveWorkers(): Promise<AssociateWorker[]> {
    return Array.from(this.activeWorkers.values());
  }

  static async cancelWorker(workerId: string): Promise<boolean> {
    const worker = this.activeWorkers.get(workerId);
    if (worker) {
      worker.status = 'cancelled';
      await worker.cleanup();
      this.activeWorkers.delete(workerId);
      return true;
    }
    return false;
  }

  static async cleanupAllWorkers(): Promise<void> {
    const workers = Array.from(this.activeWorkers.values());
    await Promise.all(workers.map(worker => worker.cleanup()));
    this.activeWorkers.clear();
  }
}
