import { AIMessage, BaseMessage, HumanMessage } from "@langchain/core/messages"
import { Annotation, END, START, StateGraph } from "@langchain/langgraph"
import { budgetManagerNode } from "./agents/budgetManager"
import { guestManagerNode } from "./agents/guestManager"
import { timelineManagerNode } from "./agents/timelineManager"
import { vendorManagerNode } from "./agents/vendorManager"
import { performanceAnalytics } from "./analytics/performanceAnalytics"
import { crossDomainCoordinator } from "./coordination/crossDomainCoordinator"
import { memoryManager } from "./memory/memoryManager"
import { multiModelFallback } from "./models/multiModelFallback"
import { AssociateWorkerFactory, WorkerType } from "./workers/associateFactory"

// Define the state schema for the orchestrator
export const OrchestratorState = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
  }),
  weddingId: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  userId: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  currentAgent: Annotation<string>({
    reducer: (x, y) => y ?? x,
    default: () => "orchestrator",
  }),
  context: Annotation<Record<string, any>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({}),
  }),
  requiresConfirmation: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false,
  }),
  confirmationData: Annotation<Record<string, any>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({}),
  }),
  runId: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  workflowId: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  coordinationId: Annotation<string>({
    reducer: (x, y) => y ?? x,
  }),
  activeWorkers: Annotation<string[]>({
    reducer: (x, y) => y ?? x,
    default: () => [],
  }),
  performanceMetrics: Annotation<Record<string, any>>({
    reducer: (x, y) => ({ ...x, ...y }),
    default: () => ({}),
  }),
})

export type OrchestratorStateType = typeof OrchestratorState.State

/**
 * CEO Orchestrator Agent - Routes requests to appropriate domain managers
 */
export async function orchestratorNode(
  state: OrchestratorStateType
): Promise<Partial<OrchestratorStateType>> {
  const startTime = Date.now()

  // Check if messages array is empty or doesn't contain a HumanMessage
  if (state.messages.length === 0) {
    return {
      messages: [
        new AIMessage({
          content:
            "I'm Ella, your AI wedding planning assistant. How can I help you today?",
        }),
      ],
    }
  }

  const lastMessage = state.messages[state.messages.length - 1]

  if (!lastMessage || !(lastMessage instanceof HumanMessage)) {
    return {
      messages: [
        new AIMessage({
          content:
            "I'm Ella, your AI wedding planning assistant. How can I help you today?",
        }),
      ],
    }
  }

  const userMessage = lastMessage.content as string

  // Get context from memory
  const context = await memoryManager.getAgentContext(
    state.weddingId,
    userMessage,
    { limit: 3 }
  )

  // Classify intent and route to appropriate manager
  const intent = await classifyIntent(userMessage, context)

  // Check if this requires cross-domain coordination
  const requiresCoordination = await checkCrossDomainRequirement(
    userMessage,
    intent
  )

  // Store the user's request in memory
  await memoryManager.storeMemory({
    content: `User Request: ${userMessage}`,
    weddingId: state.weddingId,
    metadata: {
      type: "user_request",
      intent: intent.domain,
      confidence: intent.confidence,
      requires_coordination: requiresCoordination,
    },
  })

  // Record performance metrics
  const responseTime = Date.now() - startTime
  await performanceAnalytics.recordMetric({
    agentType: "orchestrator",
    metricName: "response_time",
    metricValue: responseTime,
    metricUnit: "milliseconds",
    timePeriod: "hour",
    periodStart: new Date(Date.now() - 60 * 60 * 1000),
    periodEnd: new Date(),
    metadata: {
      intent: intent.domain,
      confidence: intent.confidence,
    },
  })

  return {
    currentAgent: intent.domain,
    context: {
      ...state.context,
      intent: intent.domain,
      confidence: intent.confidence,
      memoryContext: context,
      requiresCoordination,
      startTime,
    },
  }
}

/**
 * Intent classification to determine which domain manager to route to
 */
async function classifyIntent(
  userMessage: string,
  context: string
): Promise<{
  domain: string
  confidence: number
  reasoning: string
}> {
  const classificationPrompt = `
You are an AI wedding planning assistant that routes user requests to the appropriate domain specialist.

Available domains:
- budget: Budget management, cost tracking, expense allocation, savings suggestions
- vendor: Vendor research, booking, communication, availability checking
- guest: Guest list management, RSVP tracking, invitation sending
- timeline: Timeline creation, task management, milestone tracking, scheduling
- general: General questions, greetings, or requests that don't fit other categories

Context from previous conversations:
${context}

User message: "${userMessage}"

Analyze the user's request and determine which domain specialist should handle it. Consider:
1. The primary intent of the message
2. Any specific actions mentioned
3. The context from previous conversations

Respond with a JSON object containing:
- domain: The domain name (budget, vendor, guest, timeline, or general)
- confidence: A number between 0 and 1 indicating confidence in the classification
- reasoning: A brief explanation of why this domain was chosen

Example response:
{
  "domain": "budget",
  "confidence": 0.9,
  "reasoning": "User is asking about wedding costs and budget allocation"
}
`

  try {
    const response = await multiModelFallback.invoke([
      new HumanMessage(classificationPrompt),
    ])

    const content = response.content as string
    const jsonMatch = content.match(/\{[\s\S]*\}/)

    if (jsonMatch) {
      const parsed = JSON.parse(jsonMatch[0])
      return {
        domain: parsed.domain || "general",
        confidence: parsed.confidence || 0.5,
        reasoning: parsed.reasoning || "Default classification",
      }
    }
  } catch (error) {
    console.error("Error classifying intent:", error)
  }

  // Fallback classification based on keywords
  const lowerMessage = userMessage.toLowerCase()

  if (
    lowerMessage.includes("budget") ||
    lowerMessage.includes("cost") ||
    lowerMessage.includes("money") ||
    lowerMessage.includes("price")
  ) {
    return {
      domain: "budget",
      confidence: 0.7,
      reasoning: "Keyword-based classification: budget-related terms detected",
    }
  }

  if (
    lowerMessage.includes("vendor") ||
    lowerMessage.includes("book") ||
    lowerMessage.includes("florist") ||
    lowerMessage.includes("photographer")
  ) {
    return {
      domain: "vendor",
      confidence: 0.7,
      reasoning: "Keyword-based classification: vendor-related terms detected",
    }
  }

  if (
    lowerMessage.includes("guest") ||
    lowerMessage.includes("rsvp") ||
    lowerMessage.includes("invitation")
  ) {
    return {
      domain: "guest",
      confidence: 0.7,
      reasoning: "Keyword-based classification: guest-related terms detected",
    }
  }

  if (
    lowerMessage.includes("timeline") ||
    lowerMessage.includes("task") ||
    lowerMessage.includes("schedule") ||
    lowerMessage.includes("plan")
  ) {
    return {
      domain: "timeline",
      confidence: 0.7,
      reasoning:
        "Keyword-based classification: timeline-related terms detected",
    }
  }

  return {
    domain: "general",
    confidence: 0.5,
    reasoning: "No specific domain detected, routing to general handler",
  }
}

/**
 * Check if a request requires cross-domain coordination
 */
async function checkCrossDomainRequirement(
  userMessage: string,
  intent: any
): Promise<boolean> {
  const crossDomainKeywords = [
    "complete wedding plan",
    "full planning",
    "everything",
    "all aspects",
    "comprehensive",
    "budget and vendor",
    "timeline and guest",
    "vendor and budget",
    "guest and timeline",
  ]

  const lowerMessage = userMessage.toLowerCase()
  return crossDomainKeywords.some((keyword) => lowerMessage.includes(keyword))
}

/**
 * Enhanced workflow execution node for complex multi-domain tasks
 */
export async function workflowExecutionNode(
  state: OrchestratorStateType
): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1]
  const userMessage = lastMessage.content as string

  try {
    // Define a comprehensive wedding planning workflow
    const workflowDefinition = {
      workflowName: "comprehensive_wedding_planning",
      description: "Complete wedding planning workflow across all domains",
      steps: [
        {
          stepName: "budget_analysis",
          stepOrder: 1,
          agentType: "budget",
          inputData: {
            analysisType: "comprehensive",
            userRequest: userMessage,
          },
        },
        {
          stepName: "vendor_research",
          stepOrder: 2,
          agentType: "vendor",
          inputData: {
            researchType: "all_categories",
            userRequest: userMessage,
          },
        },
        {
          stepName: "timeline_generation",
          stepOrder: 3,
          agentType: "timeline",
          inputData: {
            timelineType: "master_timeline",
            userRequest: userMessage,
          },
        },
        {
          stepName: "guest_coordination",
          stepOrder: 4,
          agentType: "guest",
          inputData: {
            coordinationType: "full_management",
            userRequest: userMessage,
          },
        },
      ],
    }

    // Execute the workflow
    const workflowId = await crossDomainCoordinator.executeWorkflow(
      workflowDefinition,
      state.weddingId,
      state.userId,
      { originalRequest: userMessage }
    )

    return {
      messages: [
        new AIMessage({
          content:
            "I'm executing a comprehensive wedding planning workflow that will coordinate across all domains. This will take a few moments as I analyze your budget, research vendors, create a timeline, and coordinate guest management.",
        }),
      ],
      workflowId,
      currentAgent: "orchestrator",
    }
  } catch (error) {
    console.error("Error executing workflow:", error)
    return {
      messages: [
        new AIMessage({
          content:
            "I encountered an issue while setting up the comprehensive planning workflow. Let me handle your request through individual domain specialists instead.",
        }),
      ],
      currentAgent: "orchestrator",
    }
  }
}

/**
 * Associate worker delegation node
 */
export async function workerDelegationNode(
  state: OrchestratorStateType
): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1]
  const userMessage = lastMessage.content as string

  try {
    // Determine which workers are needed
    const workerTasks = await determineWorkerTasks(userMessage, state.context)

    if (workerTasks.length === 0) {
      return {
        messages: [
          new AIMessage({
            content:
              "I don't need to delegate any specialized tasks for this request. Let me handle it directly.",
          }),
        ],
        currentAgent: state.context.intent || "general",
      }
    }

    // Execute worker tasks
    const workerResults = await Promise.all(
      workerTasks.map(({ type, task }) =>
        AssociateWorkerFactory.executeTask(type, task, state.runId || "")
      )
    )

    // Aggregate results
    const successfulResults = workerResults.filter(
      (r: any) => r.status === "completed"
    )
    const failedResults = workerResults.filter(
      (r: any) => r.status === "failed"
    )

    let responseContent = "I've completed the specialized analysis:\n\n"

    successfulResults.forEach((result: any) => {
      if (result.outputData) {
        responseContent += `✅ ${
          result.outputData.analysisType || "Analysis"
        }: Completed successfully\n`
      }
    })

    if (failedResults.length > 0) {
      responseContent += `\n⚠️ ${failedResults.length} tasks encountered issues but I was able to provide alternative solutions.`
    }

    return {
      messages: [new AIMessage({ content: responseContent })],
      activeWorkers: workerResults.map((r: any) => r.workerId),
      currentAgent: "orchestrator",
    }
  } catch (error) {
    console.error("Error in worker delegation:", error)
    return {
      messages: [
        new AIMessage({
          content:
            "I encountered an issue with task delegation. Let me handle your request directly instead.",
        }),
      ],
      currentAgent: state.context.intent || "general",
    }
  }
}

/**
 * Determine which worker tasks are needed based on the request
 */
async function determineWorkerTasks(
  userMessage: string,
  context: any
): Promise<
  Array<{
    type: WorkerType
    task: any
  }>
> {
  const tasks = []
  const lowerMessage = userMessage.toLowerCase()

  // Budget analysis worker
  if (
    lowerMessage.includes("budget analysis") ||
    lowerMessage.includes("cost breakdown")
  ) {
    tasks.push({
      type: WorkerType.BUDGET_ANALYZER,
      task: {
        id: crypto.randomUUID(),
        type: "budget_analysis",
        description: "Comprehensive budget analysis",
        inputData: {
          weddingId: context.weddingId,
          analysisType: "comprehensive",
          parameters: {
            includeProjections: true,
            includeRecommendations: true,
          },
        },
      },
    })
  }

  // Vendor research worker
  if (
    lowerMessage.includes("vendor research") ||
    lowerMessage.includes("find vendors")
  ) {
    tasks.push({
      type: WorkerType.VENDOR_RESEARCHER,
      task: {
        id: crypto.randomUUID(),
        type: "vendor_research",
        description: "Comprehensive vendor research",
        inputData: {
          weddingId: context.weddingId,
          vendorCategory: "all",
          criteria: { budget: "flexible", quality: "high" },
          location: "Austin, TX", // Would come from wedding details
        },
      },
    })
  }

  return tasks
}

/**
 * Conditional routing function
 */
export function routeToAgent(state: OrchestratorStateType): string {
  const agent = state.currentAgent

  // If we need confirmation, pause for human input
  if (state.requiresConfirmation) {
    return "confirmation"
  }

  // Check if this requires cross-domain coordination
  if (state.context?.requiresCoordination) {
    return "workflowExecution"
  }

  // Check if this requires worker delegation
  if (state.context?.requiresWorkers) {
    return "workerDelegation"
  }

  switch (agent) {
    case "budget":
      return "budgetManager"
    case "vendor":
      return "vendorManager"
    case "guest":
      return "guestManager"
    case "timeline":
      return "timelineManager"
    case "general":
      return "generalHandler"
    default:
      return "generalHandler"
  }
}

/**
 * General handler for non-domain-specific requests
 */
export async function generalHandlerNode(
  state: OrchestratorStateType
): Promise<Partial<OrchestratorStateType>> {
  const lastMessage = state.messages[state.messages.length - 1]
  const userMessage = lastMessage.content as string

  const response = await multiModelFallback.invoke([
    new HumanMessage(`
You are Ella, a friendly AI wedding planning assistant. The user has asked: "${userMessage}"

Provide a helpful, warm response. If this seems like it should be handled by a specific domain (budget, vendor, guest management, or timeline), gently guide them to be more specific about what they need help with.

Keep your response conversational and supportive.
`),
  ])

  // Store the interaction in memory
  await memoryManager.storeMemory({
    content: `General Interaction - User: ${userMessage}, Assistant: ${response.content}`,
    weddingId: state.weddingId,
    metadata: {
      type: "general_interaction",
      agent: "general",
    },
  })

  return {
    messages: [response],
    currentAgent: "orchestrator",
  }
}

/**
 * Confirmation handler for user approvals
 */
export async function confirmationNode(
  state: OrchestratorStateType
): Promise<Partial<OrchestratorStateType>> {
  const confirmationMessage = new AIMessage({
    content:
      "I need your confirmation before proceeding. Please review the details and let me know if you'd like to continue.",
    additional_kwargs: {
      confirmation_required: true,
      confirmation_data: state.confirmationData,
    },
  })

  return {
    messages: [confirmationMessage],
    requiresConfirmation: false,
  }
}

/**
 * Create the main orchestrator graph
 */
export function createWeddingPlannerGraph() {
  const graph = new StateGraph(OrchestratorState)
    .addNode("orchestrator", orchestratorNode)
    .addNode("budgetManager", budgetManagerNode)
    .addNode("vendorManager", vendorManagerNode)
    .addNode("guestManager", guestManagerNode)
    .addNode("timelineManager", timelineManagerNode)
    .addNode("generalHandler", generalHandlerNode)
    .addNode("confirmation", confirmationNode)
    .addNode("workflowExecution", workflowExecutionNode)
    .addNode("workerDelegation", workerDelegationNode)
    .addEdge(START, "orchestrator")
    .addConditionalEdges("orchestrator", routeToAgent, {
      budgetManager: "budgetManager",
      vendorManager: "vendorManager",
      guestManager: "guestManager",
      timelineManager: "timelineManager",
      generalHandler: "generalHandler",
      confirmation: "confirmation",
      workflowExecution: "workflowExecution",
      workerDelegation: "workerDelegation",
    })
    .addEdge("budgetManager", END)
    .addEdge("vendorManager", END)
    .addEdge("guestManager", END)
    .addEdge("timelineManager", END)
    .addEdge("generalHandler", END)
    .addEdge("confirmation", END)
    .addEdge("workflowExecution", END)
    .addEdge("workerDelegation", END)

  return graph
}
