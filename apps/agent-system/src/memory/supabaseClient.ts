import { createClient } from '@supabase/supabase-js';

// Support multiple environment variable formats for maximum compatibility
const supabaseUrl =
  process.env.NEXT_PUBLIC_SUPABASE_URL ||
  process.env.SUPABASE_URL ||
  'https://gxlucamlneoombifnirp.supabase.co';

const supabaseServiceKey = 
  process.env.SUPABASE_SERVICE_ROLE_KEY || 
  '';

const supabaseAnonKey = 
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 
  process.env.SUPABASE_ANON_KEY || 
  '';

if (!supabaseUrl) {
  throw new Error('Supabase URL is missing. Please check your environment variables.');
}

if (!supabaseServiceKey && !supabaseAnonKey) {
  throw new Error('Supabase keys are missing. Please check your environment variables.');
}

// Service role client for agent operations (bypasses RLS)
export const supabaseService = createClient(supabaseUrl, supabaseService<PERSON><PERSON>, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Regular client for user-scoped operations
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types for agent system
export interface AgentRun {
  id: string;
  wedding_id: string;
  user_id: string;
  agent_type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  input_data: Record<string, any>;
  output_data?: Record<string, any>;
  error_message?: string;
  started_at: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface AgentEvent {
  id: number;
  run_id: string;
  event_type: string;
  payload: Record<string, any>;
  created_at: string;
}

export interface AgentMemory {
  id: number;
  wedding_id: string;
  content: string;
  embedding?: number[];
  metadata: Record<string, any>;
  created_at: string;
}

export interface MemorySearchResult {
  id: number;
  content: string;
  similarity: number;
  metadata: Record<string, any>;
  created_at: string;
}

export interface AgentFeedback {
  id: string;
  run_id: string;
  user_id: string;
  wedding_id: string;
  feedback_type: 'rating' | 'thumbs' | 'text' | 'correction';
  rating?: number;
  thumbs_direction?: 'up' | 'down';
  feedback_text?: string;
  correction_data?: Record<string, any>;
  agent_response_id?: string;
  context_data: Record<string, any>;
  created_at: string;
}

export interface AgentWorker {
  id: string;
  parent_run_id: string;
  worker_type: string;
  task_description: string;
  status: 'created' | 'running' | 'completed' | 'failed' | 'cancelled';
  input_data: Record<string, any>;
  output_data?: Record<string, any>;
  error_message?: string;
  started_at: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}
