import { supabaseService } from './supabaseClient';
import { AgentMemory, MemorySearchResult } from './supabaseClient';
import { OpenAIEmbeddings } from '@langchain/openai';

export interface MemoryEntry {
  content: string;
  metadata?: Record<string, any>;
  weddingId: string;
}

export interface SearchOptions {
  threshold?: number;
  limit?: number;
  metadata?: Record<string, any>;
}

export class MemoryManager {
  private embeddings: OpenAIEmbeddings;

  constructor() {
    this.embeddings = new OpenAIEmbeddings({
      openAIApiKey: process.env.OPENAI_API_KEY,
      modelName: 'text-embedding-3-small'
    });
  }

  /**
   * Store a memory entry with vector embedding
   */
  async storeMemory(entry: MemoryEntry): Promise<void> {
    try {
      // Generate embedding for the content
      const embedding = await this.embeddings.embedQuery(entry.content);

      // Store in Supabase
      const { error } = await supabaseService
        .from('agent_memory')
        .insert({
          wedding_id: entry.weddingId,
          content: entry.content,
          embedding: embedding,
          metadata: entry.metadata || {}
        });

      if (error) {
        throw new Error(`Failed to store memory: ${error.message}`);
      }

      console.log('Memory stored successfully');
    } catch (error) {
      console.error('Error storing memory:', error);
      throw error;
    }
  }

  /**
   * Search memories using vector similarity
   */
  async searchMemories(
    query: string, 
    weddingId: string, 
    options: SearchOptions = {}
  ): Promise<MemorySearchResult[]> {
    try {
      const { threshold = 0.7, limit = 10 } = options;

      // Generate embedding for the query
      const queryEmbedding = await this.embeddings.embedQuery(query);

      // Search using the database function
      const { data, error } = await supabaseService
        .rpc('search_agent_memory', {
          query_embedding: queryEmbedding,
          target_wedding_id: weddingId,
          match_threshold: threshold,
          match_count: limit
        });

      if (error) {
        throw new Error(`Failed to search memories: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error searching memories:', error);
      throw error;
    }
  }

  /**
   * Get recent memories for a wedding
   */
  async getRecentMemories(weddingId: string, limit: number = 10): Promise<AgentMemory[]> {
    try {
      const { data, error } = await supabaseService
        .from('agent_memory')
        .select('*')
        .eq('wedding_id', weddingId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        throw new Error(`Failed to get recent memories: ${error.message}`);
      }

      return data || [];
    } catch (error) {
      console.error('Error getting recent memories:', error);
      throw error;
    }
  }

  /**
   * Store a conversation summary
   */
  async storeConversationSummary(
    weddingId: string, 
    summary: string, 
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.storeMemory({
      content: `Conversation Summary: ${summary}`,
      weddingId,
      metadata: {
        type: 'conversation_summary',
        timestamp: new Date().toISOString(),
        ...metadata
      }
    });
  }

  /**
   * Store a decision or action taken by an agent
   */
  async storeDecision(
    weddingId: string,
    decision: string,
    agent: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.storeMemory({
      content: `Agent Decision (${agent}): ${decision}`,
      weddingId,
      metadata: {
        type: 'agent_decision',
        agent,
        timestamp: new Date().toISOString(),
        ...metadata
      }
    });
  }

  /**
   * Store important facts or insights
   */
  async storeFact(
    weddingId: string,
    fact: string,
    source?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.storeMemory({
      content: `Fact: ${fact}`,
      weddingId,
      metadata: {
        type: 'fact',
        source,
        timestamp: new Date().toISOString(),
        ...metadata
      }
    });
  }

  /**
   * Get context for an agent based on recent activity and relevant memories
   */
  async getAgentContext(
    weddingId: string,
    query: string,
    options: SearchOptions = {}
  ): Promise<string> {
    try {
      // Get recent memories
      const recentMemories = await this.getRecentMemories(weddingId, 5);
      
      // Search for relevant memories
      const relevantMemories = await this.searchMemories(query, weddingId, {
        threshold: 0.6,
        limit: 5,
        ...options
      });

      // Combine and format context
      const context = [];
      
      if (recentMemories.length > 0) {
        context.push('Recent Activity:');
        recentMemories.forEach(memory => {
          context.push(`- ${memory.content}`);
        });
      }

      if (relevantMemories.length > 0) {
        context.push('\nRelevant Context:');
        relevantMemories.forEach(memory => {
          context.push(`- ${memory.content} (similarity: ${memory.similarity.toFixed(2)})`);
        });
      }

      return context.join('\n');
    } catch (error) {
      console.error('Error getting agent context:', error);
      return '';
    }
  }

  /**
   * Clear old memories (cleanup function)
   */
  async clearOldMemories(weddingId: string, daysOld: number = 30): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const { error } = await supabaseService
        .from('agent_memory')
        .delete()
        .eq('wedding_id', weddingId)
        .lt('created_at', cutoffDate.toISOString());

      if (error) {
        throw new Error(`Failed to clear old memories: ${error.message}`);
      }

      console.log(`Cleared memories older than ${daysOld} days for wedding ${weddingId}`);
    } catch (error) {
      console.error('Error clearing old memories:', error);
      throw error;
    }
  }
}

// Singleton instance
export const memoryManager = new MemoryManager();
