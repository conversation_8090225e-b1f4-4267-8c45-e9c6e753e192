import { describe, it, expect, beforeEach, mock } from 'bun:test';
import { getBudgetStatus, updateBudgetItem, addBudgetCategory, generateBudgetReport } from '../../tools/budgetTools';

// Mock Supabase client
const mockSupabase = {
  from: mock(() => ({
    select: mock(() => ({
      eq: mock(() => ({
        single: mock(() => ({ data: { initial_budget: 50000 }, error: null })),
        order: mock(() => ({ data: [], error: null }))
      }))
    })),
    update: mock(() => ({
      eq: mock(() => ({
        select: mock(() => ({
          single: mock(() => ({ data: { id: 'test-id', category: 'Catering' }, error: null }))
        }))
      }))
    })),
    insert: mock(() => ({
      select: mock(() => ({
        single: mock(() => ({ data: { id: 'new-id' }, error: null }))
      }))
    }))
  }))
};

mock.module('../../memory/supabaseClient', () => ({
  supabase: mockSupabase
}));

describe('Budget Tools', () => {
  const testWeddingId = '550e8400-e29b-41d4-a716-446655440000';
  const testItemId = '550e8400-e29b-41d4-a716-446655440001';

  beforeEach(() => {
    // Reset mocks
    mock.restore();
  });

  describe('getBudgetStatus', () => {
    it('should return budget status with correct calculations', async () => {
      // Mock wedding data
      mockSupabase.from = mock(() => ({
        select: mock(() => ({
          eq: mock(() => ({
            single: mock(() => ({ data: { initial_budget: 50000 }, error: null }))
          }))
        }))
      }));

      // Mock budget items data
      const mockBudgetItems = [
        {
          id: '1',
          category: 'Catering',
          estimated_cost: 15000,
          actual_cost: 16000,
          vendor: 'Test Caterer',
          payment_due_date: '2024-06-01',
          is_paid: false
        },
        {
          id: '2',
          category: 'Photography',
          estimated_cost: 3000,
          actual_cost: 2800,
          vendor: 'Test Photographer',
          payment_due_date: null,
          is_paid: true
        }
      ];

      // Setup mock chain for budget items
      mockSupabase.from = mock((table) => {
        if (table === 'weddings') {
          return {
            select: mock(() => ({
              eq: mock(() => ({
                single: mock(() => ({ data: { initial_budget: 50000 }, error: null }))
              }))
            }))
          };
        } else if (table === 'budget_items') {
          return {
            select: mock(() => ({
              eq: mock(() => ({
                order: mock(() => ({ data: mockBudgetItems, error: null }))
              }))
            }))
          };
        }
      });

      const result = await getBudgetStatus(testWeddingId);

      expect(result.totalBudget).toBe(50000);
      expect(result.totalSpent).toBe(18800);
      expect(result.totalEstimated).toBe(18000);
      expect(result.remaining).toBe(31200);
      expect(result.categories).toHaveLength(2);
      expect(result.overBudgetCategories).toHaveLength(1);
      expect(result.overBudgetCategories[0].category).toBe('Catering');
    });

    it('should handle missing wedding data gracefully', async () => {
      mockSupabase.from = mock(() => ({
        select: mock(() => ({
          eq: mock(() => ({
            single: mock(() => ({ data: null, error: { message: 'Wedding not found' } }))
          }))
        }))
      }));

      await expect(getBudgetStatus(testWeddingId)).rejects.toThrow('Failed to get wedding data');
    });

    it('should handle empty budget items', async () => {
      mockSupabase.from = mock((table) => {
        if (table === 'weddings') {
          return {
            select: mock(() => ({
              eq: mock(() => ({
                single: mock(() => ({ data: { initial_budget: 50000 }, error: null }))
              }))
            }))
          };
        } else if (table === 'budget_items') {
          return {
            select: mock(() => ({
              eq: mock(() => ({
                order: mock(() => ({ data: [], error: null }))
              }))
            }))
          };
        }
      });

      const result = await getBudgetStatus(testWeddingId);

      expect(result.totalBudget).toBe(50000);
      expect(result.totalSpent).toBe(0);
      expect(result.totalEstimated).toBe(0);
      expect(result.remaining).toBe(50000);
      expect(result.categories).toHaveLength(0);
    });
  });

  describe('updateBudgetItem', () => {
    it('should successfully update budget item', async () => {
      const result = await updateBudgetItem(testItemId, 5000, 'Updated catering cost');

      expect(result.success).toBe(true);
      expect(result.message).toContain('Successfully updated');
      expect(result.message).toContain('$5,000');
    });

    it('should handle update errors', async () => {
      mockSupabase.from = mock(() => ({
        update: mock(() => ({
          eq: mock(() => ({
            select: mock(() => ({
              single: mock(() => ({ data: null, error: { message: 'Item not found' } }))
            }))
          }))
        }))
      }));

      const result = await updateBudgetItem(testItemId, 5000);

      expect(result.success).toBe(false);
      expect(result.message).toContain('Failed to update budget item');
    });

    it('should validate input parameters', async () => {
      await expect(updateBudgetItem('', -100)).rejects.toThrow();
      await expect(updateBudgetItem('invalid-id', 0)).rejects.toThrow();
    });
  });

  describe('addBudgetCategory', () => {
    it('should successfully add new budget category', async () => {
      // Mock check for existing category (returns null)
      mockSupabase.from = mock((table) => {
        if (table === 'budget_items') {
          const selectMock = mock(() => ({
            eq: mock(() => ({
              single: mock(() => ({ data: null, error: null }))
            }))
          }));
          
          const insertMock = mock(() => ({
            select: mock(() => ({
              single: mock(() => ({ data: { id: 'new-id' }, error: null }))
            }))
          }));

          return {
            select: selectMock,
            insert: insertMock
          };
        }
      });

      const result = await addBudgetCategory(
        testWeddingId,
        'Flowers',
        2500,
        'Test Florist',
        'Beautiful arrangements'
      );

      expect(result.success).toBe(true);
      expect(result.message).toContain('Successfully added "Flowers" category');
      expect(result.itemId).toBe('new-id');
    });

    it('should prevent duplicate categories', async () => {
      // Mock existing category
      mockSupabase.from = mock(() => ({
        select: mock(() => ({
          eq: mock(() => ({
            single: mock(() => ({ data: { id: 'existing-id' }, error: null }))
          }))
        }))
      }));

      const result = await addBudgetCategory(testWeddingId, 'Catering', 15000);

      expect(result.success).toBe(false);
      expect(result.message).toContain('already exists');
    });

    it('should validate input parameters', async () => {
      await expect(addBudgetCategory('', 'Flowers', 2500)).rejects.toThrow();
      await expect(addBudgetCategory(testWeddingId, '', 2500)).rejects.toThrow();
      await expect(addBudgetCategory(testWeddingId, 'Flowers', -100)).rejects.toThrow();
    });
  });

  describe('generateBudgetReport', () => {
    it('should generate comprehensive budget report', async () => {
      // Mock getBudgetStatus
      const mockBudgetStatus = {
        totalBudget: 50000,
        totalSpent: 45000,
        totalEstimated: 48000,
        remaining: 5000,
        categories: [],
        overBudgetCategories: [
          { category: 'Catering', estimatedCost: 15000, actualCost: 16000 }
        ],
        upcomingPayments: [
          { category: 'Photography', paymentDueDate: '2024-06-01' }
        ]
      };

      // Mock the getBudgetStatus function
      const getBudgetStatusMock = mock(() => Promise.resolve(mockBudgetStatus));
      
      const result = await generateBudgetReport(testWeddingId, true);

      expect(result.summary).toContain('Budget Report Summary');
      expect(result.summary).toContain('$50,000');
      expect(result.summary).toContain('$45,000');
      expect(result.summary).toContain('90.0%');
      expect(result.warnings).toContain('1 categories are over budget');
      expect(result.warnings).toContain('1 payments due within 30 days');
      expect(result.recommendations).toContain('Review over-budget categories');
      expect(result.projections).toContain('Budget Projections');
    });

    it('should generate report without projections', async () => {
      const mockBudgetStatus = {
        totalBudget: 50000,
        totalSpent: 25000,
        totalEstimated: 48000,
        remaining: 25000,
        categories: [],
        overBudgetCategories: [],
        upcomingPayments: []
      };

      const result = await generateBudgetReport(testWeddingId, false);

      expect(result.summary).toContain('Budget Report Summary');
      expect(result.projections).toBeUndefined();
      expect(result.recommendations).toContain('good budget flexibility');
    });

    it('should handle budget analysis edge cases', async () => {
      const mockBudgetStatus = {
        totalBudget: 50000,
        totalSpent: 52000, // Over budget
        totalEstimated: 48000,
        remaining: -2000,
        categories: [],
        overBudgetCategories: [],
        upcomingPayments: []
      };

      const result = await generateBudgetReport(testWeddingId, true);

      expect(result.summary).toContain('Over Budget by: $2,000');
      expect(result.warnings).toContain('spent over 90% of your budget');
    });
  });
});
