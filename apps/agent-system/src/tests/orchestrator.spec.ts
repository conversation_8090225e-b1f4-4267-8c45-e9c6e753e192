import { describe, it, expect, beforeEach, mock } from 'bun:test';
import { HumanMessage, AIMessage } from '@langchain/core/messages';
import { orchestratorNode, routeToAgent, OrchestratorState } from '../orchestratorGraph';

// Mock dependencies
mock.module('../models/multiModelFallback', () => ({
  multiModelFallback: {
    invoke: mock(async () => new AIMessage({ content: '{"domain": "budget", "confidence": 0.9, "reasoning": "Budget-related request"}' }))
  }
}));

mock.module('../memory/memoryManager', () => ({
  memoryManager: {
    getAgentContext: mock(async () => 'Previous context about budget planning'),
    storeMemory: mock(async () => {})
  }
}));

describe('Orchestrator Agent', () => {
  let mockState: typeof OrchestratorState.State;

  beforeEach(() => {
    mockState = {
      messages: [new HumanMessage({ content: 'What is my current budget status?' })],
      weddingId: 'test-wedding-id',
      userId: 'test-user-id',
      currentAgent: 'orchestrator',
      context: {},
      requiresConfirmation: false,
      confirmationData: {}
    };
  });

  describe('orchestratorNode', () => {
    it('should handle initial greeting when no human message is present', async () => {
      const stateWithoutHumanMessage = {
        ...mockState,
        messages: [new AIMessage({ content: 'Previous AI message' })]
      };

      const result = await orchestratorNode(stateWithoutHumanMessage);

      expect(result.messages).toBeDefined();
      expect(result.messages![0]).toBeInstanceOf(AIMessage);
      expect(result.messages![0].content).toContain('Ella');
    });

    it('should classify budget-related requests correctly', async () => {
      const result = await orchestratorNode(mockState);

      expect(result.currentAgent).toBe('budget');
      expect(result.context?.intent).toBe('budget');
    });

    it('should store user requests in memory', async () => {
      await orchestratorNode(mockState);

      const memoryManager = await import('../memory/memoryManager');
      expect(memoryManager.memoryManager.storeMemory).toHaveBeenCalledWith(
        expect.objectContaining({
          content: expect.stringContaining('User Request: What is my current budget status?'),
          weddingId: 'test-wedding-id'
        })
      );
    });

    it('should handle vendor-related requests', async () => {
      const vendorState = {
        ...mockState,
        messages: [new HumanMessage({ content: 'Find me a photographer under $2000' })]
      };

      // Mock vendor classification response
      const multiModelFallback = await import('../models/multiModelFallback');
      multiModelFallback.multiModelFallback.invoke = mock(async () => 
        new AIMessage({ content: '{"domain": "vendor", "confidence": 0.8, "reasoning": "Vendor search request"}' })
      );

      const result = await orchestratorNode(vendorState);

      expect(result.currentAgent).toBe('vendor');
      expect(result.context?.intent).toBe('vendor');
    });

    it('should handle guest-related requests', async () => {
      const guestState = {
        ...mockState,
        messages: [new HumanMessage({ content: 'Add John Doe to the guest list' })]
      };

      // Mock guest classification response
      const multiModelFallback = await import('../models/multiModelFallback');
      multiModelFallback.multiModelFallback.invoke = mock(async () => 
        new AIMessage({ content: '{"domain": "guest", "confidence": 0.9, "reasoning": "Guest management request"}' })
      );

      const result = await orchestratorNode(guestState);

      expect(result.currentAgent).toBe('guest');
      expect(result.context?.intent).toBe('guest');
    });

    it('should fallback to keyword-based classification when LLM fails', async () => {
      // Mock LLM failure
      const multiModelFallback = await import('../models/multiModelFallback');
      multiModelFallback.multiModelFallback.invoke = mock(async () => {
        throw new Error('LLM API error');
      });

      const budgetState = {
        ...mockState,
        messages: [new HumanMessage({ content: 'How much money have I spent on the wedding?' })]
      };

      const result = await orchestratorNode(budgetState);

      expect(result.currentAgent).toBe('budget');
      expect(result.context?.confidence).toBe(0.7);
    });
  });

  describe('routeToAgent', () => {
    it('should route to budget manager for budget requests', () => {
      const state = { ...mockState, currentAgent: 'budget' };
      const route = routeToAgent(state);
      expect(route).toBe('budgetManager');
    });

    it('should route to vendor manager for vendor requests', () => {
      const state = { ...mockState, currentAgent: 'vendor' };
      const route = routeToAgent(state);
      expect(route).toBe('vendorManager');
    });

    it('should route to guest manager for guest requests', () => {
      const state = { ...mockState, currentAgent: 'guest' };
      const route = routeToAgent(state);
      expect(route).toBe('guestManager');
    });

    it('should route to timeline manager for timeline requests', () => {
      const state = { ...mockState, currentAgent: 'timeline' };
      const route = routeToAgent(state);
      expect(route).toBe('timelineManager');
    });

    it('should route to confirmation when confirmation is required', () => {
      const state = { ...mockState, requiresConfirmation: true };
      const route = routeToAgent(state);
      expect(route).toBe('confirmation');
    });

    it('should route to general handler for unknown agents', () => {
      const state = { ...mockState, currentAgent: 'unknown' };
      const route = routeToAgent(state);
      expect(route).toBe('generalHandler');
    });

    it('should route to general handler for general requests', () => {
      const state = { ...mockState, currentAgent: 'general' };
      const route = routeToAgent(state);
      expect(route).toBe('generalHandler');
    });
  });

  describe('Intent Classification', () => {
    it('should classify timeline-related keywords correctly', async () => {
      const timelineState = {
        ...mockState,
        messages: [new HumanMessage({ content: 'Create a timeline for my wedding planning tasks' })]
      };

      // Mock timeline classification response
      const multiModelFallback = await import('../models/multiModelFallback');
      multiModelFallback.multiModelFallback.invoke = mock(async () => 
        new AIMessage({ content: '{"domain": "timeline", "confidence": 0.8, "reasoning": "Timeline planning request"}' })
      );

      const result = await orchestratorNode(timelineState);

      expect(result.currentAgent).toBe('timeline');
    });

    it('should handle general greetings', async () => {
      const greetingState = {
        ...mockState,
        messages: [new HumanMessage({ content: 'Hello, how are you?' })]
      };

      // Mock general classification response
      const multiModelFallback = await import('../models/multiModelFallback');
      multiModelFallback.multiModelFallback.invoke = mock(async () => 
        new AIMessage({ content: '{"domain": "general", "confidence": 0.6, "reasoning": "General greeting"}' })
      );

      const result = await orchestratorNode(greetingState);

      expect(result.currentAgent).toBe('general');
    });
  });
});
