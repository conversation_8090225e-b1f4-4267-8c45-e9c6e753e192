import { describe, it, expect } from 'bun:test';
import { createWeddingPlannerGraph } from '../orchestratorGraph';

describe('Integration Tests', () => {
  describe('Wedding Planner Graph', () => {
    it('should create graph without errors', () => {
      expect(() => {
        const graph = createWeddingPlannerGraph();
        expect(graph).toBeDefined();
      }).not.toThrow();
    });

    it('should have all required nodes', () => {
      const graph = createWeddingPlannerGraph();
      const compiledGraph = graph.compile();
      
      // The graph should compile successfully
      expect(compiledGraph).toBeDefined();
    });
  });

  describe('Multi-Model Fallback', () => {
    it('should initialize without errors', async () => {
      const { MultiModelFallback } = await import('../models/multiModelFallback');
      
      expect(() => {
        new MultiModelFallback();
      }).not.toThrow();
    });

    it('should have correct provider order', async () => {
      const { MultiModelFallback } = await import('../models/multiModelFallback');
      
      const fallback = new MultiModelFallback();
      const providers = fallback.getAvailableProviders();
      
      // Should have at least one provider available
      expect(providers.length).toBeGreaterThan(0);
    });
  });

  describe('Memory Manager', () => {
    it('should initialize without errors', async () => {
      expect(() => {
        const { MemoryManager } = require('../memory/memoryManager');
        new MemoryManager();
      }).not.toThrow();
    });
  });
});
