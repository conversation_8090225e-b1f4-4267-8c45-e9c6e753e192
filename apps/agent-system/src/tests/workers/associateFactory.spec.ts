import { describe, it, expect, beforeEach, mock } from 'bun:test';
import { AssociateWorkerFactory, WorkerType, BudgetAnalyzerWorker } from '../../workers/associateFactory';

// Mock dependencies
mock.module('../../models/multiModelFallback', () => ({
  multiModelFallback: {
    invoke: mock(async () => ({ content: 'Mock analysis result' }))
  }
}));

mock.module('../../memory/supabaseClient', () => ({
  supabaseService: {
    from: mock(() => ({
      insert: mock(() => ({ error: null })),
      update: mock(() => ({ eq: mock(() => ({ error: null })) }))
    }))
  }
}));

mock.module('../../memory/memoryManager', () => ({
  memoryManager: {
    storeMemory: mock(async () => {})
  }
}));

describe('Associate Worker Factory', () => {
  const testParentRunId = '550e8400-e29b-41d4-a716-446655440000';
  const testWeddingId = '550e8400-e29b-41d4-a716-446655440001';

  beforeEach(() => {
    // Reset mocks
    mock.restore();
  });

  describe('AssociateWorkerFactory', () => {
    it('should create budget analyzer worker', async () => {
      const worker = await AssociateWorkerFactory.createWorker(
        WorkerType.BUDGET_ANALYZER,
        testParentRunId
      );

      expect(worker).toBeInstanceOf(BudgetAnalyzerWorker);
      expect(worker.type).toBe(WorkerType.BUDGET_ANALYZER);
      expect(worker.status).toBe('created');
    });

    it('should execute task successfully', async () => {
      const task = {
        id: crypto.randomUUID(),
        type: 'budget_analysis',
        description: 'Test budget analysis',
        inputData: {
          weddingId: testWeddingId,
          analysisType: 'comprehensive',
          parameters: {}
        }
      };

      const result = await AssociateWorkerFactory.executeTask(
        WorkerType.BUDGET_ANALYZER,
        task,
        testParentRunId
      );

      expect(result.status).toBe('completed');
      expect(result.workerId).toBeDefined();
      expect(result.outputData).toBeDefined();
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('should track active workers', async () => {
      const worker = await AssociateWorkerFactory.createWorker(
        WorkerType.BUDGET_ANALYZER,
        testParentRunId
      );

      const activeWorkers = await AssociateWorkerFactory.getActiveWorkers();
      expect(activeWorkers).toContain(worker);
    });

    it('should cleanup all workers', async () => {
      await AssociateWorkerFactory.createWorker(WorkerType.BUDGET_ANALYZER, testParentRunId);

      await AssociateWorkerFactory.cleanupAllWorkers();
      const activeWorkers = await AssociateWorkerFactory.getActiveWorkers();
      expect(activeWorkers).toHaveLength(0);
    });
  });

  describe('BudgetAnalyzerWorker', () => {
    it('should execute budget analysis task', async () => {
      const worker = new BudgetAnalyzerWorker(testParentRunId);
      const task = {
        id: crypto.randomUUID(),
        type: 'budget_analysis',
        description: 'Comprehensive budget analysis',
        inputData: {
          weddingId: testWeddingId,
          analysisType: 'comprehensive',
          parameters: { includeProjections: true }
        }
      };

      const result = await worker.execute(task);

      expect(result.status).toBe('completed');
      expect(result.outputData?.analysis).toBeDefined();
      expect(result.outputData?.analysisType).toBe('comprehensive');
    });
  });
});
