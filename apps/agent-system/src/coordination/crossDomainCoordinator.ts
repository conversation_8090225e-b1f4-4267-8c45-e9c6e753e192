import { z } from 'zod';
import { supabaseService } from '../memory/supabaseClient';

// Coordination schemas
export const CoordinationRequestSchema = z.object({
  primaryRunId: z.string().uuid(),
  secondaryRunId: z.string().uuid(),
  coordinationType: z.enum(['sequential', 'parallel', 'conditional', 'merge']),
  sharedState: z.record(z.any()).optional(),
  priority: z.number().min(0).max(10).default(5),
  conditions: z.record(z.any()).optional()
});

export const WorkflowStepSchema = z.object({
  stepName: z.string(),
  stepOrder: z.number(),
  agentType: z.string(),
  inputData: z.record(z.any()).optional(),
  conditions: z.record(z.any()).optional(),
  dependencies: z.array(z.string()).optional()
});

export const WorkflowDefinitionSchema = z.object({
  workflowName: z.string(),
  description: z.string(),
  steps: z.array(WorkflowStepSchema),
  metadata: z.record(z.any()).optional()
});

export type CoordinationRequest = z.infer<typeof CoordinationRequestSchema>;
export type WorkflowStep = z.infer<typeof WorkflowStepSchema>;
export type WorkflowDefinition = z.infer<typeof WorkflowDefinitionSchema>;

// Cross-Domain Coordination System
export class CrossDomainCoordinator {
  private activeCoordinations: Map<string, CoordinationRequest> = new Map();

  /**
   * Create coordination between two agent runs
   */
  async createCoordination(request: CoordinationRequest): Promise<string> {
    try {
      // Validate request
      CoordinationRequestSchema.parse(request);

      // Store coordination in database
      const { data, error } = await supabaseService
        .from('agent_coordination')
        .insert({
          primary_run_id: request.primaryRunId,
          secondary_run_id: request.secondaryRunId,
          coordination_type: request.coordinationType,
          shared_state: request.sharedState || {},
          priority: request.priority,
          status: 'pending'
        })
        .select()
        .single();

      if (error) {
        throw new Error(`Failed to create coordination: ${error.message}`);
      }

      const coordinationId = data.id;
      this.activeCoordinations.set(coordinationId, request);

      return coordinationId;
    } catch (error) {
      console.error('Error creating coordination:', error);
      throw error;
    }
  }

  /**
   * Execute a complex workflow across multiple domains
   */
  async executeWorkflow(
    workflowDefinition: WorkflowDefinition,
    weddingId: string,
    userId: string,
    inputData: Record<string, any> = {}
  ): Promise<string> {
    try {
      // Validate workflow definition
      WorkflowDefinitionSchema.parse(workflowDefinition);

      // Create workflow execution record
      const { data: execution, error: executionError } = await supabaseService
        .from('workflow_executions')
        .insert({
          workflow_name: workflowDefinition.workflowName,
          wedding_id: weddingId,
          user_id: userId,
          status: 'pending',
          input_data: inputData,
          total_steps: workflowDefinition.steps.length
        })
        .select()
        .single();

      if (executionError) {
        throw new Error(`Failed to create workflow execution: ${executionError.message}`);
      }

      const executionId = execution.id;

      // Create workflow steps
      const steps = workflowDefinition.steps.map(step => ({
        workflow_execution_id: executionId,
        step_name: step.stepName,
        step_order: step.stepOrder,
        agent_type: step.agentType,
        status: 'pending',
        input_data: step.inputData || {}
      }));

      const { error: stepsError } = await supabaseService
        .from('workflow_steps')
        .insert(steps);

      if (stepsError) {
        throw new Error(`Failed to create workflow steps: ${stepsError.message}`);
      }

      return executionId;
    } catch (error) {
      console.error('Error executing workflow:', error);
      throw error;
    }
  }

  /**
   * Get shared state between coordinated agents
   */
  async getSharedState(coordinationId: string): Promise<Record<string, any>> {
    try {
      const { data, error } = await supabaseService
        .from('agent_coordination')
        .select('shared_state')
        .eq('id', coordinationId)
        .single();

      if (error) {
        throw new Error(`Failed to get shared state: ${error.message}`);
      }

      return data.shared_state || {};
    } catch (error) {
      console.error('Error getting shared state:', error);
      throw error;
    }
  }

  /**
   * Update shared state between coordinated agents
   */
  async updateSharedState(
    coordinationId: string,
    updates: Record<string, any>
  ): Promise<void> {
    try {
      // Get current shared state
      const currentState = await this.getSharedState(coordinationId);
      const newState = { ...currentState, ...updates };

      // Update in database
      const { error } = await supabaseService
        .from('agent_coordination')
        .update({
          shared_state: newState,
          updated_at: new Date().toISOString()
        })
        .eq('id', coordinationId);

      if (error) {
        throw new Error(`Failed to update shared state: ${error.message}`);
      }

      console.log('Shared state updated successfully');
    } catch (error) {
      console.error('Error updating shared state:', error);
      throw error;
    }
  }

  /**
   * Get active coordinations
   */
  async getActiveCoordinations(): Promise<CoordinationRequest[]> {
    return Array.from(this.activeCoordinations.values());
  }

  /**
   * Cancel coordination
   */
  async cancelCoordination(coordinationId: string): Promise<boolean> {
    try {
      await supabaseService
        .from('agent_coordination')
        .update({ status: 'cancelled' })
        .eq('id', coordinationId);

      this.activeCoordinations.delete(coordinationId);
      return true;
    } catch (error) {
      console.error('Error cancelling coordination:', error);
      return false;
    }
  }
}

// Singleton instance
export const crossDomainCoordinator = new CrossDomainCoordinator();
