import { z } from 'zod';
import { supabaseService } from '../memory/supabaseClient';

// Analytics schemas
export const MetricSchema = z.object({
  agentType: z.string(),
  metricName: z.string(),
  metricValue: z.number(),
  metricUnit: z.string().optional(),
  timePeriod: z.enum(['hour', 'day', 'week', 'month']),
  periodStart: z.date(),
  periodEnd: z.date(),
  metadata: z.record(z.any()).optional()
});

export const SatisfactionDataSchema = z.object({
  userId: z.string().uuid(),
  weddingId: z.string().uuid(),
  sessionId: z.string().optional(),
  satisfactionScore: z.number().min(1).max(5),
  interactionCount: z.number().min(1).default(1),
  successfulResolutions: z.number().min(0).default(0),
  escalations: z.number().min(0).default(0),
  sessionDurationSeconds: z.number().min(0).optional(),
  feedbackProvided: z.boolean().default(false)
});

export type Metric = z.infer<typeof MetricSchema>;
export type SatisfactionData = z.infer<typeof SatisfactionDataSchema>;

// Performance Analytics System
export class PerformanceAnalyticsSystem {
  /**
   * Record a performance metric
   */
  async recordMetric(metric: Metric): Promise<void> {
    try {
      // Validate metric
      MetricSchema.parse(metric);

      // Store metric in database
      const { error } = await supabaseService
        .from('agent_performance_metrics')
        .insert({
          agent_type: metric.agentType,
          metric_name: metric.metricName,
          metric_value: metric.metricValue,
          metric_unit: metric.metricUnit,
          time_period: metric.timePeriod,
          period_start: metric.periodStart.toISOString(),
          period_end: metric.periodEnd.toISOString(),
          metadata: metric.metadata || {}
        });

      if (error) {
        throw new Error(`Failed to record metric: ${error.message}`);
      }

      console.log('Metric recorded successfully:', metric.metricName);
    } catch (error) {
      console.error('Error recording metric:', error);
      throw error;
    }
  }

  /**
   * Record user satisfaction data
   */
  async recordSatisfaction(satisfaction: SatisfactionData): Promise<void> {
    try {
      // Validate satisfaction data
      SatisfactionDataSchema.parse(satisfaction);

      // Store satisfaction data
      const { error } = await supabaseService
        .from('user_satisfaction')
        .insert({
          user_id: satisfaction.userId,
          wedding_id: satisfaction.weddingId,
          session_id: satisfaction.sessionId,
          satisfaction_score: satisfaction.satisfactionScore,
          interaction_count: satisfaction.interactionCount,
          successful_resolutions: satisfaction.successfulResolutions,
          escalations: satisfaction.escalations,
          session_duration_seconds: satisfaction.sessionDurationSeconds,
          feedback_provided: satisfaction.feedbackProvided
        });

      if (error) {
        throw new Error(`Failed to record satisfaction: ${error.message}`);
      }

      console.log('Satisfaction data recorded successfully');
    } catch (error) {
      console.error('Error recording satisfaction:', error);
      throw error;
    }
  }

  /**
   * Get performance metrics for an agent type
   */
  async getAgentMetrics(
    agentType: string,
    timePeriod: 'hour' | 'day' | 'week' | 'month',
    startDate: Date,
    endDate: Date
  ): Promise<{
    responseTime: number;
    successRate: number;
    userSatisfaction: number;
    throughput: number;
    errorRate: number;
    metrics: Metric[];
  }> {
    try {
      // Get metrics from database
      const { data: metricsData, error } = await supabaseService
        .from('agent_performance_metrics')
        .select('*')
        .eq('agent_type', agentType)
        .eq('time_period', timePeriod)
        .gte('period_start', startDate.toISOString())
        .lte('period_end', endDate.toISOString())
        .order('period_start', { ascending: true });

      if (error) {
        throw new Error(`Failed to get agent metrics: ${error.message}`);
      }

      const metrics = metricsData || [];

      // Calculate aggregate metrics
      const responseTimeMetrics = metrics.filter(m => m.metric_name === 'response_time');
      const responseTime = responseTimeMetrics.length > 0 
        ? responseTimeMetrics.reduce((sum, m) => sum + m.metric_value, 0) / responseTimeMetrics.length
        : 0;

      const successRateMetrics = metrics.filter(m => m.metric_name === 'success_rate');
      const successRate = successRateMetrics.length > 0
        ? successRateMetrics.reduce((sum, m) => sum + m.metric_value, 0) / successRateMetrics.length
        : 0;

      const throughputMetrics = metrics.filter(m => m.metric_name === 'throughput');
      const throughput = throughputMetrics.length > 0
        ? throughputMetrics.reduce((sum, m) => sum + m.metric_value, 0)
        : 0;

      const errorRateMetrics = metrics.filter(m => m.metric_name === 'error_rate');
      const errorRate = errorRateMetrics.length > 0
        ? errorRateMetrics.reduce((sum, m) => sum + m.metric_value, 0) / errorRateMetrics.length
        : 0;

      // Get user satisfaction for this agent type
      const userSatisfaction = await this.getAgentSatisfactionScore(agentType, startDate, endDate);

      return {
        responseTime,
        successRate,
        userSatisfaction,
        throughput,
        errorRate,
        metrics: metrics.map(m => ({
          agentType: m.agent_type,
          metricName: m.metric_name,
          metricValue: m.metric_value,
          metricUnit: m.metric_unit,
          timePeriod: m.time_period as 'hour' | 'day' | 'week' | 'month',
          periodStart: new Date(m.period_start),
          periodEnd: new Date(m.period_end),
          metadata: m.metadata
        }))
      };
    } catch (error) {
      console.error('Error getting agent metrics:', error);
      throw error;
    }
  }

  /**
   * Get overall system performance dashboard
   */
  async getSystemDashboard(
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalInteractions: number;
    averageResponseTime: number;
    overallSatisfaction: number;
    agentPerformance: Array<{
      agentType: string;
      interactions: number;
      satisfaction: number;
      responseTime: number;
      successRate: number;
    }>;
    trends: {
      satisfactionTrend: number;
      responseTrend: number;
      volumeTrend: number;
    };
  }> {
    try {
      // Get total interactions
      const { data: runsData, error: runsError } = await supabaseService
        .from('agent_runs')
        .select('agent_type')
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (runsError) {
        throw new Error(`Failed to get runs data: ${runsError.message}`);
      }

      const totalInteractions = runsData?.length || 0;

      // Simplified dashboard for now
      return {
        totalInteractions,
        averageResponseTime: 2500, // placeholder
        overallSatisfaction: 4.2, // placeholder
        agentPerformance: [],
        trends: {
          satisfactionTrend: 5.2,
          responseTrend: -8.1,
          volumeTrend: 12.3
        }
      };
    } catch (error) {
      console.error('Error getting system dashboard:', error);
      throw error;
    }
  }

  /**
   * Generate performance optimization recommendations
   */
  async generateOptimizationRecommendations(
    agentType?: string
  ): Promise<Array<{
    type: 'performance' | 'satisfaction' | 'efficiency';
    priority: 'high' | 'medium' | 'low';
    recommendation: string;
    expectedImpact: string;
    implementation: string;
  }>> {
    // Simplified recommendations for now
    return [
      {
        type: 'performance',
        priority: 'medium',
        recommendation: 'Implement response caching for common queries',
        expectedImpact: 'Reduce response time by 20-30%',
        implementation: 'Add Redis cache layer for frequent responses'
      },
      {
        type: 'satisfaction',
        priority: 'high',
        recommendation: 'Enhance response personalization',
        expectedImpact: 'Increase satisfaction score by 0.3-0.5 points',
        implementation: 'Use user preference data in response generation'
      }
    ];
  }

  private async getAgentSatisfactionScore(
    agentType: string,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const { data, error } = await supabaseService
        .from('user_satisfaction')
        .select(`
          satisfaction_score,
          agent_runs!inner(agent_type)
        `)
        .eq('agent_runs.agent_type', agentType)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (error) {
        throw new Error(`Failed to get satisfaction score: ${error.message}`);
      }

      const satisfactionData = data || [];
      return satisfactionData.length > 0
        ? satisfactionData.reduce((sum, s) => sum + s.satisfaction_score, 0) / satisfactionData.length
        : 0;
    } catch (error) {
      console.error('Error getting agent satisfaction score:', error);
      return 0;
    }
  }
}

// Singleton instance
export const performanceAnalytics = new PerformanceAnalyticsSystem();
