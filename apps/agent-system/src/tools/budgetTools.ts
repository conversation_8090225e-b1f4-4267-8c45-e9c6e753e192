import { z } from 'zod';
import { supabase } from '../memory/supabaseClient';

// Zod schemas for validation
export const BudgetStatusSchema = z.object({
  weddingId: z.string().uuid()
});

export const UpdateBudgetItemSchema = z.object({
  itemId: z.string().uuid(),
  newAmount: z.number().positive(),
  reason: z.string().optional()
});

export const AddBudgetCategorySchema = z.object({
  weddingId: z.string().uuid(),
  category: z.string().min(1),
  estimatedCost: z.number().positive(),
  vendor: z.string().optional(),
  notes: z.string().optional()
});

export const GenerateBudgetReportSchema = z.object({
  weddingId: z.string().uuid(),
  includeProjections: z.boolean().default(true)
});

// Types
export interface BudgetItem {
  id: string;
  category: string;
  estimatedCost: number;
  actualCost: number;
  vendor?: string;
  paymentDueDate?: string;
  isPaid: boolean;
}

export interface BudgetStatus {
  totalBudget: number;
  totalSpent: number;
  totalEstimated: number;
  remaining: number;
  categories: BudgetItem[];
  overBudgetCategories: BudgetItem[];
  upcomingPayments: BudgetItem[];
}

/**
 * Get current budget status and breakdown
 */
export async function getBudgetStatus(weddingId: string): Promise<BudgetStatus> {
  try {
    // Validate input
    BudgetStatusSchema.parse({ weddingId });

    // Get wedding details for total budget
    const { data: weddingData, error: weddingError } = await supabase
      .from('weddings')
      .select('initial_budget')
      .eq('id', weddingId)
      .single();

    if (weddingError) {
      throw new Error(`Failed to get wedding data: ${weddingError.message}`);
    }

    // Get all budget items
    const { data: budgetItems, error: budgetError } = await supabase
      .from('budget_items')
      .select('*')
      .eq('wedding_id', weddingId)
      .order('category');

    if (budgetError) {
      throw new Error(`Failed to get budget items: ${budgetError.message}`);
    }

    const items = budgetItems || [];
    const totalBudget = weddingData?.initial_budget || 0;
    const totalEstimated = items.reduce((sum, item) => sum + item.estimated_cost, 0);
    const totalSpent = items.reduce((sum, item) => sum + item.actual_cost, 0);
    const remaining = totalBudget - totalSpent;

    // Find over-budget categories
    const overBudgetCategories = items.filter(item => item.actual_cost > item.estimated_cost);

    // Find upcoming payments (due within 30 days)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    
    const upcomingPayments = items.filter(item => 
      !item.is_paid && 
      item.payment_due_date && 
      new Date(item.payment_due_date) <= thirtyDaysFromNow
    );

    return {
      totalBudget,
      totalSpent,
      totalEstimated,
      remaining,
      categories: items.map(item => ({
        id: item.id,
        category: item.category,
        estimatedCost: item.estimated_cost,
        actualCost: item.actual_cost,
        vendor: item.vendor,
        paymentDueDate: item.payment_due_date,
        isPaid: item.is_paid
      })),
      overBudgetCategories: overBudgetCategories.map(item => ({
        id: item.id,
        category: item.category,
        estimatedCost: item.estimated_cost,
        actualCost: item.actual_cost,
        vendor: item.vendor,
        paymentDueDate: item.payment_due_date,
        isPaid: item.is_paid
      })),
      upcomingPayments: upcomingPayments.map(item => ({
        id: item.id,
        category: item.category,
        estimatedCost: item.estimated_cost,
        actualCost: item.actual_cost,
        vendor: item.vendor,
        paymentDueDate: item.payment_due_date,
        isPaid: item.is_paid
      }))
    };

  } catch (error) {
    console.error('Error getting budget status:', error);
    throw error;
  }
}

/**
 * Update a specific budget item
 */
export async function updateBudgetItem(
  itemId: string, 
  newAmount: number, 
  reason?: string
): Promise<{ success: boolean; message: string }> {
  try {
    // Validate input
    UpdateBudgetItemSchema.parse({ itemId, newAmount, reason });

    // Update the budget item
    const { data, error } = await supabase
      .from('budget_items')
      .update({ 
        actual_cost: newAmount,
        updated_at: new Date().toISOString()
      })
      .eq('id', itemId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update budget item: ${error.message}`);
    }

    return {
      success: true,
      message: `Successfully updated ${data.category} budget to $${newAmount.toLocaleString()}`
    };

  } catch (error) {
    console.error('Error updating budget item:', error);
    return {
      success: false,
      message: `Failed to update budget item: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Add a new budget category
 */
export async function addBudgetCategory(
  weddingId: string,
  category: string,
  estimatedCost: number,
  vendor?: string,
  notes?: string
): Promise<{ success: boolean; message: string; itemId?: string }> {
  try {
    // Validate input
    AddBudgetCategorySchema.parse({ weddingId, category, estimatedCost, vendor, notes });

    // Check if category already exists
    const { data: existingItem } = await supabase
      .from('budget_items')
      .select('id')
      .eq('wedding_id', weddingId)
      .eq('category', category)
      .single();

    if (existingItem) {
      return {
        success: false,
        message: `Budget category "${category}" already exists. Use update instead.`
      };
    }

    // Add new budget item
    const { data, error } = await supabase
      .from('budget_items')
      .insert({
        wedding_id: weddingId,
        category,
        estimated_cost: estimatedCost,
        actual_cost: 0,
        vendor: vendor || null,
        is_paid: false,
        notes: notes || null
      })
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to add budget category: ${error.message}`);
    }

    return {
      success: true,
      message: `Successfully added "${category}" category with estimated cost of $${estimatedCost.toLocaleString()}`,
      itemId: data.id
    };

  } catch (error) {
    console.error('Error adding budget category:', error);
    return {
      success: false,
      message: `Failed to add budget category: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Generate comprehensive budget report
 */
export async function generateBudgetReport(
  weddingId: string,
  includeProjections: boolean = true
): Promise<{
  summary: string;
  recommendations: string[];
  warnings: string[];
  projections?: string;
}> {
  try {
    // Validate input
    GenerateBudgetReportSchema.parse({ weddingId, includeProjections });

    const budgetStatus = await getBudgetStatus(weddingId);
    
    // Generate summary
    const overBudgetAmount = budgetStatus.totalSpent - budgetStatus.totalBudget;
    const percentSpent = (budgetStatus.totalSpent / budgetStatus.totalBudget) * 100;
    
    let summary = `Budget Report Summary:\n`;
    summary += `• Total Budget: $${budgetStatus.totalBudget.toLocaleString()}\n`;
    summary += `• Total Spent: $${budgetStatus.totalSpent.toLocaleString()}\n`;
    summary += `• Remaining: $${budgetStatus.remaining.toLocaleString()}\n`;
    summary += `• Percentage Spent: ${percentSpent.toFixed(1)}%\n`;
    
    if (overBudgetAmount > 0) {
      summary += `• ⚠️ Over Budget by: $${overBudgetAmount.toLocaleString()}\n`;
    }

    // Generate recommendations
    const recommendations: string[] = [];
    const warnings: string[] = [];

    if (budgetStatus.overBudgetCategories.length > 0) {
      warnings.push(`${budgetStatus.overBudgetCategories.length} categories are over budget`);
      recommendations.push('Review over-budget categories and consider cost-cutting measures');
    }

    if (budgetStatus.upcomingPayments.length > 0) {
      warnings.push(`${budgetStatus.upcomingPayments.length} payments due within 30 days`);
      recommendations.push('Prepare for upcoming payments to avoid late fees');
    }

    if (percentSpent > 90) {
      warnings.push('You have spent over 90% of your budget');
      recommendations.push('Consider postponing non-essential expenses');
    } else if (percentSpent > 75) {
      recommendations.push('Monitor spending closely as you approach your budget limit');
    }

    if (budgetStatus.remaining > 0 && percentSpent < 50) {
      recommendations.push('You have good budget flexibility for additional expenses');
    }

    // Generate projections if requested
    let projections = '';
    if (includeProjections) {
      const avgMonthlySpend = budgetStatus.totalSpent / Math.max(1, getMonthsSinceStart());
      const projectedTotal = avgMonthlySpend * getMonthsUntilWedding(weddingId);
      
      projections = `Budget Projections:\n`;
      projections += `• Average monthly spending: $${avgMonthlySpend.toLocaleString()}\n`;
      projections += `• Projected total at current rate: $${projectedTotal.toLocaleString()}\n`;
      
      if (projectedTotal > budgetStatus.totalBudget) {
        projections += `• ⚠️ Projected overage: $${(projectedTotal - budgetStatus.totalBudget).toLocaleString()}\n`;
      }
    }

    return {
      summary,
      recommendations,
      warnings,
      projections: includeProjections ? projections : undefined
    };

  } catch (error) {
    console.error('Error generating budget report:', error);
    throw error;
  }
}

// Helper functions
function getMonthsSinceStart(): number {
  // This would typically calculate months since wedding planning started
  // For now, return a default value
  return 3;
}

function getMonthsUntilWedding(weddingId: string): number {
  // This would calculate months until the wedding date
  // For now, return a default value
  return 6;
}
