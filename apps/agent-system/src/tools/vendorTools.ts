import { z } from 'zod';
import { supabase } from '../memory/supabaseClient';
import { createClient } from '@supabase/supabase-js';

// Vendor data client (separate Supabase project)
const supabaseDataUrl = process.env.NEXT_PUBLIC_SUPABASE_DATA_URL || '';
const supabaseDataKey = process.env.NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY || '';

const supabaseData = createClient(supabaseDataUrl, supabaseDataKey);

// Zod schemas for validation
export const SearchVendorsSchema = z.object({
  category: z.string().min(1),
  location: z.string().min(1),
  maxPrice: z.number().positive().optional(),
  weddingDate: z.string().optional()
});

export const GetVendorDetailsSchema = z.object({
  vendorId: z.string().min(1)
});

export const CheckAvailabilitySchema = z.object({
  vendorId: z.string().min(1),
  weddingDate: z.string().min(1)
});

export const BookVendorSchema = z.object({
  vendorId: z.string().min(1),
  weddingId: z.string().uuid(),
  details: z.object({
    serviceType: z.string(),
    eventDate: z.string(),
    budget: z.number().optional(),
    notes: z.string().optional()
  })
});

// Types
export interface Vendor {
  id: string;
  business_name: string;
  description: string;
  city: string;
  state: string;
  website: string;
  phone: string;
  email: string;
  address: string;
  zip_code?: string;
  review_score?: number;
  review_count?: number;
  pricing?: any;
  photo_gallery?: string[];
  category?: string;
}

export interface VendorSearchResult {
  vendors: Vendor[];
  total: number;
  hasMore: boolean;
}

export interface AvailabilityResult {
  available: boolean;
  message: string;
  alternativeDates?: string[];
}

/**
 * Search vendors in the vendor directory
 */
export async function searchVendors(
  category: string,
  location: string,
  maxPrice?: number,
  weddingDate?: string
): Promise<Vendor[]> {
  try {
    // Validate input
    SearchVendorsSchema.parse({ category, location, maxPrice, weddingDate });

    let query = supabaseData
      .from('vendors')
      .select('*');

    // Filter by category if not 'all'
    if (category !== 'all') {
      // Map common category names to database categories
      const categoryMap: Record<string, string[]> = {
        'photographer': ['Photography', 'Photographer'],
        'florist': ['Florist', 'Flowers'],
        'caterer': ['Catering', 'Caterer'],
        'dj': ['DJ', 'Music', 'Entertainment'],
        'band': ['Band', 'Music', 'Entertainment'],
        'venue': ['Venue', 'Reception Venue'],
        'baker': ['Bakery', 'Cake', 'Desserts'],
        'videographer': ['Videography', 'Video']
      };

      const searchCategories = categoryMap[category.toLowerCase()] || [category];
      query = query.in('category', searchCategories);
    }

    // Filter by location (city or state)
    const locationParts = location.split(',').map(part => part.trim());
    if (locationParts.length >= 1) {
      const city = locationParts[0];
      query = query.ilike('city', `%${city}%`);
    }
    if (locationParts.length >= 2) {
      const state = locationParts[1];
      query = query.ilike('state', `%${state}%`);
    }

    // Filter by price if specified
    if (maxPrice) {
      query = query.or(`pricing->starting_price.lte.${maxPrice},pricing->starting_price.is.null`);
    }

    // Order by review score and limit results
    query = query
      .order('review_score', { ascending: false })
      .order('review_count', { ascending: false })
      .limit(20);

    const { data, error } = await query;

    if (error) {
      throw new Error(`Failed to search vendors: ${error.message}`);
    }

    return data || [];

  } catch (error) {
    console.error('Error searching vendors:', error);
    throw error;
  }
}

/**
 * Get detailed information about a specific vendor
 */
export async function getVendorDetails(vendorId: string): Promise<Vendor | null> {
  try {
    // Validate input
    GetVendorDetailsSchema.parse({ vendorId });

    const { data, error } = await supabaseData
      .from('vendors')
      .select('*')
      .eq('id', vendorId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // Vendor not found
      }
      throw new Error(`Failed to get vendor details: ${error.message}`);
    }

    return data;

  } catch (error) {
    console.error('Error getting vendor details:', error);
    throw error;
  }
}

/**
 * Check vendor availability for a specific date
 */
export async function checkVendorAvailability(
  vendorId: string,
  weddingDate: string
): Promise<AvailabilityResult> {
  try {
    // Validate input
    CheckAvailabilitySchema.parse({ vendorId, weddingDate });

    // Get vendor details first
    const vendor = await getVendorDetails(vendorId);
    if (!vendor) {
      return {
        available: false,
        message: 'Vendor not found'
      };
    }

    // Check if vendor is already booked for this date
    const { data: existingBookings, error } = await supabase
      .from('vendor_bookings')
      .select('*')
      .eq('vendor_id', vendorId)
      .eq('event_date', weddingDate)
      .eq('status', 'confirmed');

    if (error) {
      console.error('Error checking existing bookings:', error);
      // Continue with availability check even if booking check fails
    }

    const hasConflict = existingBookings && existingBookings.length > 0;

    if (hasConflict) {
      return {
        available: false,
        message: `${vendor.business_name} is already booked for ${weddingDate}`,
        alternativeDates: generateAlternativeDates(weddingDate)
      };
    }

    // For now, assume vendor is available if no conflicts
    // In a real system, this would check the vendor's calendar or availability system
    return {
      available: true,
      message: `${vendor.business_name} appears to be available for ${weddingDate}. Contact them directly to confirm.`
    };

  } catch (error) {
    console.error('Error checking vendor availability:', error);
    return {
      available: false,
      message: 'Unable to check availability at this time'
    };
  }
}

/**
 * Book a vendor for a wedding
 */
export async function bookVendor(
  vendorId: string,
  weddingId: string,
  details: {
    serviceType: string;
    eventDate: string;
    budget?: number;
    notes?: string;
  }
): Promise<{ success: boolean; message: string; bookingId?: string }> {
  try {
    // Validate input
    BookVendorSchema.parse({ vendorId, weddingId, details });

    // Get vendor details
    const vendor = await getVendorDetails(vendorId);
    if (!vendor) {
      return {
        success: false,
        message: 'Vendor not found'
      };
    }

    // Check availability first
    const availability = await checkVendorAvailability(vendorId, details.eventDate);
    if (!availability.available) {
      return {
        success: false,
        message: availability.message
      };
    }

    // Create booking record
    const { data: booking, error: bookingError } = await supabase
      .from('vendor_bookings')
      .insert({
        wedding_id: weddingId,
        vendor_id: vendorId,
        vendor_name: vendor.business_name,
        service_type: details.serviceType,
        event_date: details.eventDate,
        budget: details.budget,
        notes: details.notes,
        status: 'pending', // Requires vendor confirmation
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (bookingError) {
      throw new Error(`Failed to create booking: ${bookingError.message}`);
    }

    // Add to user's vendor list
    const { error: vendorListError } = await supabase
      .from('wedding_vendors')
      .insert({
        wedding_id: weddingId,
        vendor_id: vendorId,
        vendor_name: vendor.business_name,
        category: vendor.category || details.serviceType,
        status: 'contacted',
        contact_person: vendor.business_name,
        email: vendor.email,
        phone: vendor.phone,
        quoted_price: details.budget,
        notes: `Booking requested via AI assistant. ${details.notes || ''}`
      });

    if (vendorListError) {
      console.error('Error adding to vendor list:', vendorListError);
      // Continue even if this fails
    }

    return {
      success: true,
      message: `Booking request sent to ${vendor.business_name}. They will contact you to confirm details and availability.`,
      bookingId: booking.id
    };

  } catch (error) {
    console.error('Error booking vendor:', error);
    return {
      success: false,
      message: `Failed to book vendor: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Generate alternative dates around the requested date
 */
function generateAlternativeDates(originalDate: string): string[] {
  const date = new Date(originalDate);
  const alternatives: string[] = [];

  // Generate dates within 2 weeks before and after
  for (let i = -14; i <= 14; i += 7) {
    if (i === 0) continue; // Skip the original date
    
    const altDate = new Date(date);
    altDate.setDate(date.getDate() + i);
    
    // Only suggest future dates
    if (altDate > new Date()) {
      alternatives.push(altDate.toISOString().split('T')[0]);
    }
  }

  return alternatives.slice(0, 3); // Return up to 3 alternatives
}

/**
 * Get vendor recommendations based on wedding details
 */
export async function getVendorRecommendations(
  weddingId: string,
  category: string,
  budget?: number
): Promise<Vendor[]> {
  try {
    // Get wedding details for location and date
    const { data: wedding, error: weddingError } = await supabase
      .from('weddings')
      .select('location, wedding_date, guest_count')
      .eq('id', weddingId)
      .single();

    if (weddingError) {
      console.error('Error getting wedding details:', weddingError);
      // Continue with default location
    }

    const location = wedding?.location || 'Austin, TX';
    const vendors = await searchVendors(category, location, budget);

    // Sort by relevance (review score, price match, etc.)
    return vendors.sort((a, b) => {
      let scoreA = a.review_score || 0;
      let scoreB = b.review_score || 0;

      // Boost score if price is within budget
      if (budget && a.pricing?.starting_price && a.pricing.starting_price <= budget) {
        scoreA += 1;
      }
      if (budget && b.pricing?.starting_price && b.pricing.starting_price <= budget) {
        scoreB += 1;
      }

      return scoreB - scoreA;
    });

  } catch (error) {
    console.error('Error getting vendor recommendations:', error);
    throw error;
  }
}
