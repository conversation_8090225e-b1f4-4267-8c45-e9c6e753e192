import { z } from 'zod';
import { supabaseService } from '../memory/supabaseClient';
import { memoryManager } from '../memory/memoryManager';

// Feedback schemas
export const FeedbackSchema = z.object({
  runId: z.string().uuid(),
  userId: z.string().uuid(),
  weddingId: z.string().uuid(),
  feedbackType: z.enum(['rating', 'thumbs', 'text', 'correction']),
  rating: z.number().min(1).max(5).optional(),
  thumbsDirection: z.enum(['up', 'down']).optional(),
  feedbackText: z.string().optional(),
  correctionData: z.record(z.any()).optional(),
  agentResponseId: z.string().optional(),
  contextData: z.record(z.any()).optional()
});

export type Feedback = z.infer<typeof FeedbackSchema>;

// Feedback Collection System
export class FeedbackCollectionSystem {
  /**
   * Collect user feedback for an agent response
   */
  async collectFeedback(feedback: Feedback): Promise<void> {
    try {
      // Validate feedback
      FeedbackSchema.parse(feedback);

      // Store feedback in database
      const { error } = await supabaseService
        .from('agent_feedback')
        .insert({
          run_id: feedback.runId,
          user_id: feedback.userId,
          wedding_id: feedback.weddingId,
          feedback_type: feedback.feedbackType,
          rating: feedback.rating,
          thumbs_direction: feedback.thumbsDirection,
          feedback_text: feedback.feedbackText,
          correction_data: feedback.correctionData,
          agent_response_id: feedback.agentResponseId,
          context_data: feedback.contextData || {}
        });

      if (error) {
        throw new Error(`Failed to store feedback: ${error.message}`);
      }

      // Store feedback in memory for immediate learning
      await memoryManager.storeMemory({
        content: `User Feedback: ${feedback.feedbackType} - ${feedback.feedbackText || feedback.rating || feedback.thumbsDirection}`,
        weddingId: feedback.weddingId,
        metadata: {
          type: 'user_feedback',
          feedback_type: feedback.feedbackType,
          run_id: feedback.runId,
          rating: feedback.rating,
          thumbs: feedback.thumbsDirection
        }
      });

      console.log('Feedback collected successfully');
    } catch (error) {
      console.error('Error collecting feedback:', error);
      throw error;
    }
  }

  /**
   * Calculate response quality score
   */
  async calculateQualityScore(
    runId: string,
    agentType: string,
    responseContent: string,
    userFeedback?: Feedback
  ): Promise<{
    runId: string;
    agentType: string;
    responseContent: string;
    qualityScore: number;
    confidenceScore?: number;
    relevanceScore?: number;
    helpfulnessScore?: number;
    userFeedbackScore?: number;
  }> {
    try {
      // Calculate base quality metrics
      const relevanceScore = await this.calculateRelevanceScore(responseContent);
      const helpfulnessScore = await this.calculateHelpfulnessScore(responseContent);
      const confidenceScore = await this.calculateConfidenceScore(responseContent);
      
      // Incorporate user feedback if available
      let userFeedbackScore = 0.5; // neutral default
      if (userFeedback) {
        userFeedbackScore = this.convertFeedbackToScore(userFeedback);
      }

      // Calculate overall quality score (weighted average)
      const qualityScore = (
        relevanceScore * 0.3 +
        helpfulnessScore * 0.3 +
        confidenceScore * 0.2 +
        userFeedbackScore * 0.2
      );

      const qualityData = {
        runId,
        agentType,
        responseContent,
        qualityScore,
        confidenceScore,
        relevanceScore,
        helpfulnessScore,
        userFeedbackScore
      };

      // Store quality score
      await this.storeQualityScore(qualityData);

      return qualityData;
    } catch (error) {
      console.error('Error calculating quality score:', error);
      throw error;
    }
  }

  /**
   * Get feedback analytics for an agent type
   */
  async getFeedbackAnalytics(
    agentType: string,
    timeRange: { start: Date; end: Date }
  ): Promise<{
    totalFeedback: number;
    averageRating: number;
    thumbsUpPercentage: number;
    commonIssues: string[];
    improvementSuggestions: string[];
  }> {
    try {
      // Get feedback data
      const { data: feedbackData, error } = await supabaseService
        .from('agent_feedback')
        .select(`
          *,
          agent_runs!inner(agent_type)
        `)
        .eq('agent_runs.agent_type', agentType)
        .gte('created_at', timeRange.start.toISOString())
        .lte('created_at', timeRange.end.toISOString());

      if (error) {
        throw new Error(`Failed to get feedback analytics: ${error.message}`);
      }

      const feedback = feedbackData || [];
      
      // Calculate metrics
      const totalFeedback = feedback.length;
      const ratings = feedback.filter(f => f.rating).map(f => f.rating);
      const averageRating = ratings.length > 0 ? ratings.reduce((a, b) => a + b, 0) / ratings.length : 0;
      
      const thumbsVotes = feedback.filter(f => f.thumbs_direction);
      const thumbsUpCount = thumbsVotes.filter(f => f.thumbs_direction === 'up').length;
      const thumbsUpPercentage = thumbsVotes.length > 0 ? Math.round((thumbsUpCount / thumbsVotes.length) * 100 * 100) / 100 : 0;

      // Extract common issues from text feedback
      const textFeedback = feedback.filter(f => f.feedback_text).map(f => f.feedback_text);
      const commonIssues = await this.extractCommonIssues(textFeedback);
      const improvementSuggestions = await this.generateImprovementSuggestions(feedback);

      return {
        totalFeedback,
        averageRating,
        thumbsUpPercentage,
        commonIssues,
        improvementSuggestions
      };
    } catch (error) {
      console.error('Error getting feedback analytics:', error);
      throw error;
    }
  }

  private async calculateRelevanceScore(responseContent: string): Promise<number> {
    const wordCount = responseContent.split(' ').length;
    const hasWeddingTerms = /wedding|bride|groom|venue|budget|guest|vendor/i.test(responseContent);
    
    let score = 0.5; // base score
    if (wordCount > 20 && wordCount < 500) score += 0.2; // appropriate length
    if (hasWeddingTerms) score += 0.3; // relevant content
    
    return Math.min(score, 1.0);
  }

  private async calculateHelpfulnessScore(responseContent: string): Promise<number> {
    const hasActionableAdvice = /should|recommend|suggest|try|consider/i.test(responseContent);
    const hasSpecificDetails = /\$|[0-9]+|specific|detail/i.test(responseContent);
    const hasQuestions = /\?/.test(responseContent);
    
    let score = 0.4; // base score
    if (hasActionableAdvice) score += 0.3;
    if (hasSpecificDetails) score += 0.2;
    if (hasQuestions) score += 0.1; // asking clarifying questions is helpful
    
    return Math.min(score, 1.0);
  }

  private async calculateConfidenceScore(responseContent: string): Promise<number> {
    const uncertaintyWords = /maybe|perhaps|might|could|possibly|unsure/i.test(responseContent);
    const confidentWords = /definitely|certainly|sure|confident|recommend/i.test(responseContent);
    
    let score = 0.5; // base score
    if (confidentWords) score += 0.3;
    if (uncertaintyWords) score -= 0.2;
    
    return Math.max(0, Math.min(score, 1.0));
  }

  private convertFeedbackToScore(feedback: Feedback): number {
    if (feedback.rating) {
      return feedback.rating / 5.0; // Convert 1-5 rating to 0-1 score
    }
    if (feedback.thumbsDirection) {
      return feedback.thumbsDirection === 'up' ? 0.8 : 0.2;
    }
    if (feedback.feedbackText) {
      // Simple sentiment analysis - can be improved with ML
      const positive = /good|great|helpful|excellent|perfect|love/i.test(feedback.feedbackText);
      const negative = /bad|terrible|unhelpful|wrong|hate|awful/i.test(feedback.feedbackText);
      
      if (positive) return 0.8;
      if (negative) return 0.2;
      return 0.5; // neutral
    }
    return 0.5; // default neutral
  }

  private async storeQualityScore(qualityData: any): Promise<void> {
    try {
      const { error } = await supabaseService
        .from('response_quality_scores')
        .insert({
          run_id: qualityData.runId,
          agent_type: qualityData.agentType,
          response_content: qualityData.responseContent,
          quality_score: qualityData.qualityScore,
          confidence_score: qualityData.confidenceScore,
          relevance_score: qualityData.relevanceScore,
          helpfulness_score: qualityData.helpfulnessScore,
          user_feedback_score: qualityData.userFeedbackScore
        });

      if (error) {
        throw new Error(`Failed to store quality score: ${error.message}`);
      }
    } catch (error) {
      console.error('Error storing quality score:', error);
    }
  }

  private async extractCommonIssues(textFeedback: string[]): Promise<string[]> {
    const issues: string[] = [];
    const keywords = ['slow', 'wrong', 'confusing', 'unclear', 'missing', 'error'];
    
    keywords.forEach(keyword => {
      const count = textFeedback.filter(text => 
        text.toLowerCase().includes(keyword)
      ).length;
      
      if (count > 0) {
        issues.push(`${keyword} (${count} mentions)`);
      }
    });
    
    return issues;
  }

  private async generateImprovementSuggestions(feedback: any[]): Promise<string[]> {
    const suggestions: string[] = [];
    
    const lowRatings = feedback.filter(f => f.rating && f.rating < 3).length;
    const thumbsDown = feedback.filter(f => f.thumbs_direction === 'down').length;
    
    if (lowRatings > feedback.length * 0.3) {
      suggestions.push('Focus on improving response quality and relevance');
    }
    
    if (thumbsDown > feedback.length * 0.2) {
      suggestions.push('Review and refine response generation prompts');
    }
    
    return suggestions;
  }
}

// Singleton instance
export const feedbackSystem = new FeedbackCollectionSystem();
