-- Advanced Features Schema Migration
-- Adds support for associate workers, learning system, coordination, and analytics

-- Associate Workers Management
CREATE TABLE agent_workers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  parent_run_id UUID NOT NULL REFERENCES agent_runs(id) ON DELETE CASCADE,
  worker_type TEXT NOT NULL,
  task_description TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('created', 'running', 'completed', 'failed', 'cancelled')),
  input_data JSONB NOT NULL DEFAULT '{}'::JSONB,
  output_data JSONB DEFAULT '{}'::JSONB,
  error_message TEXT,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Worker Task Dependencies
CREATE TABLE worker_dependencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  worker_id UUID NOT NULL REFERENCES agent_workers(id) ON DELETE CASCADE,
  depends_on_worker_id UUID NOT NULL REFERENCES agent_workers(id) ON DELETE CASCADE,
  dependency_type TEXT NOT NULL DEFAULT 'sequential',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(worker_id, depends_on_worker_id)
);

-- Agent Feedback and Learning System
CREATE TABLE agent_feedback (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  run_id UUID NOT NULL REFERENCES agent_runs(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  wedding_id UUID NOT NULL,
  feedback_type TEXT NOT NULL CHECK (feedback_type IN ('rating', 'thumbs', 'text', 'correction')),
  rating INTEGER CHECK (rating >= 1 AND rating <= 5),
  thumbs_direction TEXT CHECK (thumbs_direction IN ('up', 'down')),
  feedback_text TEXT,
  correction_data JSONB,
  agent_response_id TEXT,
  context_data JSONB DEFAULT '{}'::JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Response Quality Scores
CREATE TABLE response_quality_scores (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  run_id UUID NOT NULL REFERENCES agent_runs(id) ON DELETE CASCADE,
  agent_type TEXT NOT NULL,
  response_content TEXT NOT NULL,
  quality_score FLOAT NOT NULL CHECK (quality_score >= 0 AND quality_score <= 1),
  confidence_score FLOAT CHECK (confidence_score >= 0 AND confidence_score <= 1),
  relevance_score FLOAT CHECK (relevance_score >= 0 AND relevance_score <= 1),
  helpfulness_score FLOAT CHECK (helpfulness_score >= 0 AND helpfulness_score <= 1),
  user_feedback_score FLOAT CHECK (user_feedback_score >= 0 AND user_feedback_score <= 1),
  calculated_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cross-Domain Coordination
CREATE TABLE agent_coordination (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  primary_run_id UUID NOT NULL REFERENCES agent_runs(id) ON DELETE CASCADE,
  secondary_run_id UUID NOT NULL REFERENCES agent_runs(id) ON DELETE CASCADE,
  coordination_type TEXT NOT NULL CHECK (coordination_type IN ('sequential', 'parallel', 'conditional', 'merge')),
  shared_state JSONB DEFAULT '{}'::JSONB,
  status TEXT NOT NULL CHECK (status IN ('pending', 'active', 'completed', 'failed')) DEFAULT 'pending',
  priority INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Agent Performance Metrics
CREATE TABLE agent_performance_metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  agent_type TEXT NOT NULL,
  metric_name TEXT NOT NULL,
  metric_value FLOAT NOT NULL,
  metric_unit TEXT,
  time_period TEXT NOT NULL, -- 'hour', 'day', 'week', 'month'
  period_start TIMESTAMPTZ NOT NULL,
  period_end TIMESTAMPTZ NOT NULL,
  metadata JSONB DEFAULT '{}'::JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(agent_type, metric_name, time_period, period_start)
);

-- User Satisfaction Tracking
CREATE TABLE user_satisfaction (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  wedding_id UUID NOT NULL,
  session_id TEXT,
  satisfaction_score INTEGER CHECK (satisfaction_score >= 1 AND satisfaction_score <= 5),
  interaction_count INTEGER DEFAULT 1,
  successful_resolutions INTEGER DEFAULT 0,
  escalations INTEGER DEFAULT 0,
  session_duration_seconds INTEGER,
  feedback_provided BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Learning Model Versions
CREATE TABLE learning_model_versions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  model_type TEXT NOT NULL,
  version_number INTEGER NOT NULL,
  model_data JSONB NOT NULL,
  performance_metrics JSONB DEFAULT '{}'::JSONB,
  training_data_count INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(model_type, version_number)
);

-- Workflow Executions
CREATE TABLE workflow_executions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_name TEXT NOT NULL,
  wedding_id UUID NOT NULL,
  user_id UUID NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')) DEFAULT 'pending',
  input_data JSONB NOT NULL DEFAULT '{}'::JSONB,
  output_data JSONB DEFAULT '{}'::JSONB,
  steps_completed INTEGER DEFAULT 0,
  total_steps INTEGER DEFAULT 0,
  error_message TEXT,
  started_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Workflow Steps
CREATE TABLE workflow_steps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workflow_execution_id UUID NOT NULL REFERENCES workflow_executions(id) ON DELETE CASCADE,
  step_name TEXT NOT NULL,
  step_order INTEGER NOT NULL,
  agent_type TEXT NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'skipped')) DEFAULT 'pending',
  input_data JSONB DEFAULT '{}'::JSONB,
  output_data JSONB DEFAULT '{}'::JSONB,
  error_message TEXT,
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Performance
CREATE INDEX idx_agent_workers_parent_run_id ON agent_workers(parent_run_id);
CREATE INDEX idx_agent_workers_status ON agent_workers(status);
CREATE INDEX idx_agent_workers_worker_type ON agent_workers(worker_type);
CREATE INDEX idx_worker_dependencies_worker_id ON worker_dependencies(worker_id);
CREATE INDEX idx_agent_feedback_run_id ON agent_feedback(run_id);
CREATE INDEX idx_agent_feedback_user_id ON agent_feedback(user_id);
CREATE INDEX idx_agent_feedback_wedding_id ON agent_feedback(wedding_id);
CREATE INDEX idx_response_quality_scores_agent_type ON response_quality_scores(agent_type);
CREATE INDEX idx_agent_coordination_primary_run_id ON agent_coordination(primary_run_id);
CREATE INDEX idx_agent_coordination_status ON agent_coordination(status);
CREATE INDEX idx_agent_performance_metrics_agent_type ON agent_performance_metrics(agent_type);
CREATE INDEX idx_agent_performance_metrics_period ON agent_performance_metrics(period_start, period_end);
CREATE INDEX idx_user_satisfaction_user_id ON user_satisfaction(user_id);
CREATE INDEX idx_user_satisfaction_wedding_id ON user_satisfaction(wedding_id);
CREATE INDEX idx_workflow_executions_wedding_id ON workflow_executions(wedding_id);
CREATE INDEX idx_workflow_executions_status ON workflow_executions(status);
CREATE INDEX idx_workflow_steps_execution_id ON workflow_steps(workflow_execution_id);
CREATE INDEX idx_workflow_steps_step_order ON workflow_steps(step_order);

-- RLS Policies
ALTER TABLE agent_workers ENABLE ROW LEVEL SECURITY;
ALTER TABLE worker_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE response_quality_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_coordination ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_satisfaction ENABLE ROW LEVEL SECURITY;
ALTER TABLE learning_model_versions ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE workflow_steps ENABLE ROW LEVEL SECURITY;

-- RLS Policies for agent_workers
CREATE POLICY "Service role can manage all agent workers" ON agent_workers
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for agent_feedback
CREATE POLICY "Users can view their own feedback" ON agent_feedback
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create their own feedback" ON agent_feedback
  FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Service role can manage all feedback" ON agent_feedback
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for user_satisfaction
CREATE POLICY "Users can view their own satisfaction data" ON user_satisfaction
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all satisfaction data" ON user_satisfaction
  FOR ALL USING (auth.role() = 'service_role');

-- RLS Policies for workflow_executions
CREATE POLICY "Users can view their own workflow executions" ON workflow_executions
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Service role can manage all workflow executions" ON workflow_executions
  FOR ALL USING (auth.role() = 'service_role');

-- Service role policies for other tables
CREATE POLICY "Service role can manage all worker dependencies" ON worker_dependencies
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage all response quality scores" ON response_quality_scores
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage all agent coordination" ON agent_coordination
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage all performance metrics" ON agent_performance_metrics
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage all learning model versions" ON learning_model_versions
  FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can manage all workflow steps" ON workflow_steps
  FOR ALL USING (auth.role() = 'service_role');

-- Update triggers
CREATE TRIGGER update_agent_workers_updated_at
  BEFORE UPDATE ON agent_workers
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agent_coordination_updated_at
  BEFORE UPDATE ON agent_coordination
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_satisfaction_updated_at
  BEFORE UPDATE ON user_satisfaction
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
