# VL Wedding Planner Agent System

A LangGraph-powered hierarchical agent system for intelligent wedding planning assistance with advanced learning, coordination, and analytics capabilities.

## Architecture

### Four-Tier Hierarchy

1. **CEO Orchestrator** - Routes user requests to appropriate domain specialists with advanced coordination
2. **Domain Managers** - Specialized agents for budget, vendor, guest, and timeline management
3. **Associate Workers** - Ephemeral task executors for complex multi-step operations
4. **Cross-Domain Coordinators** - Workflow orchestration across multiple domains

### Core Components

- **Multi-Model Fallback**: Resilient LLM access across OpenAI, Claude, and Gemini
- **Vector Memory**: Long-term memory storage using Supabase pgvector
- **Streaming API**: Real-time chat interface with CopilotKit integration
- **Domain Tools**: Specialized tools for wedding planning operations
- **Associate Workers**: Ephemeral task executors for complex analysis and research
- **Learning System**: Feedback collection and reinforcement learning for continuous improvement
- **Cross-Domain Coordination**: Workflow orchestration and inter-agent communication
- **Performance Analytics**: Comprehensive metrics, monitoring, and optimization recommendations

## Getting Started

### Prerequisites

- Bun runtime
- Supabase project with pgvector extension
- API keys for OpenAI, Anthropic, and/or Google AI

### Installation

```bash
# Install dependencies
bun install

# Build the agent system
bun run build

# Run tests
bun test
```

### Environment Variables

Create a `.env` file with the following variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Vendor Data (separate Supabase project)
NEXT_PUBLIC_SUPABASE_DATA_URL=your_data_supabase_url
NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY=your_data_anon_key

# AI Model API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GEMINI_API_KEY=your_gemini_key
```

### Database Setup

Run the migration script to set up the agent system tables:

```sql
-- Run the migration in apps/agent-system/migrations/001_agent_system_schema.sql
```

## Usage

### API Integration

The agent system exposes a streaming API endpoint at `/api/agent` that accepts:

```typescript
{
  message: string;
  weddingId: string;
  userId: string;
  threadId?: string;
}
```

### Example Request

```javascript
const response = await fetch("/api/agent", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    message: "What's my current budget status?",
    weddingId: "wedding-uuid",
    userId: "user-uuid",
  }),
})
```

### Domain Capabilities

#### Budget Manager

- Budget status tracking
- Expense categorization
- Budget recommendations
- Spending analysis

#### Vendor Manager

- Vendor search and filtering
- Availability checking
- Booking management
- Vendor recommendations

#### Guest Manager

- Guest list management
- RSVP tracking
- Invitation coordination
- Guest analytics

#### Timeline Manager

- Timeline generation
- Task management
- Milestone tracking
- Conflict detection

## Development

### Project Structure

```
src/
├── agents/          # Domain manager agents
├── tools/           # Domain-specific tools
├── memory/          # Memory management and Supabase client
├── models/          # Multi-model fallback system
├── tests/           # Test suites
└── orchestratorGraph.ts  # Main LangGraph assembly
```

### Testing

```bash
# Run all tests
bun test

# Run specific test suite
bun test src/tests/orchestrator.spec.ts

# Run with coverage
bun test --coverage
```

### Adding New Agents

1. Create agent file in `src/agents/`
2. Implement agent node function
3. Add tools in `src/tools/`
4. Update orchestrator graph
5. Add tests

### Adding New Tools

1. Create tool file in `src/tools/`
2. Define Zod schemas for validation
3. Implement tool functions
4. Add to agent imports
5. Write unit tests

## 🚀 Advanced Features

### Associate Workers

- **Budget Analyzer**: Deep budget analysis with projections and recommendations
- **Vendor Researcher**: Comprehensive vendor research and comparison
- **Guest Coordinator**: Advanced guest management and communication
- **Timeline Optimizer**: Intelligent scheduling and conflict resolution
- **Document Generator**: Automated contract and agreement generation
- **Data Aggregator**: Multi-source data collection and synthesis
- **Conflict Resolver**: Cross-domain conflict detection and resolution
- **Recommendation Engine**: Personalized suggestions based on preferences

### Learning System

- **Feedback Collection**: Multi-modal feedback (ratings, thumbs, text, corrections)
- **Quality Scoring**: Automated response quality assessment
- **Reinforcement Learning**: Continuous improvement from user interactions
- **Adaptive Responses**: Personalized responses based on user preferences
- **Performance Optimization**: Data-driven agent improvement recommendations

### Cross-Domain Coordination

- **Workflow Orchestration**: Complex multi-step workflows across domains
- **Inter-Agent Communication**: Shared state and message passing
- **Conflict Resolution**: Automated conflict detection and resolution
- **Priority Management**: Intelligent task prioritization and scheduling
- **Resource Coordination**: Optimal resource allocation across agents

### Performance Analytics

- **Real-Time Metrics**: Response time, success rate, user satisfaction tracking
- **Dashboard Analytics**: Comprehensive performance visualization
- **Optimization Recommendations**: AI-powered improvement suggestions
- **User Satisfaction Tracking**: Detailed satisfaction analysis and trends
- **Predictive Analytics**: Proactive issue detection and prevention

### API Endpoints

#### Feedback Collection

```bash
POST /api/agent/feedback
{
  "runId": "uuid",
  "userId": "uuid",
  "weddingId": "uuid",
  "feedbackType": "rating|thumbs|text|correction",
  "rating": 1-5,
  "thumbsDirection": "up|down",
  "feedbackText": "string",
  "correctionData": {}
}
```

#### Analytics Dashboard

```bash
GET /api/agent/analytics?type=dashboard&startDate=2024-01-01&endDate=2024-01-31
GET /api/agent/analytics?type=agent-metrics&agentType=budget&timePeriod=day
GET /api/agent/analytics?type=recommendations&agentType=budget
```

#### Workflow Orchestration

```bash
POST /api/agent/workflow
{
  "type": "workflow",
  "data": {
    "workflowName": "comprehensive_planning",
    "weddingId": "uuid",
    "userId": "uuid",
    "steps": [...]
  }
}
```

## Deployment

The agent system is designed to deploy alongside the main Next.js application:

1. Build the agent system: `bun run build`
2. Deploy with your preferred platform (Vercel, etc.)
3. Ensure environment variables are configured
4. Run database migrations

## Monitoring

The system includes comprehensive logging, event tracking, and analytics:

### Core Monitoring

- Agent execution logs in `agent_runs` table
- Event sourcing in `agent_events` table
- Memory operations tracking
- Error handling and fallback logging

### Advanced Analytics

- Worker execution tracking in `agent_workers` table
- User feedback collection in `agent_feedback` table
- Response quality scoring in `response_quality_scores` table
- Cross-domain coordination in `agent_coordination` table
- Performance metrics in `agent_performance_metrics` table
- User satisfaction tracking in `user_satisfaction` table
- Workflow execution monitoring in `workflow_executions` table

### Real-Time Dashboards

- System performance overview
- Agent-specific metrics and trends
- User satisfaction analytics
- Optimization recommendations
- Workflow execution status

## Contributing

1. Follow the existing code patterns
2. Add comprehensive tests for new features
3. Update documentation
4. Ensure TypeScript compliance
5. Test multi-model fallback scenarios

## License

This project is part of the VL Wedding Planner application.
