# Agent System Deployment Guide

This guide covers deploying the VL Wedding Planner Agent System to production.

## Pre-Deployment Checklist

### 1. Environment Setup

Ensure all required environment variables are configured:

```bash
# Required for production
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Vendor data (if using separate project)
NEXT_PUBLIC_SUPABASE_DATA_URL=https://your-data-project.supabase.co
NEXT_PUBLIC_SUPABASE_DATA_ANON_KEY=your_data_anon_key

# AI Model Keys (at least one required)
OPENAI_API_KEY=sk-...
ANTHROPIC_API_KEY=sk-ant-...
GEMINI_API_KEY=AI...
```

### 2. Database Migration

Run the agent system migration in your Supabase project:

```sql
-- Execute the contents of migrations/001_agent_system_schema.sql
-- This creates the agent_runs, agent_events, and agent_memory tables
-- Plus the pgvector extension and search functions
```

### 3. Build Verification

Ensure the project builds successfully:

```bash
# From project root
bun run build

# Should output:
# agent-system build: Exited with code 0
# web build: ✓ Compiled successfully
```

## Deployment Platforms

### Vercel (Recommended)

1. **Connect Repository**
   ```bash
   # Push to GitHub
   git add .
   git commit -m "Add agent system"
   git push origin main
   ```

2. **Configure Build Settings**
   - Build Command: `bun run build`
   - Output Directory: `apps/web/.next`
   - Install Command: `bun install`

3. **Environment Variables**
   Add all required environment variables in Vercel dashboard.

4. **Deploy**
   Vercel will automatically deploy on push to main branch.

### Railway

1. **Connect Repository**
   - Link your GitHub repository
   - Select the root directory

2. **Configure Build**
   ```bash
   # Build command
   bun run build
   
   # Start command
   bun --filter web start
   ```

3. **Environment Variables**
   Add all required environment variables in Railway dashboard.

### Docker Deployment

1. **Create Dockerfile**
   ```dockerfile
   FROM oven/bun:1 as base
   WORKDIR /app
   
   # Install dependencies
   COPY package.json bun.lockb ./
   COPY apps/web/package.json ./apps/web/
   COPY apps/agent-system/package.json ./apps/agent-system/
   RUN bun install --frozen-lockfile
   
   # Copy source code
   COPY . .
   
   # Build the application
   RUN bun run build
   
   # Expose port
   EXPOSE 3000
   
   # Start the application
   CMD ["bun", "--filter", "web", "start"]
   ```

2. **Build and Run**
   ```bash
   docker build -t vl-wedding-planner .
   docker run -p 3000:3000 --env-file .env vl-wedding-planner
   ```

## Post-Deployment Verification

### 1. Health Checks

Test the agent API endpoint:

```bash
curl -X POST https://your-domain.com/api/agent \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello",
    "weddingId": "550e8400-e29b-41d4-a716-446655440000",
    "userId": "550e8400-e29b-41d4-a716-446655440001"
  }'
```

### 2. Database Connectivity

Verify Supabase connection:
- Check agent_runs table exists
- Verify RLS policies are active
- Test vector search function

### 3. AI Model Fallback

Test each configured AI provider:
- OpenAI (primary)
- Anthropic (secondary)
- Google AI (tertiary)

### 4. Memory System

Verify vector embeddings:
- Test memory storage
- Test similarity search
- Check embedding generation

## Monitoring and Logging

### Application Monitoring

1. **Vercel Analytics** (if using Vercel)
   - Enable in project settings
   - Monitor response times and errors

2. **Custom Logging**
   ```typescript
   // Agent execution logging is built-in
   // Check agent_runs and agent_events tables
   ```

### Error Tracking

1. **Sentry Integration** (optional)
   ```bash
   bun add @sentry/nextjs
   ```

2. **Supabase Logs**
   - Monitor database performance
   - Check RLS policy violations
   - Review API usage

### Performance Monitoring

1. **Response Times**
   - Agent API should respond < 2s for simple queries
   - Complex operations may take longer

2. **Memory Usage**
   - Monitor vector storage growth
   - Implement cleanup for old memories

3. **AI API Usage**
   - Track token consumption
   - Monitor rate limits
   - Review fallback frequency

## Scaling Considerations

### Database Scaling

1. **Connection Pooling**
   - Supabase handles this automatically
   - Monitor connection usage

2. **Vector Index Optimization**
   ```sql
   -- Monitor and optimize vector searches
   EXPLAIN ANALYZE SELECT * FROM search_agent_memory(...);
   ```

### AI Model Scaling

1. **Rate Limiting**
   - Implement user-based rate limiting
   - Add request queuing for high traffic

2. **Caching**
   - Cache common responses
   - Implement memory-based caching

### Edge Deployment

1. **Vercel Edge Functions**
   - Agent API runs on Edge Runtime
   - Automatic global distribution

2. **CDN Configuration**
   - Static assets cached globally
   - API responses not cached

## Security Considerations

### API Security

1. **Authentication**
   - Verify user authentication in API routes
   - Validate wedding ownership

2. **Input Validation**
   - All inputs validated with Zod schemas
   - Sanitize user messages

3. **Rate Limiting**
   ```typescript
   // Implement rate limiting per user
   const rateLimiter = new Map();
   ```

### Database Security

1. **RLS Policies**
   - All tables have proper RLS
   - Service role bypasses for agents

2. **API Keys**
   - Store in environment variables
   - Rotate regularly

### AI Model Security

1. **Prompt Injection Protection**
   - Validate and sanitize inputs
   - Use structured prompts

2. **Content Filtering**
   - Monitor AI responses
   - Implement content policies

## Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clear cache and rebuild
   rm -rf node_modules .next
   bun install
   bun run build
   ```

2. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check network connectivity
   - Review RLS policies

3. **AI Model Failures**
   - Check API key validity
   - Verify model availability
   - Review fallback logic

### Debug Mode

Enable debug logging:

```bash
# Set environment variable
DEBUG=agent-system:*
```

### Support

For deployment issues:
1. Check application logs
2. Review Supabase logs
3. Verify environment variables
4. Test individual components

## Rollback Procedure

If deployment issues occur:

1. **Immediate Rollback**
   ```bash
   # Revert to previous deployment
   git revert HEAD
   git push origin main
   ```

2. **Database Rollback**
   ```sql
   -- If needed, drop agent tables
   DROP TABLE IF EXISTS agent_memory;
   DROP TABLE IF EXISTS agent_events;
   DROP TABLE IF EXISTS agent_runs;
   ```

3. **Environment Cleanup**
   - Remove new environment variables
   - Restore previous configuration

## Maintenance

### Regular Tasks

1. **Memory Cleanup**
   ```sql
   -- Clean old memories (monthly)
   DELETE FROM agent_memory 
   WHERE created_at < NOW() - INTERVAL '30 days';
   ```

2. **Log Rotation**
   ```sql
   -- Archive old agent runs (quarterly)
   DELETE FROM agent_runs 
   WHERE created_at < NOW() - INTERVAL '90 days';
   ```

3. **Performance Review**
   - Monitor response times
   - Review AI usage costs
   - Optimize slow queries

### Updates

1. **Dependency Updates**
   ```bash
   bun update
   bun test
   bun run build
   ```

2. **AI Model Updates**
   - Test new model versions
   - Update fallback configurations
   - Monitor performance changes
